import type { Config } from "tailwindcss";

const config: Config = {
  content: [
    "./src/pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/components/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/app/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
    extend: {
      colors: {
        background: "var(--background)",
        foreground: "var(--foreground)",

        // Enhanced Brand Colors
        "brand-white": "var(--color-white)",
        "brand-dark-green": "var(--color-dark-green)",
        "brand-pine-green": "var(--color-pine-green)",
        "brand-light-green": "var(--color-light-green)",
        "brand-sage-green": "var(--color-sage-green)",
        "brand-forest-green": "var(--color-forest-green)",
        "brand-mint": "var(--color-mint)",
        "brand-cream": "var(--color-cream)",
        "brand-charcoal": "var(--color-charcoal)",
        "brand-soft-gray": "var(--color-soft-gray)",
        "brand-warm-gray": "var(--color-warm-gray)",
        "brand-black": "var(--color-black)",

        // shadcn/ui semantic colors
        primary: {
          DEFAULT: "var(--primary)",
          foreground: "var(--primary-foreground)",
        },
        secondary: {
          DEFAULT: "var(--secondary)",
          foreground: "var(--secondary-foreground)",
        },
        destructive: {
          DEFAULT: "var(--destructive)",
          foreground: "var(--destructive-foreground)",
        },
        muted: {
          DEFAULT: "var(--muted)",
          foreground: "var(--muted-foreground)",
        },
        accent: {
          DEFAULT: "var(--accent)",
          foreground: "var(--accent-foreground)",
        },
        popover: {
          DEFAULT: "var(--popover)",
          foreground: "var(--popover-foreground)",
        },
        card: {
          DEFAULT: "var(--card)",
          foreground: "var(--card-foreground)",
        },
        border: "var(--border)",
        input: "var(--input)",
        ring: "var(--ring)",
      },
      fontFamily: {
        sans: ["var(--font-inter)", "var(--font-geist-sans)", "Arial", "Helvetica", "sans-serif"],
        mono: ["var(--font-geist-mono)", "monospace"],
        inter: ["var(--font-inter)", "sans-serif"],
        heading: ["var(--font-inter)", "sans-serif"],
        subheading: ["var(--font-inter)", "sans-serif"],
        body: ["var(--font-inter)", "sans-serif"],
      },
      backgroundImage: {
        "gradient-primary": "linear-gradient(135deg, var(--color-dark-green) 0%, var(--color-pine-green) 100%)",
        "gradient-secondary": "linear-gradient(135deg, var(--color-pine-green) 0%, var(--color-dark-green) 100%)",
        "gradient-hero": "linear-gradient(135deg, var(--color-dark-green) 0%, var(--color-pine-green) 50%, var(--color-forest-green) 100%)",
        "gradient-soft": "linear-gradient(135deg, var(--color-light-green) 0%, var(--color-sage-green) 100%)",
        "gradient-overlay": "linear-gradient(135deg, rgba(3, 34, 33, 0.95) 0%, rgba(1, 121, 111, 0.9) 100%)",
        "gradient-card": "linear-gradient(145deg, var(--color-white) 0%, var(--color-light-green) 100%)",
        "gradient-mesh": "radial-gradient(circle at 20% 50%, rgba(1, 121, 111, 0.1) 0%, transparent 50%), radial-gradient(circle at 80% 20%, rgba(3, 34, 33, 0.1) 0%, transparent 50%), radial-gradient(circle at 40% 80%, rgba(168, 213, 186, 0.1) 0%, transparent 50%)",
      },
      boxShadow: {
        "soft": "0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24)",
        "medium": "0 3px 6px rgba(0, 0, 0, 0.16), 0 3px 6px rgba(0, 0, 0, 0.23)",
        "strong": "0 10px 20px rgba(0, 0, 0, 0.19), 0 6px 6px rgba(0, 0, 0, 0.23)",
        "colored": "0 4px 15px rgba(1, 121, 111, 0.2), 0 2px 4px rgba(1, 121, 111, 0.1)",
        "glow": "0 0 20px rgba(1, 121, 111, 0.3), 0 0 40px rgba(1, 121, 111, 0.1)",
        "inner": "inset 0 2px 4px rgba(0, 0, 0, 0.06)",
        "elevated": "0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)",
      },
      animation: {
        "fade-in-up": "fadeInUp 0.6s ease-out forwards",
        "fade-in-down": "fadeInDown 0.6s ease-out forwards",
        "fade-in-left": "fadeInLeft 0.6s ease-out forwards",
        "fade-in-right": "fadeInRight 0.6s ease-out forwards",
        "scale-in": "scaleIn 0.4s ease-out forwards",
        "float": "float 6s ease-in-out infinite",
        "pulse-slow": "pulse 3s ease-in-out infinite",
        "shimmer": "shimmer 2s infinite",
      },
      keyframes: {
        fadeInUp: {
          "0%": {
            opacity: "0",
            transform: "translateY(30px)",
          },
          "100%": {
            opacity: "1",
            transform: "translateY(0)",
          },
        },
        fadeInDown: {
          "0%": {
            opacity: "0",
            transform: "translateY(-30px)",
          },
          "100%": {
            opacity: "1",
            transform: "translateY(0)",
          },
        },
        fadeInLeft: {
          "0%": {
            opacity: "0",
            transform: "translateX(-30px)",
          },
          "100%": {
            opacity: "1",
            transform: "translateX(0)",
          },
        },
        fadeInRight: {
          "0%": {
            opacity: "0",
            transform: "translateX(30px)",
          },
          "100%": {
            opacity: "1",
            transform: "translateX(0)",
          },
        },
        scaleIn: {
          "0%": {
            opacity: "0",
            transform: "scale(0.9)",
          },
          "100%": {
            opacity: "1",
            transform: "scale(1)",
          },
        },
        float: {
          "0%, 100%": {
            transform: "translateY(0px)",
          },
          "50%": {
            transform: "translateY(-10px)",
          },
        },
        shimmer: {
          "0%": {
            backgroundPosition: "-200% 0",
          },
          "100%": {
            backgroundPosition: "200% 0",
          },
        },
      },
    },
  },
  plugins: [],
};

export default config;
