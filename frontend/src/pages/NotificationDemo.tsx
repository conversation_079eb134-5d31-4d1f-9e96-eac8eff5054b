import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { 
  Bell, 
  Send, 
  TestTube, 
  Zap,
  Gift,
  CreditCard,
  MessageSquare,
  Settings,
  Shield,
  Megaphone,
  Calendar,
  UserPlus
} from 'lucide-react';
import {
  NotificationProvider,
  NotificationBell,
  NotificationToastContainer,
  useNotifications,
  type BaseNotification
} from '@/components/notifications';

// Demo notification data
const demoNotifications: Omit<BaseNotification, '_id' | 'createdAt' | 'updatedAt'>[] = [
  {
    category: 'payment',
    type: 'sms',
    title: 'Payment Confirmed',
    message: 'Your payment of KES 500 has been successfully processed.',
    priority: 'high',
    status: 'delivered',
    isRead: false,
    relatedEntity: { type: 'payment', id: 'pay_123' },
  },
  {
    category: 'exchange',
    type: 'sms',
    title: 'New Exchange Request',
    message: 'Sarah wants to exchange her blue denim jacket for your red sweater.',
    priority: 'medium',
    status: 'delivered',
    isRead: false,
    relatedEntity: { type: 'exchange', id: 'ex_456' },
  },
  {
    category: 'token',
    type: 'sms',
    title: 'Tokens Earned!',
    message: 'You earned 25 Pedi tokens for completing your profile.',
    priority: 'medium',
    status: 'delivered',
    isRead: true,
    relatedEntity: { type: 'token', id: 'tok_789' },
  },
  {
    category: 'system',
    type: 'sms',
    title: 'Maintenance Notice',
    message: 'Scheduled maintenance tonight from 2-4 AM EAT.',
    priority: 'low',
    status: 'delivered',
    isRead: false,
  },
  {
    category: 'promotion',
    type: 'sms',
    title: 'Weekend Special',
    message: 'Get 20% extra tokens on all exchanges this weekend!',
    priority: 'medium',
    status: 'delivered',
    isRead: false,
  },
];

const NotificationDemoContent: React.FC = () => {
  const { addNotification, notifications, unreadCount } = useNotifications();
  const [customNotification, setCustomNotification] = useState({
    category: 'system',
    type: 'sms',
    title: '',
    message: '',
    priority: 'medium',
  });

  const generateId = () => `demo_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

  const sendDemoNotification = (demo: typeof demoNotifications[0]) => {
    const notification: BaseNotification = {
      ...demo,
      _id: generateId(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };
    addNotification(notification);
  };

  const sendCustomNotification = () => {
    if (!customNotification.title || !customNotification.message) {
      alert('Please fill in title and message');
      return;
    }

    const notification: BaseNotification = {
      ...customNotification,
      _id: generateId(),
      status: 'delivered' as const,
      isRead: false,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    addNotification(notification);
    
    // Reset form
    setCustomNotification({
      ...customNotification,
      title: '',
      message: '',
    });
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'payment': return <CreditCard className="h-4 w-4" />;
      case 'exchange': return <MessageSquare className="h-4 w-4" />;
      case 'token': return <Gift className="h-4 w-4" />;
      case 'system': return <Settings className="h-4 w-4" />;
      case 'security': return <Shield className="h-4 w-4" />;
      case 'promotion': return <Megaphone className="h-4 w-4" />;
      case 'reminder': return <Calendar className="h-4 w-4" />;
      case 'welcome': return <UserPlus className="h-4 w-4" />;
      default: return <Bell className="h-4 w-4" />;
    }
  };

  const handleNotificationAction = (notification: BaseNotification) => {
    console.log('Notification action clicked:', notification);
    alert(`Navigating to ${notification.relatedEntity?.type}: ${notification.relatedEntity?.id}`);
  };

  return (
    <div className="container mx-auto px-4 py-8 max-w-6xl">
      <div className="mb-8">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h1 className="text-3xl font-bold mb-2">Notification System Demo</h1>
            <p className="text-gray-600">
              Test and preview the Pedi notification system components
            </p>
          </div>
          
          {/* Notification Bell in Header */}
          <div className="flex items-center gap-4">
            <div className="text-sm text-gray-600">
              {notifications.length} total, {unreadCount} unread
            </div>
            <NotificationBell
              onNotificationClick={handleNotificationAction}
              onViewAllClick={() => alert('Navigate to /notifications')}
              onSettingsClick={() => alert('Navigate to notification settings')}
            />
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Demo Notifications */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TestTube className="h-5 w-5" />
              Demo Notifications
            </CardTitle>
            <CardDescription>
              Click to send pre-built notification examples
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-3">
            {demoNotifications.map((demo, index) => (
              <div
                key={index}
                className="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50"
              >
                <div className="flex items-center gap-3">
                  {getCategoryIcon(demo.category)}
                  <div>
                    <div className="font-medium text-sm">{demo.title}</div>
                    <div className="text-xs text-gray-500 line-clamp-1">
                      {demo.message}
                    </div>
                  </div>
                </div>
                
                <div className="flex items-center gap-2">
                  <Badge variant="outline" className="text-xs capitalize">
                    {demo.category}
                  </Badge>
                  <Badge 
                    variant={demo.priority === 'high' ? 'destructive' : 'secondary'}
                    className="text-xs capitalize"
                  >
                    {demo.priority}
                  </Badge>
                  <Button
                    size="sm"
                    onClick={() => sendDemoNotification(demo)}
                  >
                    <Send className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            ))}
          </CardContent>
        </Card>

        {/* Custom Notification */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Zap className="h-5 w-5" />
              Custom Notification
            </CardTitle>
            <CardDescription>
              Create and send a custom notification
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="category">Category</Label>
                <Select
                  value={customNotification.category}
                  onValueChange={(value) => 
                    setCustomNotification(prev => ({ ...prev, category: value }))
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="payment">Payment</SelectItem>
                    <SelectItem value="exchange">Exchange</SelectItem>
                    <SelectItem value="token">Token</SelectItem>
                    <SelectItem value="system">System</SelectItem>
                    <SelectItem value="security">Security</SelectItem>
                    <SelectItem value="promotion">Promotion</SelectItem>
                    <SelectItem value="reminder">Reminder</SelectItem>
                    <SelectItem value="welcome">Welcome</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="priority">Priority</Label>
                <Select
                  value={customNotification.priority}
                  onValueChange={(value) => 
                    setCustomNotification(prev => ({ ...prev, priority: value }))
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="low">Low</SelectItem>
                    <SelectItem value="medium">Medium</SelectItem>
                    <SelectItem value="high">High</SelectItem>
                    <SelectItem value="urgent">Urgent</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div>
              <Label htmlFor="title">Title</Label>
              <Input
                id="title"
                value={customNotification.title}
                onChange={(e) => 
                  setCustomNotification(prev => ({ ...prev, title: e.target.value }))
                }
                placeholder="Enter notification title"
              />
            </div>

            <div>
              <Label htmlFor="message">Message</Label>
              <Textarea
                id="message"
                value={customNotification.message}
                onChange={(e) => 
                  setCustomNotification(prev => ({ ...prev, message: e.target.value }))
                }
                placeholder="Enter notification message"
                rows={3}
              />
            </div>

            <Button 
              onClick={sendCustomNotification}
              className="w-full"
              disabled={!customNotification.title || !customNotification.message}
            >
              <Send className="h-4 w-4 mr-2" />
              Send Custom Notification
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* Current Notifications Display */}
      <Card className="mt-8">
        <CardHeader>
          <CardTitle>Current Notifications ({notifications.length})</CardTitle>
          <CardDescription>
            Live view of notifications in the system
          </CardDescription>
        </CardHeader>
        <CardContent>
          {notifications.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              No notifications yet. Send some demo notifications above!
            </div>
          ) : (
            <div className="space-y-2 max-h-64 overflow-y-auto">
              {notifications.slice(0, 10).map((notification) => (
                <div
                  key={notification._id}
                  className={`p-3 border rounded-lg ${
                    !notification.isRead ? 'bg-blue-50 border-blue-200' : 'bg-gray-50'
                  }`}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      {getCategoryIcon(notification.category)}
                      <span className="font-medium text-sm">{notification.title}</span>
                      {!notification.isRead && (
                        <Badge variant="destructive" className="text-xs">New</Badge>
                      )}
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge variant="outline" className="text-xs capitalize">
                        {notification.category}
                      </Badge>
                      <Badge 
                        variant={notification.priority === 'high' || notification.priority === 'urgent' ? 'destructive' : 'secondary'}
                        className="text-xs capitalize"
                      >
                        {notification.priority}
                      </Badge>
                    </div>
                  </div>
                  <p className="text-sm text-gray-600 mt-1">{notification.message}</p>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Toast Container */}
      <NotificationToastContainer
        maxToasts={3}
        position="top-right"
        autoClose={true}
        autoCloseDelay={5000}
        onNotificationAction={handleNotificationAction}
      />
    </div>
  );
};

const NotificationDemo: React.FC = () => {
  return (
    <NotificationProvider autoRefresh={false}>
      <NotificationDemoContent />
    </NotificationProvider>
  );
};

export default NotificationDemo;
