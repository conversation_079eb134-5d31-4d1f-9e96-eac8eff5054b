import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  PaymentForm, 
  PaymentStatus, 
  PaymentHistory, 
  PaymentMethodManager, 
  PaymentIntegration 
} from '@/components/payments';
import { 
  CreditCard, 
  History, 
  Settings, 
  Zap,
  CheckCircle,
  Info
} from 'lucide-react';

export const PaymentDemo: React.FC = () => {
  const [activePaymentId, setActivePaymentId] = useState<string>('');
  const [showSuccess, setShowSuccess] = useState<boolean>(false);
  const [successMessage, setSuccessMessage] = useState<string>('');

  const handlePaymentSuccess = (paymentId: string, type?: string) => {
    setActivePaymentId('');
    setShowSuccess(true);
    setSuccessMessage(`Payment ${paymentId} completed successfully! ${type ? `(${type})` : ''}`);
    
    // Hide success message after 5 seconds
    setTimeout(() => {
      setShowSuccess(false);
      setSuccessMessage('');
    }, 5000);
  };

  const handlePaymentError = (error: string) => {
    console.error('Payment error:', error);
    setActivePaymentId('');
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold">Payment System Demo</h1>
        <p className="text-gray-600">
          Comprehensive M-Pesa payment integration for the Pedi platform
        </p>
      </div>

      {/* Success Message */}
      {showSuccess && (
        <Alert className="border-green-200 bg-green-50">
          <CheckCircle className="h-4 w-4 text-green-600" />
          <AlertDescription className="text-green-800">
            {successMessage}
          </AlertDescription>
        </Alert>
      )}

      {/* Payment Status Display */}
      {activePaymentId && (
        <Card>
          <CardHeader>
            <CardTitle>Payment in Progress</CardTitle>
            <CardDescription>
              Monitoring payment status for: {activePaymentId}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <PaymentStatus
              paymentId={activePaymentId}
              onStatusChange={(status) => {
                if (status === 'completed') {
                  handlePaymentSuccess(activePaymentId);
                } else if (status === 'failed' || status === 'cancelled') {
                  setActivePaymentId('');
                }
              }}
              autoRefresh={true}
            />
          </CardContent>
        </Card>
      )}

      {/* Main Payment Tabs */}
      {!activePaymentId && (
        <Tabs defaultValue="integration" className="w-full">
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="integration" className="flex items-center gap-2">
              <Zap className="h-4 w-4" />
              Integration
            </TabsTrigger>
            <TabsTrigger value="simple-payment" className="flex items-center gap-2">
              <CreditCard className="h-4 w-4" />
              Simple Payment
            </TabsTrigger>
            <TabsTrigger value="history" className="flex items-center gap-2">
              <History className="h-4 w-4" />
              History
            </TabsTrigger>
            <TabsTrigger value="methods" className="flex items-center gap-2">
              <Settings className="h-4 w-4" />
              Methods
            </TabsTrigger>
            <TabsTrigger value="info" className="flex items-center gap-2">
              <Info className="h-4 w-4" />
              Info
            </TabsTrigger>
          </TabsList>

          {/* Payment Integration Tab */}
          <TabsContent value="integration">
            <Card>
              <CardHeader>
                <CardTitle>Payment Integration</CardTitle>
                <CardDescription>
                  Comprehensive payment features including token top-up, platform fees, and delivery payments
                </CardDescription>
              </CardHeader>
              <CardContent>
                <PaymentIntegration
                  transactionId="demo-transaction-id"
                  onPaymentSuccess={(paymentId, type) => handlePaymentSuccess(paymentId, type)}
                />
              </CardContent>
            </Card>
          </TabsContent>

          {/* Simple Payment Tab */}
          <TabsContent value="simple-payment">
            <Card>
              <CardHeader>
                <CardTitle>Simple Payment Form</CardTitle>
                <CardDescription>
                  Basic payment form for custom amounts and descriptions
                </CardDescription>
              </CardHeader>
              <CardContent>
                <PaymentForm
                  amount={500}
                  description="Demo payment - Custom transaction"
                  onPaymentSuccess={(paymentId) => handlePaymentSuccess(paymentId, 'custom')}
                  onPaymentError={handlePaymentError}
                  relatedEntity={{
                    type: 'premium_feature',
                    id: 'demo-feature-id',
                  }}
                />
              </CardContent>
            </Card>
          </TabsContent>

          {/* Payment History Tab */}
          <TabsContent value="history">
            <PaymentHistory
              onPaymentClick={(payment) => {
                console.log('Payment clicked:', payment);
                // Could navigate to payment details or show modal
              }}
            />
          </TabsContent>

          {/* Payment Methods Tab */}
          <TabsContent value="methods">
            <PaymentMethodManager
              onMethodSelect={(method) => {
                console.log('Payment method selected:', method);
                // Could use this for quick payments
              }}
            />
          </TabsContent>

          {/* Info Tab */}
          <TabsContent value="info">
            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Payment System Features</CardTitle>
                  <CardDescription>
                    Overview of the comprehensive M-Pesa payment integration
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <h3 className="font-semibold">Core Features</h3>
                      <ul className="text-sm space-y-1 text-gray-600">
                        <li>• M-Pesa STK Push integration</li>
                        <li>• Real-time payment status tracking</li>
                        <li>• Payment method management</li>
                        <li>• Comprehensive payment history</li>
                        <li>• Automatic callback processing</li>
                      </ul>
                    </div>
                    <div className="space-y-2">
                      <h3 className="font-semibold">Platform Integration</h3>
                      <ul className="text-sm space-y-1 text-gray-600">
                        <li>• Token purchase payments</li>
                        <li>• Platform fee collection</li>
                        <li>• Delivery fee processing</li>
                        <li>• Premium feature payments</li>
                        <li>• Automatic token awarding</li>
                      </ul>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Technical Implementation</CardTitle>
                  <CardDescription>
                    Backend services and API endpoints
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <h3 className="font-semibold">Backend Services</h3>
                      <ul className="text-sm space-y-1 text-gray-600">
                        <li>• M-Pesa Daraja API integration</li>
                        <li>• Payment lifecycle management</li>
                        <li>• Webhook callback processing</li>
                        <li>• Payment method verification</li>
                        <li>• Transaction integration</li>
                      </ul>
                    </div>
                    <div className="space-y-2">
                      <h3 className="font-semibold">API Endpoints</h3>
                      <ul className="text-sm space-y-1 text-gray-600">
                        <li>• /api/payments - Payment CRUD</li>
                        <li>• /api/payment-methods - Method management</li>
                        <li>• /api/payment-integration - Platform integration</li>
                        <li>• Callback handling and status updates</li>
                        <li>• Real-time payment monitoring</li>
                      </ul>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Usage Examples</CardTitle>
                  <CardDescription>
                    How to integrate payment components in your application
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <pre className="text-sm overflow-x-auto">
{`// Basic payment form
<PaymentForm
  amount={1000}
  description="Token purchase"
  onPaymentSuccess={(paymentId) => console.log('Success:', paymentId)}
  onPaymentError={(error) => console.log('Error:', error)}
/>

// Payment integration with platform features
<PaymentIntegration
  transactionId="transaction-123"
  showTokenTopUp={true}
  showPlatformFee={true}
  onPaymentSuccess={(paymentId, type) => handleSuccess(paymentId, type)}
/>

// Payment status monitoring
<PaymentStatus
  paymentId="payment-123"
  autoRefresh={true}
  onStatusChange={(status) => handleStatusChange(status)}
/>`}
                    </pre>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      )}
    </div>
  );
};
