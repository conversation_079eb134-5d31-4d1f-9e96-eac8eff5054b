import { useEffect, useRef, useCallback } from 'react';
import { useAuth } from './useAuth';
import socketService from '@/services/socket';

interface UseSocketOptions {
  autoConnect?: boolean;
  onConnect?: () => void;
  onDisconnect?: () => void;
  onError?: (error: Error) => void;
}

export const useSocket = (options: UseSocketOptions = {}) => {
  const { user, token } = useAuth();
  const { autoConnect = true, onConnect, onDisconnect, onError } = options;
  const isConnecting = useRef(false);

  const connect = useCallback(async () => {
    if (!token || !user || isConnecting.current || socketService.isConnected()) {
      return;
    }

    isConnecting.current = true;
    try {
      await socketService.connect(token);
      onConnect?.();
    } catch (error) {
      console.error('Failed to connect to socket:', error);
      onError?.(error as Error);
    } finally {
      isConnecting.current = false;
    }
  }, [token, user, onConnect, onError]);

  const disconnect = useCallback(() => {
    socketService.disconnect();
    onDisconnect?.();
  }, [onDisconnect]);

  useEffect(() => {
    if (autoConnect && user && token) {
      connect();
    }

    return () => {
      if (autoConnect) {
        disconnect();
      }
    };
  }, [autoConnect, user, token, connect, disconnect]);

  // Set up connection event listeners
  useEffect(() => {
    const handleConnect = () => {
      console.log('Socket connected');
      onConnect?.();
    };

    const handleDisconnect = () => {
      console.log('Socket disconnected');
      onDisconnect?.();
    };

    const handleError = (error: Error) => {
      console.error('Socket error:', error);
      onError?.(error);
    };

    socketService.on('connect', handleConnect);
    socketService.on('disconnect', handleDisconnect);
    socketService.on('connect_error', handleError);

    return () => {
      socketService.off('connect', handleConnect);
      socketService.off('disconnect', handleDisconnect);
      socketService.off('connect_error', handleError);
    };
  }, [onConnect, onDisconnect, onError]);

  return {
    socket: socketService,
    isConnected: socketService.isConnected(),
    connect,
    disconnect,
  };
};

// Hook for chat-specific socket operations
export const useChatSocket = (chatId?: string) => {
  const { socket } = useSocket();

  const joinChat = useCallback((id: string) => {
    socket.joinChat(id);
  }, [socket]);

  const leaveChat = useCallback((id: string) => {
    socket.leaveChat(id);
  }, [socket]);

  const sendTypingStart = useCallback(() => {
    if (chatId) {
      socket.sendTypingStart(chatId);
    }
  }, [socket, chatId]);

  const sendTypingStop = useCallback(() => {
    if (chatId) {
      socket.sendTypingStop(chatId);
    }
  }, [socket, chatId]);

  const markMessageDelivered = useCallback((messageId: string) => {
    if (chatId) {
      socket.markMessageDelivered(chatId, messageId);
    }
  }, [socket, chatId]);

  // Auto join/leave chat when chatId changes
  useEffect(() => {
    if (chatId && socket.isConnected()) {
      joinChat(chatId);
      return () => {
        leaveChat(chatId);
      };
    }
  }, [chatId, socket, joinChat, leaveChat]);

  return {
    socket,
    joinChat,
    leaveChat,
    sendTypingStart,
    sendTypingStop,
    markMessageDelivered,
  };
};

// Hook for managing online users
export const useOnlineUsers = () => {
  const { socket } = useSocket();

  const getOnlineUsers = useCallback(() => {
    socket.getOnlineUsers();
  }, [socket]);

  const updatePresence = useCallback(() => {
    socket.updatePresence();
  }, [socket]);

  // Update presence every 30 seconds
  useEffect(() => {
    if (socket.isConnected()) {
      const interval = setInterval(updatePresence, 30000);
      return () => clearInterval(interval);
    }
  }, [socket, updatePresence]);

  return {
    socket,
    getOnlineUsers,
    updatePresence,
  };
};
