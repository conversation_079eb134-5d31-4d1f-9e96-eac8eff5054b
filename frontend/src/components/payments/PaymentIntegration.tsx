import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Tabs, Ta<PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { 
  Loader2, 
  Coins, 
  CreditCard, 
  Truck, 
  Star,
  Calculator,
  AlertCircle,
  CheckCircle,
  Info
} from 'lucide-react';
import { PaymentForm } from './PaymentForm';
import { PaymentStatus } from './PaymentStatus';

interface ConversionRates {
  tokenToKes: number;
  kesToToken: number;
  platformFeePercentage: number;
}

interface PlatformFeeCalculation {
  tokenAmount: number;
  tokenToKesRate: number;
  platformFee: number;
  totalCost: number;
}

interface PaymentIntegrationProps {
  transactionId?: string;
  showTokenTopUp?: boolean;
  showPlatformFee?: boolean;
  showDeliveryFee?: boolean;
  onPaymentSuccess?: (paymentId: string, type: string) => void;
}

export const PaymentIntegration: React.FC<PaymentIntegrationProps> = ({
  transactionId,
  showTokenTopUp = true,
  showPlatformFee = true,
  showDeliveryFee = true,
  onPaymentSuccess,
}) => {
  const [rates, setRates] = useState<ConversionRates | null>(null);
  const [isLoadingRates, setIsLoadingRates] = useState(true);
  const [activePaymentId, setActivePaymentId] = useState<string>('');
  const [error, setError] = useState<string>('');

  // Token top-up state
  const [tokenAmount, setTokenAmount] = useState<number>(100);
  const [cashAmount, setCashAmount] = useState<number>(1000);

  // Platform fee state
  const [platformFeeCalc, setPlatformFeeCalc] = useState<PlatformFeeCalculation | null>(null);
  const [feeTokenAmount, setFeeTokenAmount] = useState<number>(50);

  // Delivery fee state
  const [deliveryFee, setDeliveryFee] = useState<number>(200);

  useEffect(() => {
    fetchConversionRates();
  }, []);

  useEffect(() => {
    if (rates) {
      setCashAmount(Math.round(tokenAmount / rates.kesToToken));
    }
  }, [tokenAmount, rates]);

  useEffect(() => {
    if (rates && feeTokenAmount > 0) {
      calculatePlatformFee();
    }
  }, [feeTokenAmount, rates]);

  const fetchConversionRates = async () => {
    try {
      setIsLoadingRates(true);
      const response = await fetch('/api/payment-integration/rates', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
      });

      if (!response.ok) {
        throw new Error('Failed to fetch conversion rates');
      }

      const data = await response.json();
      setRates(data.data);
    } catch (error: any) {
      console.error('Failed to fetch rates:', error);
      setError('Failed to load conversion rates');
    } finally {
      setIsLoadingRates(false);
    }
  };

  const calculatePlatformFee = async () => {
    try {
      const response = await fetch('/api/payment-integration/calculate-platform-fee', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
        body: JSON.stringify({ tokenAmount: feeTokenAmount }),
      });

      if (!response.ok) {
        throw new Error('Failed to calculate platform fee');
      }

      const data = await response.json();
      setPlatformFeeCalc(data.data);
    } catch (error: any) {
      console.error('Failed to calculate platform fee:', error);
    }
  };

  const handleTokenTopUp = async () => {
    try {
      const response = await fetch('/api/payment-integration/token-topup', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
        body: JSON.stringify({
          tokenAmount,
          cashAmount,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to initiate token top-up');
      }

      const data = await response.json();
      setActivePaymentId(data.data.paymentId);
    } catch (error: any) {
      setError(error.message);
    }
  };

  const handlePlatformFeePayment = async () => {
    if (!transactionId || !platformFeeCalc) return;

    try {
      const response = await fetch('/api/payment-integration/platform-fee', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
        body: JSON.stringify({
          transactionId,
          feeAmount: platformFeeCalc.platformFee,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to initiate platform fee payment');
      }

      const data = await response.json();
      setActivePaymentId(data.data.paymentId);
    } catch (error: any) {
      setError(error.message);
    }
  };

  const handleDeliveryFeePayment = async () => {
    if (!transactionId) return;

    try {
      const response = await fetch('/api/payment-integration/delivery-fee', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
        body: JSON.stringify({
          transactionId,
          deliveryFee,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to initiate delivery fee payment');
      }

      const data = await response.json();
      setActivePaymentId(data.data.paymentId);
    } catch (error: any) {
      setError(error.message);
    }
  };

  const handlePaymentSuccess = (paymentId: string, type: string) => {
    setActivePaymentId('');
    onPaymentSuccess?.(paymentId, type);
  };

  if (isLoadingRates) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center p-6">
          <Loader2 className="h-6 w-6 animate-spin mr-2" />
          Loading payment options...
        </CardContent>
      </Card>
    );
  }

  if (activePaymentId) {
    return (
      <PaymentStatus
        paymentId={activePaymentId}
        onStatusChange={(status) => {
          if (status === 'completed') {
            handlePaymentSuccess(activePaymentId, 'payment');
          }
        }}
        autoRefresh={true}
      />
    );
  }

  return (
    <div className="space-y-6">
      {/* Error Display */}
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Conversion Rates Info */}
      {rates && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Calculator className="h-5 w-5" />
              Current Rates
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-3 gap-4 text-sm">
              <div className="text-center">
                <p className="font-medium">Token to KES</p>
                <p className="text-lg">1 Token = KES {rates.tokenToKes}</p>
              </div>
              <div className="text-center">
                <p className="font-medium">KES to Token</p>
                <p className="text-lg">KES 1 = {rates.kesToToken} Tokens</p>
              </div>
              <div className="text-center">
                <p className="font-medium">Platform Fee</p>
                <p className="text-lg">{rates.platformFeePercentage}%</p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      <Tabs defaultValue="topup" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          {showTokenTopUp && (
            <TabsTrigger value="topup" className="flex items-center gap-2">
              <Coins className="h-4 w-4" />
              Token Top-Up
            </TabsTrigger>
          )}
          {showPlatformFee && transactionId && (
            <TabsTrigger value="platform-fee" className="flex items-center gap-2">
              <Star className="h-4 w-4" />
              Platform Fee
            </TabsTrigger>
          )}
          {showDeliveryFee && transactionId && (
            <TabsTrigger value="delivery-fee" className="flex items-center gap-2">
              <Truck className="h-4 w-4" />
              Delivery Fee
            </TabsTrigger>
          )}
        </TabsList>

        {/* Token Top-Up Tab */}
        {showTokenTopUp && (
          <TabsContent value="topup">
            <Card>
              <CardHeader>
                <CardTitle>Top Up Tokens</CardTitle>
                <CardDescription>
                  Purchase tokens to use for clothing exchanges
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="token-amount">Tokens to Purchase</Label>
                    <Input
                      id="token-amount"
                      type="number"
                      min="1"
                      value={tokenAmount}
                      onChange={(e) => setTokenAmount(Number(e.target.value))}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="cash-amount">Amount to Pay (KES)</Label>
                    <Input
                      id="cash-amount"
                      type="number"
                      min="1"
                      value={cashAmount}
                      onChange={(e) => {
                        setCashAmount(Number(e.target.value));
                        if (rates) {
                          setTokenAmount(Math.round(Number(e.target.value) * rates.kesToToken));
                        }
                      }}
                    />
                  </div>
                </div>

                <Alert>
                  <Info className="h-4 w-4" />
                  <AlertDescription>
                    You will receive {tokenAmount} tokens for KES {cashAmount.toLocaleString()}
                  </AlertDescription>
                </Alert>

                <Button onClick={handleTokenTopUp} className="w-full">
                  <Coins className="h-4 w-4 mr-2" />
                  Top Up {tokenAmount} Tokens
                </Button>
              </CardContent>
            </Card>
          </TabsContent>
        )}

        {/* Platform Fee Tab */}
        {showPlatformFee && transactionId && (
          <TabsContent value="platform-fee">
            <Card>
              <CardHeader>
                <CardTitle>Platform Fee</CardTitle>
                <CardDescription>
                  Pay the platform fee to complete your token purchase
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="fee-token-amount">Token Purchase Amount</Label>
                  <Input
                    id="fee-token-amount"
                    type="number"
                    min="1"
                    value={feeTokenAmount}
                    onChange={(e) => setFeeTokenAmount(Number(e.target.value))}
                  />
                </div>

                {platformFeeCalc && (
                  <div className="bg-gray-50 p-4 rounded-lg space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Token Amount:</span>
                      <span>{platformFeeCalc.tokenAmount} tokens</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>Token Value:</span>
                      <span>KES {(platformFeeCalc.tokenAmount * platformFeeCalc.tokenToKesRate).toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between text-sm font-medium">
                      <span>Platform Fee (5%):</span>
                      <span>KES {platformFeeCalc.platformFee.toLocaleString()}</span>
                    </div>
                  </div>
                )}

                <Button 
                  onClick={handlePlatformFeePayment} 
                  className="w-full"
                  disabled={!platformFeeCalc}
                >
                  <Star className="h-4 w-4 mr-2" />
                  Pay Platform Fee {platformFeeCalc ? `(KES ${platformFeeCalc.platformFee})` : ''}
                </Button>
              </CardContent>
            </Card>
          </TabsContent>
        )}

        {/* Delivery Fee Tab */}
        {showDeliveryFee && transactionId && (
          <TabsContent value="delivery-fee">
            <Card>
              <CardHeader>
                <CardTitle>Delivery Fee</CardTitle>
                <CardDescription>
                  Pay for delivery of your purchased item
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="delivery-fee">Delivery Fee (KES)</Label>
                  <Input
                    id="delivery-fee"
                    type="number"
                    min="1"
                    value={deliveryFee}
                    onChange={(e) => setDeliveryFee(Number(e.target.value))}
                  />
                </div>

                <Alert>
                  <Info className="h-4 w-4" />
                  <AlertDescription>
                    Delivery fee varies based on location and item size
                  </AlertDescription>
                </Alert>

                <Button onClick={handleDeliveryFeePayment} className="w-full">
                  <Truck className="h-4 w-4 mr-2" />
                  Pay Delivery Fee (KES {deliveryFee.toLocaleString()})
                </Button>
              </CardContent>
            </Card>
          </TabsContent>
        )}
      </Tabs>
    </div>
  );
};
