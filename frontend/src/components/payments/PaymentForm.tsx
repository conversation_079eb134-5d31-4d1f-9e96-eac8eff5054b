import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, CreditCard, Smartphone, AlertCircle, CheckCircle } from 'lucide-react';

interface PaymentMethod {
  _id: string;
  type: 'mpesa' | 'card' | 'bank_account';
  provider: string;
  name: string;
  displayName: string;
  isDefault: boolean;
  mpesaDetails?: {
    phoneNumber: string;
  };
}

interface PaymentFormProps {
  amount: number;
  description: string;
  onPaymentSuccess: (paymentId: string) => void;
  onPaymentError: (error: string) => void;
  relatedEntity?: {
    type: 'token_purchase' | 'platform_fee' | 'premium_feature' | 'delivery_fee';
    id: string;
  };
}

export const PaymentForm: React.FC<PaymentFormProps> = ({
  amount,
  description,
  onPaymentSuccess,
  onPaymentError,
  relatedEntity,
}) => {
  const [paymentMethods, setPaymentMethods] = useState<PaymentMethod[]>([]);
  const [selectedMethodId, setSelectedMethodId] = useState<string>('');
  const [phoneNumber, setPhoneNumber] = useState<string>('');
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingMethods, setIsLoadingMethods] = useState(true);
  const [error, setError] = useState<string>('');
  const [paymentStatus, setPaymentStatus] = useState<'idle' | 'processing' | 'success' | 'failed'>('idle');

  // Load payment methods on component mount
  useEffect(() => {
    loadPaymentMethods();
  }, []);

  const loadPaymentMethods = async () => {
    try {
      setIsLoadingMethods(true);
      const response = await fetch('/api/payment-methods', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
      });

      if (!response.ok) {
        throw new Error('Failed to load payment methods');
      }

      const data = await response.json();
      setPaymentMethods(data.data || []);
      
      // Set default payment method
      const defaultMethod = data.data?.find((method: PaymentMethod) => method.isDefault);
      if (defaultMethod) {
        setSelectedMethodId(defaultMethod._id);
        if (defaultMethod.mpesaDetails?.phoneNumber) {
          setPhoneNumber(defaultMethod.mpesaDetails.phoneNumber);
        }
      }
    } catch (error: any) {
      console.error('Failed to load payment methods:', error);
      setError('Failed to load payment methods');
    } finally {
      setIsLoadingMethods(false);
    }
  };

  const handlePaymentMethodChange = (methodId: string) => {
    setSelectedMethodId(methodId);
    const method = paymentMethods.find(m => m._id === methodId);
    if (method?.mpesaDetails?.phoneNumber) {
      setPhoneNumber(method.mpesaDetails.phoneNumber);
    } else {
      setPhoneNumber('');
    }
  };

  const formatPhoneNumber = (phone: string): string => {
    // Remove any non-digit characters
    const digits = phone.replace(/\D/g, '');
    
    // Convert to 254 format
    if (digits.startsWith('0')) {
      return '254' + digits.substring(1);
    } else if (digits.startsWith('254')) {
      return digits;
    } else if (digits.startsWith('+254')) {
      return digits.substring(1);
    }
    
    return digits;
  };

  const validatePhoneNumber = (phone: string): boolean => {
    const formatted = formatPhoneNumber(phone);
    return /^254[17]\d{8}$/.test(formatted);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setIsLoading(true);
    setPaymentStatus('processing');

    try {
      // Validate inputs
      if (!selectedMethodId && !phoneNumber) {
        throw new Error('Please select a payment method or enter a phone number');
      }

      if (phoneNumber && !validatePhoneNumber(phoneNumber)) {
        throw new Error('Please enter a valid Kenyan phone number');
      }

      // Create payment
      const createPaymentResponse = await fetch('/api/payments', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
        body: JSON.stringify({
          amount,
          description,
          paymentMethodId: selectedMethodId || undefined,
          relatedEntity,
        }),
      });

      if (!createPaymentResponse.ok) {
        const errorData = await createPaymentResponse.json();
        throw new Error(errorData.message || 'Failed to create payment');
      }

      const paymentData = await createPaymentResponse.json();
      const paymentId = paymentData.data.paymentId;

      // Initiate STK Push
      const stkPushResponse = await fetch(`/api/payments/${paymentId}/stk-push`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
        body: JSON.stringify({
          phoneNumber: formatPhoneNumber(phoneNumber),
        }),
      });

      if (!stkPushResponse.ok) {
        const errorData = await stkPushResponse.json();
        throw new Error(errorData.message || 'Failed to initiate payment');
      }

      // Start polling for payment status
      pollPaymentStatus(paymentId);

    } catch (error: any) {
      console.error('Payment failed:', error);
      setError(error.message);
      setPaymentStatus('failed');
      onPaymentError(error.message);
    } finally {
      setIsLoading(false);
    }
  };

  const pollPaymentStatus = async (paymentId: string) => {
    const maxAttempts = 30; // Poll for 5 minutes (30 * 10 seconds)
    let attempts = 0;

    const poll = async () => {
      try {
        const response = await fetch(`/api/payments/${paymentId}/status`, {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
          },
        });

        if (!response.ok) {
          throw new Error('Failed to check payment status');
        }

        const data = await response.json();
        const status = data.data.status;

        if (status === 'completed') {
          setPaymentStatus('success');
          onPaymentSuccess(paymentId);
          return;
        } else if (status === 'failed' || status === 'cancelled') {
          setPaymentStatus('failed');
          setError('Payment was not completed');
          onPaymentError('Payment was not completed');
          return;
        }

        // Continue polling if still processing
        attempts++;
        if (attempts < maxAttempts) {
          setTimeout(poll, 10000); // Poll every 10 seconds
        } else {
          setPaymentStatus('failed');
          setError('Payment timeout - please check your phone and try again');
          onPaymentError('Payment timeout');
        }
      } catch (error: any) {
        console.error('Failed to poll payment status:', error);
        setPaymentStatus('failed');
        setError('Failed to check payment status');
        onPaymentError('Failed to check payment status');
      }
    };

    poll();
  };

  const getStatusIcon = () => {
    switch (paymentStatus) {
      case 'processing':
        return <Loader2 className="h-5 w-5 animate-spin text-blue-500" />;
      case 'success':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'failed':
        return <AlertCircle className="h-5 w-5 text-red-500" />;
      default:
        return null;
    }
  };

  const getStatusMessage = () => {
    switch (paymentStatus) {
      case 'processing':
        return 'Please check your phone and enter your M-Pesa PIN to complete the payment...';
      case 'success':
        return 'Payment completed successfully!';
      case 'failed':
        return 'Payment failed. Please try again.';
      default:
        return '';
    }
  };

  if (isLoadingMethods) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center p-6">
          <Loader2 className="h-6 w-6 animate-spin mr-2" />
          Loading payment methods...
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <CreditCard className="h-5 w-5" />
          Complete Payment
        </CardTitle>
        <CardDescription>
          Pay KES {amount.toLocaleString()} for {description}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Payment Method Selection */}
          {paymentMethods.length > 0 && (
            <div className="space-y-2">
              <Label htmlFor="payment-method">Payment Method</Label>
              <Select value={selectedMethodId} onValueChange={handlePaymentMethodChange}>
                <SelectTrigger>
                  <SelectValue placeholder="Select a payment method" />
                </SelectTrigger>
                <SelectContent>
                  {paymentMethods.map((method) => (
                    <SelectItem key={method._id} value={method._id}>
                      <div className="flex items-center gap-2">
                        {method.type === 'mpesa' ? (
                          <Smartphone className="h-4 w-4" />
                        ) : (
                          <CreditCard className="h-4 w-4" />
                        )}
                        {method.displayName}
                        {method.isDefault && (
                          <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">
                            Default
                          </span>
                        )}
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          )}

          {/* Phone Number Input */}
          <div className="space-y-2">
            <Label htmlFor="phone-number">M-Pesa Phone Number</Label>
            <Input
              id="phone-number"
              type="tel"
              placeholder="0712345678 or 254712345678"
              value={phoneNumber}
              onChange={(e) => setPhoneNumber(e.target.value)}
              required
            />
            <p className="text-sm text-gray-500">
              Enter the phone number registered with M-Pesa
            </p>
          </div>

          {/* Error Display */}
          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {/* Status Display */}
          {paymentStatus !== 'idle' && (
            <Alert className={paymentStatus === 'success' ? 'border-green-200 bg-green-50' : 
                             paymentStatus === 'failed' ? 'border-red-200 bg-red-50' : 
                             'border-blue-200 bg-blue-50'}>
              <div className="flex items-center gap-2">
                {getStatusIcon()}
                <AlertDescription>{getStatusMessage()}</AlertDescription>
              </div>
            </Alert>
          )}

          {/* Submit Button */}
          <Button
            type="submit"
            className="w-full"
            disabled={isLoading || paymentStatus === 'processing' || paymentStatus === 'success'}
          >
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Processing...
              </>
            ) : paymentStatus === 'success' ? (
              'Payment Completed'
            ) : (
              `Pay KES ${amount.toLocaleString()}`
            )}
          </Button>
        </form>
      </CardContent>
    </Card>
  );
};
