import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Loader2, 
  Plus, 
  Smartphone, 
  CreditCard, 
  Building, 
  Star,
  Edit,
  Trash2,
  AlertCircle,
  CheckCircle
} from 'lucide-react';

interface PaymentMethod {
  _id: string;
  type: 'mpesa' | 'card' | 'bank_account';
  provider: string;
  name: string;
  displayName: string;
  isDefault: boolean;
  isActive: boolean;
  mpesaDetails?: {
    phoneNumber: string;
    accountName?: string;
    isVerified: boolean;
  };
  cardDetails?: {
    lastFourDigits: string;
    cardholderName: string;
    brand: string;
    isVerified: boolean;
  };
  stats: {
    totalTransactions: number;
    totalAmount: number;
    successRate: number;
  };
  verificationStatus: 'pending' | 'verified' | 'failed' | 'expired';
  createdAt: string;
}

interface PaymentMethodManagerProps {
  onMethodSelect?: (method: PaymentMethod) => void;
  showAddForm?: boolean;
}

export const PaymentMethodManager: React.FC<PaymentMethodManagerProps> = ({
  onMethodSelect,
  showAddForm = true,
}) => {
  const [paymentMethods, setPaymentMethods] = useState<PaymentMethod[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isAdding, setIsAdding] = useState(false);
  const [showForm, setShowForm] = useState(false);
  const [error, setError] = useState<string>('');
  const [success, setSuccess] = useState<string>('');

  // Form state
  const [formData, setFormData] = useState({
    type: 'mpesa' as 'mpesa' | 'card' | 'bank_account',
    provider: 'safaricom',
    name: '',
    phoneNumber: '',
    accountName: '',
  });

  useEffect(() => {
    fetchPaymentMethods();
  }, []);

  const fetchPaymentMethods = async () => {
    try {
      setIsLoading(true);
      setError('');

      const response = await fetch('/api/payment-methods', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
      });

      if (!response.ok) {
        throw new Error('Failed to fetch payment methods');
      }

      const data = await response.json();
      setPaymentMethods(data.data || []);
    } catch (error: any) {
      console.error('Failed to fetch payment methods:', error);
      setError(error.message);
    } finally {
      setIsLoading(false);
    }
  };

  const handleAddMethod = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsAdding(true);
    setError('');
    setSuccess('');

    try {
      const payload: any = {
        type: formData.type,
        provider: formData.provider,
        name: formData.name,
      };

      if (formData.type === 'mpesa') {
        payload.mpesaDetails = {
          phoneNumber: formData.phoneNumber,
          accountName: formData.accountName || undefined,
        };
      }

      const response = await fetch('/api/payment-methods', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to add payment method');
      }

      setSuccess('Payment method added successfully');
      setShowForm(false);
      setFormData({
        type: 'mpesa',
        provider: 'safaricom',
        name: '',
        phoneNumber: '',
        accountName: '',
      });
      
      await fetchPaymentMethods();
    } catch (error: any) {
      console.error('Failed to add payment method:', error);
      setError(error.message);
    } finally {
      setIsAdding(false);
    }
  };

  const handleSetDefault = async (methodId: string) => {
    try {
      const response = await fetch(`/api/payment-methods/${methodId}/set-default`, {
        method: 'PATCH',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
      });

      if (!response.ok) {
        throw new Error('Failed to set default payment method');
      }

      setSuccess('Default payment method updated');
      await fetchPaymentMethods();
    } catch (error: any) {
      console.error('Failed to set default:', error);
      setError(error.message);
    }
  };

  const handleDeleteMethod = async (methodId: string) => {
    if (!confirm('Are you sure you want to delete this payment method?')) {
      return;
    }

    try {
      const response = await fetch(`/api/payment-methods/${methodId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
      });

      if (!response.ok) {
        throw new Error('Failed to delete payment method');
      }

      setSuccess('Payment method deleted successfully');
      await fetchPaymentMethods();
    } catch (error: any) {
      console.error('Failed to delete payment method:', error);
      setError(error.message);
    }
  };

  const getMethodIcon = (type: string) => {
    switch (type) {
      case 'mpesa':
        return <Smartphone className="h-5 w-5 text-green-600" />;
      case 'card':
        return <CreditCard className="h-5 w-5 text-blue-600" />;
      case 'bank_account':
        return <Building className="h-5 w-5 text-purple-600" />;
      default:
        return <CreditCard className="h-5 w-5 text-gray-600" />;
    }
  };

  const getVerificationBadge = (status: string) => {
    switch (status) {
      case 'verified':
        return <Badge className="bg-green-100 text-green-800">Verified</Badge>;
      case 'pending':
        return <Badge className="bg-yellow-100 text-yellow-800">Pending</Badge>;
      case 'failed':
        return <Badge className="bg-red-100 text-red-800">Failed</Badge>;
      case 'expired':
        return <Badge className="bg-gray-100 text-gray-800">Expired</Badge>;
      default:
        return null;
    }
  };

  if (isLoading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center p-6">
          <Loader2 className="h-6 w-6 animate-spin mr-2" />
          Loading payment methods...
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Payment Methods</CardTitle>
              <CardDescription>
                Manage your saved payment methods
              </CardDescription>
            </div>
            {showAddForm && (
              <Button onClick={() => setShowForm(!showForm)} variant="outline">
                <Plus className="h-4 w-4 mr-2" />
                Add Method
              </Button>
            )}
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Success/Error Messages */}
          {success && (
            <Alert className="border-green-200 bg-green-50">
              <CheckCircle className="h-4 w-4 text-green-600" />
              <AlertDescription className="text-green-800">{success}</AlertDescription>
            </Alert>
          )}
          
          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {/* Add Payment Method Form */}
          {showForm && showAddForm && (
            <Card className="border-dashed">
              <CardHeader>
                <CardTitle className="text-lg">Add New Payment Method</CardTitle>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleAddMethod} className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="type">Type</Label>
                      <Select 
                        value={formData.type} 
                        onValueChange={(value: any) => setFormData(prev => ({ ...prev, type: value }))}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="mpesa">M-Pesa</SelectItem>
                          <SelectItem value="card">Credit/Debit Card</SelectItem>
                          <SelectItem value="bank_account">Bank Account</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="provider">Provider</Label>
                      <Select 
                        value={formData.provider} 
                        onValueChange={(value) => setFormData(prev => ({ ...prev, provider: value }))}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {formData.type === 'mpesa' && (
                            <SelectItem value="safaricom">Safaricom</SelectItem>
                          )}
                          {formData.type === 'card' && (
                            <>
                              <SelectItem value="visa">Visa</SelectItem>
                              <SelectItem value="mastercard">Mastercard</SelectItem>
                            </>
                          )}
                          {formData.type === 'bank_account' && (
                            <>
                              <SelectItem value="equity">Equity Bank</SelectItem>
                              <SelectItem value="kcb">KCB Bank</SelectItem>
                              <SelectItem value="coop">Co-operative Bank</SelectItem>
                              <SelectItem value="other">Other</SelectItem>
                            </>
                          )}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="name">Display Name</Label>
                    <Input
                      id="name"
                      placeholder="e.g., My M-Pesa, Work Card"
                      value={formData.name}
                      onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                      required
                    />
                  </div>

                  {formData.type === 'mpesa' && (
                    <>
                      <div className="space-y-2">
                        <Label htmlFor="phoneNumber">Phone Number</Label>
                        <Input
                          id="phoneNumber"
                          type="tel"
                          placeholder="********** or ************"
                          value={formData.phoneNumber}
                          onChange={(e) => setFormData(prev => ({ ...prev, phoneNumber: e.target.value }))}
                          required
                        />
                      </div>
                      
                      <div className="space-y-2">
                        <Label htmlFor="accountName">Account Name (Optional)</Label>
                        <Input
                          id="accountName"
                          placeholder="Account holder name"
                          value={formData.accountName}
                          onChange={(e) => setFormData(prev => ({ ...prev, accountName: e.target.value }))}
                        />
                      </div>
                    </>
                  )}

                  <div className="flex gap-2">
                    <Button type="submit" disabled={isAdding}>
                      {isAdding ? (
                        <>
                          <Loader2 className="h-4 w-4 animate-spin mr-2" />
                          Adding...
                        </>
                      ) : (
                        'Add Payment Method'
                      )}
                    </Button>
                    <Button type="button" variant="outline" onClick={() => setShowForm(false)}>
                      Cancel
                    </Button>
                  </div>
                </form>
              </CardContent>
            </Card>
          )}

          {/* Payment Methods List */}
          {paymentMethods.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <CreditCard className="h-12 w-12 mx-auto mb-4 text-gray-300" />
              <p>No payment methods added yet</p>
              {showAddForm && (
                <Button 
                  variant="link" 
                  onClick={() => setShowForm(true)}
                  className="mt-2"
                >
                  Add your first payment method
                </Button>
              )}
            </div>
          ) : (
            <div className="space-y-3">
              {paymentMethods.map((method) => (
                <div
                  key={method._id}
                  className={`border rounded-lg p-4 transition-colors ${
                    onMethodSelect 
                      ? 'cursor-pointer hover:bg-gray-50' 
                      : ''
                  }`}
                  onClick={() => onMethodSelect?.(method)}
                >
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center gap-3">
                      {getMethodIcon(method.type)}
                      <div>
                        <div className="flex items-center gap-2">
                          <span className="font-medium">{method.name}</span>
                          {method.isDefault && (
                            <Badge className="bg-blue-100 text-blue-800">
                              <Star className="h-3 w-3 mr-1" />
                              Default
                            </Badge>
                          )}
                        </div>
                        <p className="text-sm text-gray-600">{method.displayName}</p>
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-2">
                      {getVerificationBadge(method.verificationStatus)}
                      
                      <div className="flex gap-1">
                        {!method.isDefault && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleSetDefault(method._id);
                            }}
                          >
                            <Star className="h-4 w-4" />
                          </Button>
                        )}
                        
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleDeleteMethod(method._id);
                          }}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </div>

                  {/* Stats */}
                  {method.stats.totalTransactions > 0 && (
                    <div className="grid grid-cols-3 gap-4 text-sm text-gray-600 mt-3 pt-3 border-t">
                      <div>
                        <p className="font-medium">Transactions</p>
                        <p>{method.stats.totalTransactions}</p>
                      </div>
                      <div>
                        <p className="font-medium">Total Amount</p>
                        <p>KES {method.stats.totalAmount.toLocaleString()}</p>
                      </div>
                      <div>
                        <p className="font-medium">Success Rate</p>
                        <p>{method.stats.successRate}%</p>
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};
