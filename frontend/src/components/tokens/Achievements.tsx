'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Tabs, Ta<PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Trophy, 
  Star, 
  Target, 
  Clock, 
  CheckCircle,
  Lock,
  RefreshCw,
  AlertCircle,
  Award,
  TrendingUp
} from 'lucide-react';

interface Achievement {
  id: string;
  name: string;
  description: string;
  icon: string;
  category: string;
  type: 'bronze' | 'silver' | 'gold' | 'platinum' | 'special';
  requirements: {
    condition: string;
    value: number;
    operator: string;
  };
  rewards: {
    tokens: number;
    badge?: string;
    title?: string;
  };
}

interface UserAchievement {
  id: string;
  achievement: Achievement;
  progress?: {
    current: number;
    target: number;
    percentage: number;
  };
  isCompleted: boolean;
  unlockedAt?: string;
  tokensAwarded: number;
}

interface AchievementStats {
  totalCompleted: number;
  totalTokensEarned: number;
  totalAvailable: number;
  completionPercentage: number;
}

interface AchievementsProps {
  refreshTrigger?: number;
  onAchievementUnlocked?: (achievement: UserAchievement) => void;
}

export function Achievements({ refreshTrigger, onAchievementUnlocked }: AchievementsProps) {
  const [userAchievements, setUserAchievements] = useState<{
    completed: UserAchievement[];
    inProgress: UserAchievement[];
    stats: AchievementStats;
  } | null>(null);
  const [categories, setCategories] = useState<Record<string, Achievement[]>>({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [checkingNew, setCheckingNew] = useState(false);

  useEffect(() => {
    fetchUserAchievements();
    fetchAchievementCategories();
  }, [refreshTrigger]);

  const fetchUserAchievements = async () => {
    try {
      const token = localStorage.getItem('token');
      if (!token) {
        setError('Please log in to view achievements');
        return;
      }

      const response = await fetch('/api/achievements?endpoint=user', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Failed to fetch user achievements');
      }

      setUserAchievements(data.data);
    } catch (err) {
      console.error('Error fetching user achievements:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch achievements');
    }
  };

  const fetchAchievementCategories = async () => {
    try {
      const response = await fetch('/api/achievements?endpoint=categories');
      const data = await response.json();

      if (response.ok && data.success) {
        setCategories(data.data.categories);
      }
    } catch (err) {
      console.error('Error fetching achievement categories:', err);
    } finally {
      setLoading(false);
    }
  };

  const checkForNewAchievements = async () => {
    setCheckingNew(true);
    try {
      const token = localStorage.getItem('token');
      if (!token) return;

      const response = await fetch('/api/achievements', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ endpoint: 'check' }),
      });

      const data = await response.json();

      if (data.success && data.data.newAchievements.length > 0) {
        // Refresh achievements data
        await fetchUserAchievements();
        
        // Notify parent component
        data.data.newAchievements.forEach((achievement: UserAchievement) => {
          if (onAchievementUnlocked) {
            onAchievementUnlocked(achievement);
          }
        });
      }
    } catch (err) {
      console.error('Error checking for new achievements:', err);
    } finally {
      setCheckingNew(false);
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'bronze': return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'silver': return 'bg-gray-100 text-gray-800 border-gray-200';
      case 'gold': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'platinum': return 'bg-purple-100 text-purple-800 border-purple-200';
      case 'special': return 'bg-pink-100 text-pink-800 border-pink-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'tokens': return <Trophy className="h-4 w-4" />;
      case 'activity': return <Target className="h-4 w-4" />;
      case 'sustainability': return <Star className="h-4 w-4" />;
      case 'social': return <Award className="h-4 w-4" />;
      case 'time': return <Clock className="h-4 w-4" />;
      default: return <Trophy className="h-4 w-4" />;
    }
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Trophy className="h-5 w-5" />
            Achievements
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="animate-pulse space-y-4">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="p-4 border rounded-lg">
                <div className="flex items-center gap-3 mb-3">
                  <div className="h-8 w-8 bg-gray-200 rounded-full" />
                  <div>
                    <div className="h-4 bg-gray-200 rounded w-32 mb-1" />
                    <div className="h-3 bg-gray-200 rounded w-24" />
                  </div>
                </div>
                <div className="h-2 bg-gray-200 rounded w-full" />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Trophy className="h-5 w-5" />
            Achievements
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Trophy className="h-5 w-5" />
            Achievements
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={checkForNewAchievements}
            disabled={checkingNew}
          >
            <RefreshCw className={`h-4 w-4 ${checkingNew ? 'animate-spin' : ''}`} />
          </Button>
        </CardTitle>
      </CardHeader>
      <CardContent>
        {userAchievements && (
          <>
            {/* Stats Overview */}
            <div className="grid grid-cols-2 gap-4 mb-6">
              <div className="text-center p-3 bg-green-50 rounded-lg">
                <div className="text-2xl font-bold text-green-600">
                  {userAchievements.stats.totalCompleted}
                </div>
                <div className="text-sm text-green-700">Completed</div>
              </div>
              <div className="text-center p-3 bg-blue-50 rounded-lg">
                <div className="text-2xl font-bold text-blue-600">
                  {userAchievements.stats.completionPercentage}%
                </div>
                <div className="text-sm text-blue-700">Progress</div>
              </div>
            </div>

            <Tabs defaultValue="completed" className="space-y-4">
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="completed">
                  Completed ({userAchievements.completed.length})
                </TabsTrigger>
                <TabsTrigger value="progress">
                  In Progress ({userAchievements.inProgress.length})
                </TabsTrigger>
              </TabsList>

              <TabsContent value="completed" className="space-y-3">
                {userAchievements.completed.length === 0 ? (
                  <div className="text-center py-8 text-gray-500">
                    <Trophy className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p>No achievements completed yet</p>
                    <p className="text-sm">Start your journey to unlock achievements!</p>
                  </div>
                ) : (
                  userAchievements.completed.map((ua) => (
                    <div
                      key={ua.id}
                      className="p-4 border rounded-lg bg-green-50 border-green-200"
                    >
                      <div className="flex items-start justify-between mb-2">
                        <div className="flex items-center gap-3">
                          <div className="text-2xl">{ua.achievement.icon}</div>
                          <div>
                            <h3 className="font-medium text-sm">{ua.achievement.name}</h3>
                            <p className="text-xs text-gray-600">{ua.achievement.description}</p>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <Badge className={getTypeColor(ua.achievement.type)}>
                            {ua.achievement.type}
                          </Badge>
                          <CheckCircle className="h-4 w-4 text-green-600" />
                        </div>
                      </div>
                      <div className="flex items-center justify-between text-xs text-gray-500">
                        <span>+{ua.tokensAwarded} tokens earned</span>
                        {ua.unlockedAt && (
                          <span>
                            Unlocked {new Date(ua.unlockedAt).toLocaleDateString()}
                          </span>
                        )}
                      </div>
                    </div>
                  ))
                )}
              </TabsContent>

              <TabsContent value="progress" className="space-y-3">
                {userAchievements.inProgress.length === 0 ? (
                  <div className="text-center py-8 text-gray-500">
                    <Target className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p>All achievements completed!</p>
                    <p className="text-sm">Check back for new achievements.</p>
                  </div>
                ) : (
                  userAchievements.inProgress.map((ua) => (
                    <div
                      key={ua.id}
                      className="p-4 border rounded-lg"
                    >
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex items-center gap-3">
                          <div className="text-2xl opacity-50">{ua.achievement.icon}</div>
                          <div>
                            <h3 className="font-medium text-sm">{ua.achievement.name}</h3>
                            <p className="text-xs text-gray-600">{ua.achievement.description}</p>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <Badge className={getTypeColor(ua.achievement.type)}>
                            {ua.achievement.type}
                          </Badge>
                          <Lock className="h-4 w-4 text-gray-400" />
                        </div>
                      </div>
                      
                      {ua.progress && (
                        <div className="space-y-2">
                          <div className="flex justify-between text-xs">
                            <span>{ua.progress.current} / {ua.progress.target}</span>
                            <span>{ua.progress.percentage}%</span>
                          </div>
                          <div className="w-full bg-gray-200 rounded-full h-2">
                            <div
                              className="bg-blue-600 h-2 rounded-full transition-all"
                              style={{ width: `${ua.progress.percentage}%` }}
                            />
                          </div>
                        </div>
                      )}
                      
                      <div className="text-xs text-gray-500 mt-2">
                        Reward: +{ua.achievement.rewards.tokens} tokens
                      </div>
                    </div>
                  ))
                )}
              </TabsContent>
            </Tabs>
          </>
        )}
      </CardContent>
    </Card>
  );
}
