'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Coins, 
  TrendingUp, 
  TrendingDown, 
  RefreshCw,
  Gift,
  AlertCircle,
  Sparkles
} from 'lucide-react';

interface TokenBalanceProps {
  onClaimDailyBonus?: () => void;
  onRefresh?: () => void;
}

interface TokenData {
  balance: number;
  userId: string;
  lastUpdated: string;
}

export function TokenBalance({ onClaimDailyBonus, onRefresh }: TokenBalanceProps) {
  const [tokenData, setTokenData] = useState<TokenData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [claimingBonus, setClaimingBonus] = useState(false);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    fetchTokenBalance();
  }, []);

  const fetchTokenBalance = async () => {
    setLoading(true);
    setError('');

    try {
      const token = localStorage.getItem('token');
      if (!token) {
        setError('Please log in to view your token balance');
        return;
      }

      const response = await fetch('/api/tokens?endpoint=balance', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Failed to fetch token balance');
      }

      setTokenData(data.data);
    } catch (err) {
      console.error('Error fetching token balance:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch token balance');
    } finally {
      setLoading(false);
    }
  };

  const handleClaimDailyBonus = async () => {
    setClaimingBonus(true);
    setError('');

    try {
      const token = localStorage.getItem('token');
      if (!token) {
        setError('Please log in to claim daily bonus');
        return;
      }

      const response = await fetch('/api/tokens', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ endpoint: 'daily-bonus' }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Failed to claim daily bonus');
      }

      if (data.success) {
        // Update balance
        setTokenData(prev => prev ? {
          ...prev,
          balance: prev.balance + data.data.tokensEarned
        } : null);
        
        if (onClaimDailyBonus) {
          onClaimDailyBonus();
        }
      } else {
        setError(data.message || 'Daily bonus already claimed today');
      }
    } catch (err) {
      console.error('Error claiming daily bonus:', err);
      setError(err instanceof Error ? err.message : 'Failed to claim daily bonus');
    } finally {
      setClaimingBonus(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await fetchTokenBalance();
    setRefreshing(false);
    
    if (onRefresh) {
      onRefresh();
    }
  };

  const formatTokens = (amount: number) => {
    return amount.toLocaleString();
  };

  const getBalanceColor = (balance: number) => {
    if (balance >= 1000) return 'text-brand-pine-green';
    if (balance >= 500) return 'text-brand-dark-green';
    if (balance >= 100) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getBalanceIcon = (balance: number) => {
    if (balance >= 1000) return <TrendingUp className="h-5 w-5 text-green-600" />;
    if (balance >= 100) return <Coins className="h-5 w-5 text-blue-600" />;
    return <TrendingDown className="h-5 w-5 text-red-600" />;
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Coins className="h-5 w-5" />
            Token Balance
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded w-32 mb-4" />
            <div className="h-4 bg-gray-200 rounded w-48 mb-2" />
            <div className="h-10 bg-gray-200 rounded w-full" />
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error && !tokenData) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Coins className="h-5 w-5" />
            Token Balance
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
          <Button 
            onClick={handleRefresh} 
            className="mt-4 w-full"
            disabled={refreshing}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            Try Again
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Coins className="h-5 w-5" />
            Token Balance
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={handleRefresh}
            disabled={refreshing}
          >
            <RefreshCw className={`h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
          </Button>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Current Balance */}
        <div className="text-center">
          <div className="flex items-center justify-center gap-2 mb-2">
            {tokenData && getBalanceIcon(tokenData.balance)}
            <span className={`text-3xl font-bold ${tokenData ? getBalanceColor(tokenData.balance) : 'text-gray-600'}`}>
              {tokenData ? formatTokens(tokenData.balance) : '0'}
            </span>
            <span className="text-lg text-gray-500">tokens</span>
          </div>
          <p className="text-sm text-gray-500">
            Last updated: {tokenData ? new Date(tokenData.lastUpdated).toLocaleString() : 'Never'}
          </p>
        </div>

        {/* Balance Status */}
        <div className="flex justify-center">
          {tokenData && tokenData.balance >= 1000 && (
            <Badge className="bg-green-100 text-green-800">
              <Sparkles className="h-3 w-3 mr-1" />
              High Balance
            </Badge>
          )}
          {tokenData && tokenData.balance < 100 && (
            <Badge variant="outline" className="text-orange-600 border-orange-200">
              <AlertCircle className="h-3 w-3 mr-1" />
              Low Balance
            </Badge>
          )}
        </div>

        {/* Daily Bonus Button */}
        <Button
          onClick={handleClaimDailyBonus}
          disabled={claimingBonus}
          className="w-full"
          variant="outline"
        >
          <Gift className={`h-4 w-4 mr-2 ${claimingBonus ? 'animate-pulse' : ''}`} />
          {claimingBonus ? 'Claiming...' : 'Claim Daily Bonus (5 tokens)'}
        </Button>

        {/* Error Display */}
        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* Quick Stats */}
        {tokenData && (
          <div className="text-xs text-gray-500 text-center pt-2 border-t">
            <p>Use tokens to purchase items or save for exclusive rewards</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
