'use client';

import { useEffect, useState } from 'react';
import Link from 'next/link';
import { ArrowR<PERSON>, Play, Sparkles, Recycle, Heart } from 'lucide-react';
import { Button } from '@/components/ui/button';

export default function Hero() {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    setIsVisible(true);
  }, []);

  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-hero">
      {/* Enhanced background with modern mesh gradient */}
      <div className="absolute inset-0">
        {/* Modern mesh background */}
        <div className="absolute inset-0 bg-gradient-mesh opacity-60"></div>
        
        {/* Enhanced animated background elements with modern styling */}
        <div className="absolute top-20 left-10 w-20 h-20 bg-brand-sage-green/30 rounded-full animate-float shadow-glow backdrop-blur-sm"></div>
        <div className="absolute top-40 right-20 w-16 h-16 bg-brand-mint/20 rounded-full animate-float shadow-colored" style={{ animationDelay: '1s' }}></div>
        <div className="absolute bottom-40 left-20 w-12 h-12 bg-brand-pine-green/40 rounded-full animate-float shadow-soft" style={{ animationDelay: '2s' }}></div>
        <div className="absolute top-60 left-1/3 w-8 h-8 bg-brand-light-green/30 rounded-full animate-float" style={{ animationDelay: '3s' }}></div>
        <div className="absolute bottom-60 right-1/3 w-14 h-14 bg-brand-sage-green/25 rounded-full animate-float shadow-medium" style={{ animationDelay: '4s' }}></div>
        <div className="absolute bottom-20 right-10 w-24 h-24 bg-brand-mint/15 rounded-full animate-float" style={{ animationDelay: '0.5s' }}></div>
        
        {/* Additional floating elements for depth */}
        <div className="absolute top-1/4 right-1/4 w-6 h-6 bg-brand-white/20 rounded-full animate-float" style={{ animationDelay: '2.5s' }}></div>
        <div className="absolute bottom-1/4 left-1/4 w-10 h-10 bg-brand-sage-green/20 rounded-full animate-float" style={{ animationDelay: '1.5s' }}></div>

        {/* Dynamic shimmer overlay */}
        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-brand-white/5 to-transparent animate-shimmer"></div>
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <div className="max-w-4xl mx-auto">
          {/* Enhanced Badge with modern styling */}
          <div className={`inline-flex items-center space-x-2 card-glass rounded-full px-8 py-4 mb-8 border border-brand-white/40 shadow-elevated transition-all duration-1000 delay-100 hover-glow click-scale ${
            isVisible ? 'animate-fade-in-up opacity-100' : 'opacity-0'
          }`}>
            <Sparkles className="h-5 w-5 text-brand-sage-green animate-pulse-slow" />
            <span className="text-brand-white text-sm font-subheading tracking-wide font-medium">Kenya&apos;s First Sustainable Fashion Platform</span>
          </div>

          {/* Enhanced Main Headline with modern typography */}
          <h1 className={`text-4xl sm:text-5xl lg:text-7xl font-heading text-brand-white mb-6 leading-tight transition-all duration-1000 delay-200 ${
            isVisible ? 'animate-fade-in-up opacity-100' : 'opacity-0'
          }`}>
            The Future of Fashion is{' '}
            <span className="relative inline-block">
              <span className="bg-gradient-to-r from-brand-sage-green via-brand-mint to-brand-light-green bg-clip-text text-transparent font-bold">
                Circular
              </span>
              <div className="absolute -bottom-2 left-0 right-0 h-1 bg-gradient-to-r from-brand-sage-green to-brand-mint rounded-full opacity-80 shadow-glow"></div>
              <div className="absolute -bottom-1 left-0 right-0 h-0.5 bg-gradient-to-r from-brand-mint to-brand-sage-green rounded-full opacity-60"></div>
            </span>
          </h1>

          {/* Enhanced Subtitle */}
          <p className={`text-xl sm:text-2xl text-brand-white/90 mb-8 max-w-3xl mx-auto leading-relaxed font-body transition-all duration-1000 delay-400 ${
            isVisible ? 'animate-fade-in-up opacity-100' : 'opacity-0'
          }`}>
            Join Kenya&apos;s sustainable clothing exchange platform. Swap, donate, and discover pre-loved fashion
            while earning Pedi tokens and making a positive impact on our environment.
          </p>

          {/* Enhanced CTA Buttons */}
          <div className={`flex flex-col sm:flex-row items-center justify-center space-y-4 sm:space-y-0 sm:space-x-6 mb-12 transition-all duration-1000 delay-600 ${
            isVisible ? 'animate-fade-in-up opacity-100' : 'opacity-0'
          }`}>
            <Link href="/auth">
              <Button
                size="lg"
                className="btn-modern px-12 py-6 text-lg font-subheading group rounded-xl shadow-colored hover:shadow-glow transition-all-smooth"
              >
                Start Your Journey
                <ArrowRight className="ml-3 h-5 w-5 group-hover:translate-x-1 transition-transform-smooth" />
              </Button>
            </Link>

            <Button
              variant="ghost"
              size="lg"
              className="text-brand-white hover:bg-brand-white/10 px-10 py-5 text-lg font-subheading group rounded-xl border border-brand-white/30 hover:border-brand-white/50 backdrop-blur-sm"
            >
              <Play className="mr-3 h-5 w-5 group-hover:scale-110 transition-transform" />
              Watch Demo
            </Button>
          </div>

          {/* Enhanced Stats with modern cards */}
          <div className={`grid grid-cols-1 sm:grid-cols-3 gap-8 max-w-2xl mx-auto transition-all duration-1000 delay-800 ${
            isVisible ? 'animate-fade-in-up opacity-100' : 'opacity-0'
          }`}>
            <div className="text-center card-glass p-6 rounded-2xl border border-brand-white/20 hover-glow">
              <div className="text-3xl font-heading text-brand-white mb-2">10K+</div>
              <div className="text-brand-white/70 font-body">Clothes Exchanged</div>
            </div>
            <div className="text-center card-glass p-6 rounded-2xl border border-brand-white/20 hover-glow">
              <div className="text-3xl font-heading text-brand-white mb-2">5K+</div>
              <div className="text-brand-white/70 font-body">Active Users</div>
            </div>
            <div className="text-center card-glass p-6 rounded-2xl border border-brand-white/20 hover-glow">
              <div className="text-3xl font-heading text-brand-white mb-2">2.5T</div>
              <div className="text-brand-white/70 font-body">CO₂ Saved (kg)</div>
            </div>
          </div>
        </div>

        {/* Enhanced Floating Action Icons */}
        <div className="absolute bottom-20 left-1/2 transform -translate-x-1/2">
          <div className="flex space-x-4">
            <div className="card-glass rounded-full p-4 animate-float border border-brand-white/30 hover-glow">
              <Recycle className="h-6 w-6 text-brand-sage-green" />
            </div>
            <div className="card-glass rounded-full p-4 animate-float border border-brand-white/30 hover-glow" style={{ animationDelay: '1s' }}>
              <Heart className="h-6 w-6 text-brand-sage-green" />
            </div>
          </div>
        </div>
      </div>

      {/* Enhanced Scroll indicator */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
        <div className="w-6 h-10 border-2 border-brand-white/40 rounded-full flex justify-center backdrop-blur-sm">
          <div className="w-1 h-3 bg-brand-sage-green rounded-full mt-2 animate-pulse"></div>
        </div>
      </div>
    </section>
  );
}