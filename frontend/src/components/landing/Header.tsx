'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { Menu, X, Leaf } from 'lucide-react';
import { Button } from '@/components/ui/button';

export default function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 50);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  const navItems = [
    { name: 'How It Works', href: '#how-it-works' },
    { name: 'Features', href: '#features' },
    { name: 'Impact', href: '#impact' },
    { name: 'Community', href: '#community' },
  ];

  return (
    <header
      className={`fixed top-4 left-4 right-4 z-50 transition-all-smooth rounded-2xl ${
        isScrolled
          ? 'card-glass bg-brand-white/95 backdrop-blur-md shadow-elevated border border-brand-sage-green/30'
          : 'card-glass border border-brand-white/30 shadow-glow'
      }`}
    >
      <div className="max-w-7xl mx-auto px-6 sm:px-8 lg:px-10">
        <div className="flex items-center justify-between h-16 lg:h-20">
          {/* Enhanced Logo */}
          <Link href="/" className="flex items-center space-x-3 group">
            <div className="relative">
              <Leaf className={`h-9 w-9 transition-all-smooth ${
                isScrolled ? 'text-brand-pine-green' : 'text-brand-sage-green'
              } group-hover:scale-110 drop-shadow-sm group-hover:rotate-12`} />
              <div className="absolute inset-0 bg-brand-sage-green/20 rounded-full scale-0 group-hover:scale-150 transition-transform duration-300 shadow-glow"></div>
            </div>
            <span className={`text-2xl font-heading transition-all-smooth ${
              isScrolled ? 'text-brand-dark-green' : 'text-brand-white'
            }`}>
              Pedi
            </span>
          </Link>

          {/* Enhanced Desktop Navigation */}
          <nav className="hidden lg:flex items-center space-x-8">
            {navItems.map((item) => (
              <a
                key={item.name}
                href={item.href}
                className={`text-sm font-subheading transition-all-smooth hover:scale-105 relative group px-3 py-2 rounded-lg ${
                  isScrolled
                    ? 'text-brand-dark-green hover:text-brand-pine-green hover:bg-brand-light-green/20'
                    : 'text-brand-white/90 hover:text-brand-white hover:bg-brand-white/10'
                }`}
              >
                {item.name}
                <span className={`absolute -bottom-1 left-3 w-0 h-0.5 transition-all duration-300 group-hover:w-[calc(100%-1.5rem)] rounded-full ${
                  isScrolled ? 'bg-brand-pine-green' : 'bg-brand-sage-green'
                }`}></span>
              </a>
            ))}
          </nav>

          {/* Enhanced Desktop CTA Buttons */}
          <div className="hidden lg:flex items-center space-x-4">
            <Link href="/auth">
              <Button
                variant="ghost"
                className={`font-subheading transition-all-smooth rounded-xl px-6 hover-scale ${
                  isScrolled
                    ? 'text-brand-dark-green hover:text-brand-pine-green hover:bg-brand-light-green/30'
                    : 'text-brand-white hover:text-brand-white hover:bg-brand-white/15'
                }`}
              >
                Sign In
              </Button>
            </Link>
            <Link href="/auth">
              <Button
                className={`btn-modern font-subheading transition-all-smooth hover-lift rounded-xl px-6 shadow-colored ${
                  isScrolled
                    ? 'hover:shadow-glow'
                    : 'hover:shadow-glow'
                }`}
              >
                Get Started
              </Button>
            </Link>
          </div>

          {/* Enhanced Mobile Menu Button */}
          <button
            onClick={toggleMenu}
            className={`lg:hidden p-3 rounded-xl transition-all-smooth hover-scale click-scale ${
              isScrolled
                ? 'text-brand-dark-green hover:bg-brand-light-green/30'
                : 'text-brand-white hover:bg-brand-white/15'
            }`}
          >
            {isMenuOpen ? (
              <X className="h-6 w-6" />
            ) : (
              <Menu className="h-6 w-6" />
            )}
          </button>
        </div>

        {/* Enhanced Mobile Navigation */}
        {isMenuOpen && (
          <div className="lg:hidden">
            <div className="card-modern px-4 pt-4 pb-6 space-y-2 mt-4 shadow-elevated border border-brand-sage-green/30 animate-fade-in-up">
              {navItems.map((item) => (
                <a
                  key={item.name}
                  href={item.href}
                  className="block px-4 py-3 font-subheading text-brand-dark-green hover:text-brand-pine-green hover:bg-brand-light-green/30 rounded-lg transition-all-smooth hover-scale"
                  onClick={() => setIsMenuOpen(false)}
                >
                  {item.name}
                </a>
              ))}
              <div className="pt-4 space-y-3 border-t border-brand-sage-green/30">
                <Link href="/auth" className="block">
                  <Button
                    variant="ghost"
                    className="w-full font-subheading text-brand-dark-green hover:text-brand-pine-green hover:bg-brand-light-green/30 rounded-lg"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    Sign In
                  </Button>
                </Link>
                <Link href="/auth" className="block">
                  <Button
                    className="btn-modern w-full font-subheading rounded-lg shadow-colored hover:shadow-glow"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    Get Started
                  </Button>
                </Link>
              </div>
            </div>
          </div>
        )}
      </div>
    </header>
  );
}