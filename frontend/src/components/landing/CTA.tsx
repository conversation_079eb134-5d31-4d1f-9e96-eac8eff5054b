'use client';

import { useEffect, useRef, useState } from 'react';
import Link from 'next/link';
import { <PERSON>R<PERSON>, <PERSON><PERSON><PERSON>, Users, Leaf, Heart } from 'lucide-react';
import { Button } from '@/components/ui/button';

const stats = [
  { value: '10K+', label: 'Items Exchanged', icon: ArrowRight },
  { value: '5K+', label: 'Happy Users', icon: Users },
  { value: '2.5T', label: 'CO₂ Saved (kg)', icon: Leaf },
  { value: '50+', label: 'Charity Partners', icon: Heart },
];

export default function CTA() {
  const [isVisible, setIsVisible] = useState(false);
  const sectionRef = useRef<HTMLElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            setIsVisible(true);
          }
        });
      },
      { threshold: 0.1 }
    );

    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }

    return () => observer.disconnect();
  }, []);

  return (
    <section ref={sectionRef} className="py-20 relative overflow-hidden">
      {/* Enhanced background with modern mesh gradient */}
      <div className="absolute inset-0 bg-gradient-hero">
        {/* Modern mesh background */}
        <div className="absolute inset-0 bg-gradient-mesh opacity-70"></div>
        
        {/* Enhanced animated background elements */}
        <div className="absolute top-10 left-10 w-20 h-20 bg-brand-sage-green/30 rounded-full animate-float shadow-glow"></div>
        <div className="absolute top-20 right-20 w-16 h-16 bg-brand-mint/20 rounded-full animate-float shadow-colored" style={{ animationDelay: '1s' }}></div>
        <div className="absolute bottom-20 left-20 w-12 h-12 bg-brand-pine-green/40 rounded-full animate-float shadow-soft" style={{ animationDelay: '2s' }}></div>
        <div className="absolute bottom-10 right-10 w-24 h-24 bg-brand-light-green/15 rounded-full animate-float" style={{ animationDelay: '0.5s' }}></div>
        
        {/* Additional floating elements */}
        <div className="absolute top-1/3 left-1/4 w-8 h-8 bg-brand-white/20 rounded-full animate-float" style={{ animationDelay: '2.5s' }}></div>
        <div className="absolute bottom-1/3 right-1/4 w-14 h-14 bg-brand-sage-green/25 rounded-full animate-float" style={{ animationDelay: '1.5s' }}></div>

        {/* Dynamic shimmer overlay */}
        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-brand-white/5 to-transparent animate-shimmer"></div>
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center">
          {/* Enhanced Badge */}
          <div className={`inline-flex items-center space-x-2 card-glass rounded-full px-8 py-4 mb-8 transition-all duration-1000 border border-brand-white/40 shadow-elevated hover-glow ${
            isVisible ? 'animate-fade-in-up opacity-100' : 'opacity-0'
          }`}>
            <Sparkles className="h-5 w-5 text-brand-sage-green animate-pulse-slow" />
            <span className="text-brand-white text-sm font-subheading font-medium">Join the Sustainable Fashion Revolution</span>
          </div>

          {/* Enhanced Main Headline */}
          <h2 className={`text-3xl sm:text-4xl lg:text-6xl font-heading text-brand-white mb-6 leading-tight transition-all duration-1000 delay-200 ${
            isVisible ? 'animate-fade-in-up opacity-100' : 'opacity-0'
          }`}>
            Ready to Transform Your{' '}
            <span className="bg-gradient-to-r from-brand-sage-green via-brand-mint to-brand-light-green bg-clip-text text-transparent font-bold">
              Wardrobe
            </span>?
          </h2>

          {/* Enhanced Subtitle */}
          <p className={`text-xl sm:text-2xl text-brand-white/90 mb-12 max-w-3xl mx-auto leading-relaxed font-body transition-all duration-1000 delay-400 ${
            isVisible ? 'animate-fade-in-up opacity-100' : 'opacity-0'
          }`}>
            Join thousands of Kenyans making sustainable fashion choices. Start your journey today and be part of the change.
          </p>

          {/* Enhanced CTA Buttons */}
          <div className={`flex flex-col sm:flex-row items-center justify-center space-y-4 sm:space-y-0 sm:space-x-6 mb-16 transition-all duration-1000 delay-600 ${
            isVisible ? 'animate-fade-in-up opacity-100' : 'opacity-0'
          }`}>
            <Link href="/auth">
              <Button
                size="lg"
                className="btn-modern px-12 py-5 text-lg font-subheading group rounded-xl shadow-glow hover:shadow-elevated"
              >
                Get Started Free
                <ArrowRight className="ml-3 h-5 w-5 group-hover:translate-x-1 transition-transform" />
              </Button>
            </Link>

            <Link href="/browse">
              <Button
                variant="outline"
                size="lg"
                className="btn-outline-modern border-brand-white text-brand-white hover:bg-brand-white hover:text-brand-dark-green px-12 py-5 text-lg font-subheading rounded-xl backdrop-blur-sm"
              >
                Explore Items
              </Button>
            </Link>
          </div>

          {/* Enhanced Stats Grid */}
          <div className={`grid grid-cols-2 lg:grid-cols-4 gap-8 max-w-4xl mx-auto transition-all duration-1000 delay-800 ${
            isVisible ? 'animate-fade-in-up opacity-100' : 'opacity-0'
          }`}>
            {stats.map((stat, index) => {
              const Icon = stat.icon;
              return (
                <div
                  key={index}
                  className="text-center group hover-glow click-scale"
                  style={{ animationDelay: `${(index + 1) * 200}ms` }}
                >
                  <div className="card-glass rounded-2xl p-8 transition-all-smooth group-hover:bg-brand-white/25 border border-brand-white/30 shadow-elevated">
                    <Icon className="h-8 w-8 text-brand-sage-green mx-auto mb-4 group-hover:scale-110 transition-transform hover-bounce" />
                    <div className="text-3xl font-heading text-brand-white mb-2 group-hover:scale-105 transition-transform">
                      {stat.value}
                    </div>
                    <div className="text-brand-white/70 text-sm font-body">
                      {stat.label}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>

          {/* Enhanced trust indicators */}
          <div className={`mt-16 transition-all duration-1000 delay-1000 ${
            isVisible ? 'animate-fade-in-up opacity-100' : 'opacity-0'
          }`}>
            <p className="text-brand-white/70 text-sm mb-6 font-body">Trusted by sustainable fashion enthusiasts across Kenya</p>
            <div className="flex flex-col sm:flex-row items-center justify-center space-y-2 sm:space-y-0 sm:space-x-8 opacity-90">
              <div className="card-glass px-4 py-2 rounded-full border border-brand-white/20">
                <span className="text-brand-white text-sm font-subheading">✓ Secure Transactions</span>
              </div>
              <div className="card-glass px-4 py-2 rounded-full border border-brand-white/20">
                <span className="text-brand-white text-sm font-subheading">✓ M-Pesa Integration</span>
              </div>
              <div className="card-glass px-4 py-2 rounded-full border border-brand-white/20">
                <span className="text-brand-white text-sm font-subheading">✓ Community Verified</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Enhanced bottom wave decoration */}
      <div className="absolute bottom-0 left-0 right-0">
        <svg
          viewBox="0 0 1200 120"
          preserveAspectRatio="none"
          className="relative block w-full h-16 fill-brand-soft-gray"
        >
          <path d="M0,0V46.29c47.79,22.2,103.59,32.17,158,28,70.36-5.37,136.33-33.31,206.8-37.5C438.64,32.43,512.34,53.67,583,72.05c69.27,18,138.3,24.88,209.4,13.08,36.15-6,69.85-17.84,104.45-29.34C989.49,25,1113-14.29,1200,52.47V0Z" 
          opacity=".25"
        ></path>
          <path d="M0,0V15.81C13,36.92,27.64,56.86,47.69,72.05,99.41,111.27,165,111,224.58,91.58c31.15-10.15,60.09-26.07,89.67-39.8,40.92-19,84.73-46,130.83-49.67,36.26-2.85,70.9,9.42,98.6,31.56,31.77,25.39,62.32,62,103.63,73,40.44,10.79,81.35-6.69,119.13-24.28s75.16-39,116.92-43.05c59.73-5.85,113.28,22.88,168.9,38.84,30.2,8.66,59,6.17,87.09-7.5,22.43-10.89,48-26.93,60.65-49.24V0Z" 
          opacity=".5"
        ></path>
          <path d="M0,0V5.63C149.93,59,314.09,71.32,475.83,42.57c43-7.64,84.23-20.12,127.61-26.46,59-8.63,112.48,12.24,165.56,35.4C827.93,77.22,886,95.24,951.2,90c86.53-7,172.46-45.71,248.8-84.81V0Z"
        ></path>
        </svg>
      </div>
    </section>
  );
}