import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Package, 
  Building, 
  Truck, 
  MapPin, 
  Phone, 
  Mail,
  Clock,
  DollarSign,
  Coins,
  Heart,
  CheckCircle,
  Info,
  User,
  MessageSquare,
  Eye,
  EyeOff
} from 'lucide-react';
import { ClothingItem, CharityPartner, DonationData } from './DonationForm';

interface DonationConfirmationProps {
  selectedItems: ClothingItem[];
  selectedCharity: CharityPartner;
  donationData: DonationData;
  estimatedValue: number;
  estimatedTokens: number;
  onDataChange?: (data: Partial<DonationData>) => void;
  className?: string;
}

export const DonationConfirmation: React.FC<DonationConfirmationProps> = ({
  selectedItems,
  selectedCharity,
  donationData,
  estimatedValue,
  estimatedTokens,
  onDataChange,
  className = ''
}) => {
  const getDeliveryMethodDisplay = () => {
    switch (donationData.deliveryMethod) {
      case 'pickup':
        return {
          title: 'Charity Pickup',
          icon: Truck,
          description: 'The charity will collect items from your location',
          details: 'Pickup will be scheduled within 2-5 business days'
        };
      case 'delivery':
        return {
          title: 'Personal Delivery',
          icon: MapPin,
          description: 'You will deliver items to the charity',
          details: donationData.deliveryAddress ? 
            `${donationData.deliveryAddress.specificLocation}, ${donationData.deliveryAddress.town}, ${donationData.deliveryAddress.county}` :
            'Delivery address not specified'
        };
      case 'drop_off':
        return {
          title: 'Drop-off Point',
          icon: Building,
          description: 'Drop items at designated collection point',
          details: donationData.dropOffLocation ?
            `${donationData.dropOffLocation.name} - ${donationData.dropOffLocation.address}` :
            'Drop-off location not specified'
        };
      default:
        return {
          title: 'Unknown Method',
          icon: Truck,
          description: 'Delivery method not specified',
          details: ''
        };
    }
  };

  const getConditionColor = (condition: string) => {
    switch (condition.toLowerCase()) {
      case 'new': return 'bg-green-100 text-green-800';
      case 'like new': return 'bg-blue-100 text-blue-800';
      case 'good': return 'bg-yellow-100 text-yellow-800';
      case 'fair': return 'bg-orange-100 text-orange-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getCategoryColor = (category: string) => {
    const colors = [
      'bg-purple-100 text-purple-800',
      'bg-pink-100 text-pink-800',
      'bg-indigo-100 text-indigo-800',
      'bg-cyan-100 text-cyan-800',
      'bg-emerald-100 text-emerald-800'
    ];
    const index = category.length % colors.length;
    return colors[index];
  };

  const deliveryMethod = getDeliveryMethodDisplay();
  const DeliveryIcon = deliveryMethod.icon;

  return (
    <div className={className}>
      <div className="space-y-6">
        {/* Header */}
        <div>
          <h3 className="text-lg font-semibold mb-2">Confirm Your Donation</h3>
          <p className="text-gray-600">
            Please review all details before completing your donation
          </p>
        </div>

        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <Package className="h-5 w-5 text-blue-600" />
                <div>
                  <p className="text-sm text-gray-600">Items</p>
                  <p className="text-xl font-semibold">{selectedItems.length}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <DollarSign className="h-5 w-5 text-green-600" />
                <div>
                  <p className="text-sm text-gray-600">Total Value</p>
                  <p className="text-xl font-semibold">KES {estimatedValue.toLocaleString()}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <Coins className="h-5 w-5 text-yellow-600" />
                <div>
                  <p className="text-sm text-gray-600">Token Reward</p>
                  <p className="text-xl font-semibold">{estimatedTokens} tokens</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Items to Donate */}
        <Card>
          <CardHeader>
            <CardTitle className="text-base">Items to Donate</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {selectedItems.map((item) => (
                <div key={item._id} className="flex items-center gap-4 p-3 bg-gray-50 rounded-lg">
                  <div className="w-16 h-16 bg-gray-200 rounded-lg overflow-hidden flex-shrink-0">
                    {item.images && item.images.length > 0 ? (
                      <img
                        src={item.images[0]}
                        alt={item.title}
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <div className="w-full h-full flex items-center justify-center">
                        <Package className="h-6 w-6 text-gray-400" />
                      </div>
                    )}
                  </div>
                  
                  <div className="flex-1">
                    <h4 className="font-medium text-sm">{item.title}</h4>
                    <div className="flex flex-wrap gap-1 mt-1">
                      <Badge className={getCategoryColor(item.category)} variant="secondary">
                        {item.category}
                      </Badge>
                      <Badge className={getConditionColor(item.condition)} variant="secondary">
                        {item.condition}
                      </Badge>
                      <Badge variant="outline" className="text-xs">
                        Size {item.size}
                      </Badge>
                    </div>
                  </div>
                  
                  <div className="text-right">
                    <p className="font-medium text-sm">
                      KES {(item.estimatedValue || item.originalPrice || 0).toLocaleString()}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Charity Partner Details */}
        <Card>
          <CardHeader>
            <CardTitle className="text-base">Charity Partner</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-start gap-4">
              {selectedCharity.logo && (
                <img
                  src={selectedCharity.logo}
                  alt={`${selectedCharity.name} logo`}
                  className="w-16 h-16 rounded-lg object-cover flex-shrink-0"
                />
              )}
              
              <div className="flex-1">
                <div className="flex items-center gap-2 mb-2">
                  <h4 className="font-semibold">{selectedCharity.name}</h4>
                  <CheckCircle className="h-4 w-4 text-green-500" />
                  <Badge className="bg-blue-100 text-blue-800">
                    {selectedCharity.category}
                  </Badge>
                </div>
                
                <p className="text-sm text-gray-600 mb-3 line-clamp-2">
                  {selectedCharity.description}
                </p>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm">
                  <div className="flex items-center gap-2 text-gray-600">
                    <MapPin className="h-4 w-4" />
                    <span>{selectedCharity.contactInfo.address.town}, {selectedCharity.contactInfo.address.county}</span>
                  </div>
                  <div className="flex items-center gap-2 text-gray-600">
                    <Phone className="h-4 w-4" />
                    <span>{selectedCharity.contactInfo.phone}</span>
                  </div>
                  <div className="flex items-center gap-2 text-gray-600">
                    <Mail className="h-4 w-4" />
                    <span className="truncate">{selectedCharity.contactInfo.email}</span>
                  </div>
                  <div className="flex items-center gap-2 text-gray-600">
                    <Heart className="h-4 w-4" />
                    <span>{selectedCharity.stats.totalDonationsReceived} donations received</span>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Delivery Method */}
        <Card>
          <CardHeader>
            <CardTitle className="text-base">Delivery Method</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-start gap-4">
              <div className="p-3 bg-blue-100 rounded-lg">
                <DeliveryIcon className="h-6 w-6 text-blue-600" />
              </div>
              
              <div className="flex-1">
                <h4 className="font-medium mb-1">{deliveryMethod.title}</h4>
                <p className="text-sm text-gray-600 mb-2">{deliveryMethod.description}</p>
                
                {deliveryMethod.details && (
                  <div className="bg-gray-50 p-3 rounded-lg">
                    <p className="text-sm">{deliveryMethod.details}</p>
                    
                    {donationData.deliveryMethod === 'delivery' && donationData.deliveryAddress && (
                      <div className="mt-2 text-sm text-gray-600">
                        <div className="flex items-center gap-2">
                          <Phone className="h-3 w-3" />
                          <span>Contact: {donationData.deliveryAddress.contactPhone}</span>
                        </div>
                      </div>
                    )}
                    
                    {donationData.deliveryMethod === 'drop_off' && donationData.dropOffLocation && (
                      <div className="mt-2 text-sm text-gray-600">
                        <div className="flex items-center gap-2">
                          <Clock className="h-3 w-3" />
                          <span>Hours: {donationData.dropOffLocation.operatingHours}</span>
                        </div>
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Additional Information */}
        {(donationData.message || donationData.isAnonymous) && (
          <Card>
            <CardHeader>
              <CardTitle className="text-base">Additional Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              {donationData.message && (
                <div>
                  <div className="flex items-center gap-2 mb-2">
                    <MessageSquare className="h-4 w-4 text-gray-600" />
                    <span className="text-sm font-medium">Message to Charity</span>
                  </div>
                  <div className="bg-gray-50 p-3 rounded-lg">
                    <p className="text-sm">{donationData.message}</p>
                  </div>
                </div>
              )}
              
              {donationData.isAnonymous && (
                <div className="flex items-center gap-2">
                  <EyeOff className="h-4 w-4 text-gray-600" />
                  <span className="text-sm">This donation will be made anonymously</span>
                </div>
              )}
            </CardContent>
          </Card>
        )}

        {/* Important Information */}
        <Alert>
          <Info className="h-4 w-4" />
          <AlertDescription>
            <div className="space-y-2">
              <p className="font-medium">What happens next:</p>
              <ul className="text-sm space-y-1 ml-4">
                <li>• Your donation will be created and the charity will be notified</li>
                <li>• You'll receive 30% of your token reward immediately ({Math.round(estimatedTokens * 0.3)} tokens)</li>
                <li>• The remaining 70% will be awarded when the charity confirms receipt</li>
                <li>• You can track your donation progress in your dashboard</li>
                <li>• The charity may provide an impact report showing how your donation helped</li>
              </ul>
            </div>
          </AlertDescription>
        </Alert>

        {/* Auto-Accept Notice */}
        {selectedCharity.settings.autoAcceptDonations && (
          <Alert className="border-green-200 bg-green-50">
            <CheckCircle className="h-4 w-4 text-green-600" />
            <AlertDescription className="text-green-800">
              This charity automatically accepts donations, so your donation will be approved immediately 
              and you can proceed with the delivery arrangement.
            </AlertDescription>
          </Alert>
        )}
      </div>
    </div>
  );
};
