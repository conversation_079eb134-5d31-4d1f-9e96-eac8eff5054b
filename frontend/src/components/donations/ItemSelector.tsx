import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Package, 
  Search, 
  Filter, 
  Check, 
  X, 
  Coins, 
  DollarSign,
  Loader2,
  AlertCircle,
  Shirt,
  Eye
} from 'lucide-react';
import { ClothingItem } from './DonationForm';

interface ItemSelectorProps {
  selectedItems: ClothingItem[];
  onItemsSelected: (items: ClothingItem[]) => void;
  estimatedValue: number;
  estimatedTokens: number;
  className?: string;
}

interface ItemFilters {
  search: string;
  category: string;
  condition: string;
  minValue: number;
}

export const ItemSelector: React.FC<ItemSelectorProps> = ({
  selectedItems,
  onItemsSelected,
  estimatedValue,
  estimatedTokens,
  className = ''
}) => {
  const [availableItems, setAvailableItems] = useState<ClothingItem[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string>('');
  const [filters, setFilters] = useState<ItemFilters>({
    search: '',
    category: '',
    condition: '',
    minValue: 0
  });

  const CATEGORIES = [
    'Tops', 'Bottoms', 'Dresses', 'Outerwear', 'Shoes', 'Accessories',
    'Formal', 'Casual', 'Sportswear', 'Traditional', 'Children Clothing'
  ];

  const CONDITIONS = ['New', 'Like New', 'Good', 'Fair'];

  useEffect(() => {
    fetchUserItems();
  }, []);

  const fetchUserItems = async () => {
    setIsLoading(true);
    setError('');

    try {
      const queryParams = new URLSearchParams({
        status: 'available',
        owner: 'me', // This would be handled by the backend based on auth
        limit: '50'
      });

      const response = await fetch(`/api/clothing?${queryParams}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to fetch items');
      }

      const data = await response.json();
      setAvailableItems(data.data.items || []);
    } catch (error: any) {
      setError(error.message);
    } finally {
      setIsLoading(false);
    }
  };

  const filteredItems = availableItems.filter(item => {
    const matchesSearch = !filters.search || 
      item.title.toLowerCase().includes(filters.search.toLowerCase()) ||
      item.description.toLowerCase().includes(filters.search.toLowerCase());
    
    const matchesCategory = !filters.category || item.category === filters.category;
    const matchesCondition = !filters.condition || item.condition === filters.condition;
    const matchesValue = !filters.minValue || (item.estimatedValue || item.originalPrice || 0) >= filters.minValue;

    return matchesSearch && matchesCategory && matchesCondition && matchesValue;
  });

  const handleItemToggle = (item: ClothingItem) => {
    const isSelected = selectedItems.some(selected => selected._id === item._id);
    
    if (isSelected) {
      const newSelection = selectedItems.filter(selected => selected._id !== item._id);
      onItemsSelected(newSelection);
    } else {
      onItemsSelected([...selectedItems, item]);
    }
  };

  const handleSelectAll = () => {
    if (selectedItems.length === filteredItems.length) {
      onItemsSelected([]);
    } else {
      onItemsSelected(filteredItems);
    }
  };

  const getConditionColor = (condition: string) => {
    switch (condition.toLowerCase()) {
      case 'new': return 'bg-green-100 text-green-800';
      case 'like new': return 'bg-blue-100 text-blue-800';
      case 'good': return 'bg-yellow-100 text-yellow-800';
      case 'fair': return 'bg-orange-100 text-orange-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getCategoryColor = (category: string) => {
    const colors = [
      'bg-purple-100 text-purple-800',
      'bg-pink-100 text-pink-800',
      'bg-indigo-100 text-indigo-800',
      'bg-cyan-100 text-cyan-800',
      'bg-emerald-100 text-emerald-800'
    ];
    const index = category.length % colors.length;
    return colors[index];
  };

  return (
    <div className={className}>
      <div className="space-y-6">
        {/* Header */}
        <div>
          <h3 className="text-lg font-semibold mb-2">Select Items to Donate</h3>
          <p className="text-gray-600">Choose the clothing items you'd like to donate to charity</p>
        </div>

        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <Package className="h-5 w-5 text-blue-600" />
                <div>
                  <p className="text-sm text-gray-600">Items Selected</p>
                  <p className="text-xl font-semibold">{selectedItems.length}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <DollarSign className="h-5 w-5 text-green-600" />
                <div>
                  <p className="text-sm text-gray-600">Estimated Value</p>
                  <p className="text-xl font-semibold">KES {estimatedValue.toLocaleString()}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <Coins className="h-5 w-5 text-yellow-600" />
                <div>
                  <p className="text-sm text-gray-600">Token Reward</p>
                  <p className="text-xl font-semibold">{estimatedTokens} tokens</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filters */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-base">
              <Filter className="h-4 w-4" />
              Filter Items
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div>
                <Input
                  placeholder="Search items..."
                  value={filters.search}
                  onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
                  className="w-full"
                />
              </div>

              <select
                value={filters.category}
                onChange={(e) => setFilters(prev => ({ ...prev, category: e.target.value }))}
                className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">All Categories</option>
                {CATEGORIES.map(category => (
                  <option key={category} value={category}>{category}</option>
                ))}
              </select>

              <select
                value={filters.condition}
                onChange={(e) => setFilters(prev => ({ ...prev, condition: e.target.value }))}
                className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">All Conditions</option>
                {CONDITIONS.map(condition => (
                  <option key={condition} value={condition}>{condition}</option>
                ))}
              </select>

              <Input
                type="number"
                placeholder="Min value (KES)"
                value={filters.minValue || ''}
                onChange={(e) => setFilters(prev => ({ ...prev, minValue: parseInt(e.target.value) || 0 }))}
              />
            </div>
          </CardContent>
        </Card>

        {/* Error Display */}
        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* Items List */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="text-base">
                Your Available Items ({filteredItems.length})
              </CardTitle>
              {filteredItems.length > 0 && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleSelectAll}
                >
                  {selectedItems.length === filteredItems.length ? 'Deselect All' : 'Select All'}
                </Button>
              )}
            </div>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="flex items-center justify-center py-12">
                <Loader2 className="h-6 w-6 animate-spin mr-2" />
                <span>Loading your items...</span>
              </div>
            ) : filteredItems.length === 0 ? (
              <div className="text-center py-12">
                <Shirt className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500 mb-2">No items found</p>
                <p className="text-sm text-gray-400">
                  {availableItems.length === 0 
                    ? "You don't have any available items to donate"
                    : "Try adjusting your filters to see more items"
                  }
                </p>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {filteredItems.map((item) => {
                  const isSelected = selectedItems.some(selected => selected._id === item._id);
                  
                  return (
                    <Card 
                      key={item._id} 
                      className={`cursor-pointer transition-all duration-200 hover:shadow-md ${
                        isSelected ? 'ring-2 ring-blue-500 bg-blue-50' : ''
                      }`}
                      onClick={() => handleItemToggle(item)}
                    >
                      <CardContent className="p-4">
                        <div className="relative">
                          {/* Selection Indicator */}
                          <div className={`absolute top-2 right-2 w-6 h-6 rounded-full border-2 flex items-center justify-center ${
                            isSelected 
                              ? 'bg-blue-500 border-blue-500' 
                              : 'border-gray-300 bg-white'
                          }`}>
                            {isSelected && <Check className="h-4 w-4 text-white" />}
                          </div>

                          {/* Item Image */}
                          <div className="aspect-square bg-gray-100 rounded-lg mb-3 overflow-hidden">
                            {item.images && item.images.length > 0 ? (
                              <img
                                src={item.images[0]}
                                alt={item.title}
                                className="w-full h-full object-cover"
                              />
                            ) : (
                              <div className="w-full h-full flex items-center justify-center">
                                <Shirt className="h-8 w-8 text-gray-400" />
                              </div>
                            )}
                          </div>

                          {/* Item Details */}
                          <div className="space-y-2">
                            <h4 className="font-medium text-sm line-clamp-2">{item.title}</h4>
                            
                            <div className="flex flex-wrap gap-1">
                              <Badge className={getCategoryColor(item.category)} variant="secondary">
                                {item.category}
                              </Badge>
                              <Badge className={getConditionColor(item.condition)} variant="secondary">
                                {item.condition}
                              </Badge>
                            </div>

                            <div className="flex items-center justify-between text-sm">
                              <span className="text-gray-600">Size: {item.size}</span>
                              <span className="font-medium">
                                KES {(item.estimatedValue || item.originalPrice || 0).toLocaleString()}
                              </span>
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  );
                })}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Selection Summary */}
        {selectedItems.length > 0 && (
          <Alert>
            <Package className="h-4 w-4" />
            <AlertDescription>
              You have selected {selectedItems.length} item{selectedItems.length !== 1 ? 's' : ''} 
              with an estimated value of KES {estimatedValue.toLocaleString()}. 
              You'll earn approximately {estimatedTokens} tokens for this donation.
            </AlertDescription>
          </Alert>
        )}
      </div>
    </div>
  );
};
