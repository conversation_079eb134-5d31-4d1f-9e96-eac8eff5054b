'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { 
  Heart, 
  Recycle, 
  Scale, 
  Coins, 
  Package, 
  MapPin,
  Phone,
  CheckCircle,
  AlertCircle,
  Truck
} from 'lucide-react';

interface BulkDonationData {
  type: 'charity' | 'recycling';
  weight: number;
  description: string;
  clothingTypes: string[];
  condition: string;
  partnerId: string;
  deliveryMethod: 'pickup' | 'drop_off';
  contactInfo: {
    phone: string;
    address: string;
    preferredTime: string;
  };
  isAnonymous: boolean;
}

interface Partner {
  _id: string;
  name: string;
  type: 'charity' | 'recycling';
  location: string;
  description: string;
  acceptedItems: string[];
  rewardRate: number; // tokens per kg
}

interface BulkDonationFormProps {
  onSubmit: (data: BulkDonationData) => Promise<void>;
  loading?: boolean;
}

const clothingTypes = [
  'Shirts & Tops',
  'Pants & Jeans',
  'Dresses',
  'Jackets & Coats',
  'Shoes',
  'Accessories',
  'Underwear',
  'Children\'s Clothing',
  'Formal Wear',
  'Sportswear'
];

const conditions = [
  { value: 'excellent', label: 'Excellent - Like new', multiplier: 1.2 },
  { value: 'good', label: 'Good - Minor wear', multiplier: 1.0 },
  { value: 'fair', label: 'Fair - Visible wear', multiplier: 0.8 },
  { value: 'poor', label: 'Poor - For recycling only', multiplier: 0.5 }
];

export default function BulkDonationForm({ onSubmit, loading = false }: BulkDonationFormProps) {
  const [formData, setFormData] = useState<BulkDonationData>({
    type: 'charity',
    weight: 0,
    description: '',
    clothingTypes: [],
    condition: '',
    partnerId: '',
    deliveryMethod: 'pickup',
    contactInfo: {
      phone: '',
      address: '',
      preferredTime: ''
    },
    isAnonymous: false
  });

  const [partners, setPartners] = useState<Partner[]>([]);
  const [estimatedTokens, setEstimatedTokens] = useState(0);
  const [errors, setErrors] = useState<Record<string, string>>({});

  useEffect(() => {
    // Fetch partners based on type
    const fetchPartners = async () => {
      try {
        const token = localStorage.getItem('token');
        const endpoint = formData.type === 'charity' ? '/api/charity-partners' : '/api/recycling-centers';
        
        const response = await fetch(endpoint, {
          headers: {
            'Authorization': `Bearer ${token}`,
          },
        });

        if (response.ok) {
          const data = await response.json();
          setPartners(data.data || []);
        }
      } catch (error) {
        console.error('Error fetching partners:', error);
      }
    };

    fetchPartners();
  }, [formData.type]);

  useEffect(() => {
    // Calculate estimated tokens
    if (formData.weight > 0 && formData.condition) {
      const selectedPartner = partners.find(p => p._id === formData.partnerId);
      const baseRate = selectedPartner?.rewardRate || 10; // Default 10 tokens per kg
      const conditionMultiplier = conditions.find(c => c.value === formData.condition)?.multiplier || 1;
      
      const tokens = Math.round(formData.weight * baseRate * conditionMultiplier);
      setEstimatedTokens(tokens);
    } else {
      setEstimatedTokens(0);
    }
  }, [formData.weight, formData.condition, formData.partnerId, partners]);

  const handleInputChange = (field: string, value: any) => {
    if (field.startsWith('contactInfo.')) {
      const subField = field.split('.')[1];
      setFormData(prev => ({
        ...prev,
        contactInfo: {
          ...prev.contactInfo,
          [subField]: value
        }
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [field]: value
      }));
    }

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  const handleClothingTypeToggle = (type: string) => {
    setFormData(prev => ({
      ...prev,
      clothingTypes: prev.clothingTypes.includes(type)
        ? prev.clothingTypes.filter(t => t !== type)
        : [...prev.clothingTypes, type]
    }));
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (formData.weight <= 0) {
      newErrors.weight = 'Weight must be greater than 0';
    }

    if (formData.weight > 100) {
      newErrors.weight = 'Maximum weight is 100kg per donation';
    }

    if (!formData.condition) {
      newErrors.condition = 'Please select condition';
    }

    if (!formData.partnerId) {
      newErrors.partnerId = 'Please select a partner';
    }

    if (formData.clothingTypes.length === 0) {
      newErrors.clothingTypes = 'Please select at least one clothing type';
    }

    if (!formData.contactInfo.phone) {
      newErrors['contactInfo.phone'] = 'Phone number is required';
    }

    if (!formData.contactInfo.address) {
      newErrors['contactInfo.address'] = 'Address is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    try {
      await onSubmit(formData);
    } catch (error) {
      console.error('Error submitting bulk donation:', error);
    }
  };

  const selectedPartner = partners.find(p => p._id === formData.partnerId);

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          {formData.type === 'charity' ? (
            <Heart className="h-6 w-6 text-red-600" />
          ) : (
            <Recycle className="h-6 w-6 text-[#01796F]" />
          )}
          Bulk {formData.type === 'charity' ? 'Charity Donation' : 'Recycling'}
        </CardTitle>
        <CardDescription>
          Donate clothing in bulk and earn tokens based on weight and condition
        </CardDescription>
      </CardHeader>

      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Type Selection */}
          <div className="space-y-4">
            <Label className="text-base font-medium">Donation Type</Label>
            <div className="grid grid-cols-2 gap-4">
              <button
                type="button"
                onClick={() => handleInputChange('type', 'charity')}
                className={`p-4 border rounded-lg text-center transition-colors ${
                  formData.type === 'charity'
                    ? 'border-red-500 bg-red-50 text-red-700'
                    : 'border-gray-200 hover:border-red-300'
                }`}
              >
                <Heart className="h-6 w-6 mx-auto mb-2" />
                <p className="text-sm font-medium">Charity Donation</p>
                <p className="text-xs text-gray-600">Help those in need</p>
              </button>

              <button
                type="button"
                onClick={() => handleInputChange('type', 'recycling')}
                className={`p-4 border rounded-lg text-center transition-colors ${
                  formData.type === 'recycling'
                    ? 'border-[#01796F] bg-green-50 text-[#01796F]'
                    : 'border-gray-200 hover:border-green-300'
                }`}
              >
                <Recycle className="h-6 w-6 mx-auto mb-2" />
                <p className="text-sm font-medium">Recycling</p>
                <p className="text-xs text-gray-600">Environmental impact</p>
              </button>
            </div>
          </div>

          {/* Weight and Condition */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-2">
              <Label htmlFor="weight" className="flex items-center gap-2">
                <Scale className="h-4 w-4" />
                Weight (kg) *
              </Label>
              <Input
                id="weight"
                type="number"
                min="0.1"
                max="100"
                step="0.1"
                value={formData.weight || ''}
                onChange={(e) => handleInputChange('weight', parseFloat(e.target.value) || 0)}
                placeholder="e.g., 5.5"
                className={errors.weight ? 'border-red-500' : ''}
              />
              {errors.weight && <p className="text-sm text-red-500">{errors.weight}</p>}
              <p className="text-xs text-gray-500">Minimum 0.1kg, Maximum 100kg</p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="condition">Overall Condition *</Label>
              <Select
                value={formData.condition}
                onValueChange={(value) => handleInputChange('condition', value)}
              >
                <SelectTrigger className={errors.condition ? 'border-red-500' : ''}>
                  <SelectValue placeholder="Select condition" />
                </SelectTrigger>
                <SelectContent>
                  {conditions.map((condition) => (
                    <SelectItem key={condition.value} value={condition.value}>
                      <div className="flex items-center justify-between w-full">
                        <span>{condition.label}</span>
                        <Badge variant="secondary" className="ml-2">
                          {condition.multiplier}x
                        </Badge>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.condition && <p className="text-sm text-red-500">{errors.condition}</p>}
            </div>
          </div>
          {/* Clothing Types */}
          <div className="space-y-4">
            <Label className="text-base font-medium">Clothing Types *</Label>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
              {clothingTypes.map((type) => (
                <button
                  key={type}
                  type="button"
                  onClick={() => handleClothingTypeToggle(type)}
                  className={`p-3 border rounded-lg text-sm transition-colors ${
                    formData.clothingTypes.includes(type)
                      ? 'border-[#01796F] bg-green-50 text-[#01796F]'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  {type}
                </button>
              ))}
            </div>
            {errors.clothingTypes && <p className="text-sm text-red-500">{errors.clothingTypes}</p>}
          </div>

          {/* Partner Selection */}
          <div className="space-y-2">
            <Label htmlFor="partnerId">
              {formData.type === 'charity' ? 'Charity Partner' : 'Recycling Center'} *
            </Label>
            <Select
              value={formData.partnerId}
              onValueChange={(value) => handleInputChange('partnerId', value)}
            >
              <SelectTrigger className={errors.partnerId ? 'border-red-500' : ''}>
                <SelectValue placeholder={`Select ${formData.type === 'charity' ? 'charity' : 'recycling center'}`} />
              </SelectTrigger>
              <SelectContent>
                {partners.map((partner) => (
                  <SelectItem key={partner._id} value={partner._id}>
                    <div className="flex flex-col">
                      <span className="font-medium">{partner.name}</span>
                      <span className="text-xs text-gray-500">{partner.location}</span>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {errors.partnerId && <p className="text-sm text-red-500">{errors.partnerId}</p>}

            {selectedPartner && (
              <div className="p-3 bg-gray-50 rounded-lg">
                <p className="text-sm text-gray-700">{selectedPartner.description}</p>
                <div className="flex items-center gap-2 mt-2">
                  <Coins className="h-4 w-4 text-yellow-600" />
                  <span className="text-sm font-medium">{selectedPartner.rewardRate} tokens per kg</span>
                </div>
              </div>
            )}
          </div>

          {/* Description */}
          <div className="space-y-2">
            <Label htmlFor="description">Description (Optional)</Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={(e) => handleInputChange('description', e.target.value)}
              placeholder="Describe the items you're donating..."
              rows={3}
            />
          </div>

          {/* Contact Information */}
          <div className="space-y-4">
            <Label className="text-base font-medium">Contact Information</Label>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="phone" className="flex items-center gap-2">
                  <Phone className="h-4 w-4" />
                  Phone Number *
                </Label>
                <Input
                  id="phone"
                  type="tel"
                  value={formData.contactInfo.phone}
                  onChange={(e) => handleInputChange('contactInfo.phone', e.target.value)}
                  placeholder="e.g., +254712345678"
                  className={errors['contactInfo.phone'] ? 'border-red-500' : ''}
                />
                {errors['contactInfo.phone'] && <p className="text-sm text-red-500">{errors['contactInfo.phone']}</p>}
              </div>

              <div className="space-y-2">
                <Label htmlFor="preferredTime">Preferred Time</Label>
                <Input
                  id="preferredTime"
                  value={formData.contactInfo.preferredTime}
                  onChange={(e) => handleInputChange('contactInfo.preferredTime', e.target.value)}
                  placeholder="e.g., Weekdays 9AM-5PM"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="address" className="flex items-center gap-2">
                <MapPin className="h-4 w-4" />
                Pickup/Drop-off Address *
              </Label>
              <Textarea
                id="address"
                value={formData.contactInfo.address}
                onChange={(e) => handleInputChange('contactInfo.address', e.target.value)}
                placeholder="Enter your full address for pickup or drop-off location"
                rows={2}
                className={errors['contactInfo.address'] ? 'border-red-500' : ''}
              />
              {errors['contactInfo.address'] && <p className="text-sm text-red-500">{errors['contactInfo.address']}</p>}
            </div>
          </div>

          {/* Delivery Method */}
          <div className="space-y-4">
            <Label className="text-base font-medium">Delivery Method</Label>
            <div className="grid grid-cols-2 gap-4">
              <button
                type="button"
                onClick={() => handleInputChange('deliveryMethod', 'pickup')}
                className={`p-4 border rounded-lg text-center transition-colors ${
                  formData.deliveryMethod === 'pickup'
                    ? 'border-[#01796F] bg-green-50 text-[#01796F]'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
              >
                <Truck className="h-6 w-6 mx-auto mb-2" />
                <p className="text-sm font-medium">Pickup</p>
                <p className="text-xs text-gray-600">We'll collect from you</p>
              </button>

              <button
                type="button"
                onClick={() => handleInputChange('deliveryMethod', 'drop_off')}
                className={`p-4 border rounded-lg text-center transition-colors ${
                  formData.deliveryMethod === 'drop_off'
                    ? 'border-[#01796F] bg-green-50 text-[#01796F]'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
              >
                <Package className="h-6 w-6 mx-auto mb-2" />
                <p className="text-sm font-medium">Drop Off</p>
                <p className="text-xs text-gray-600">You bring to us</p>
              </button>
            </div>
          </div>

          {/* Estimated Rewards */}
          {estimatedTokens > 0 && (
            <Alert>
              <Coins className="h-4 w-4" />
              <AlertDescription>
                <div className="flex items-center justify-between">
                  <span>Estimated reward: <strong>{estimatedTokens} Pedi Tokens</strong></span>
                  <Badge variant="secondary">
                    {formData.weight}kg × {selectedPartner?.rewardRate || 10} tokens/kg
                  </Badge>
                </div>
              </AlertDescription>
            </Alert>
          )}

          {/* Submit Button */}
          <Button
            type="submit"
            disabled={loading || estimatedTokens === 0}
            className="w-full bg-[#01796F] hover:bg-[#032221]"
          >
            {loading ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Processing...
              </>
            ) : (
              <>
                <CheckCircle className="h-4 w-4 mr-2" />
                Submit Bulk Donation & Earn {estimatedTokens} Tokens
              </>
            )}
          </Button>
        </form>
      </CardContent>
    </Card>
  );
}
