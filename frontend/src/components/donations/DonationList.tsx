import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Package, 
  Search, 
  Filter, 
  Calendar,
  Building,
  DollarSign,
  Clock,
  CheckCircle,
  XCircle,
  Eye,
  Plus,
  Loader2,
  AlertCircle,
  Heart,
  ArrowUpDown
} from 'lucide-react';
import { DonationTracking } from './DonationTracking';

interface DonationSummary {
  _id: string;
  status: 'pending' | 'accepted' | 'completed' | 'cancelled';
  charityPartner: {
    name: string;
    category: string;
  };
  itemCount: number;
  estimatedValue: number;
  deliveryMethod: 'pickup' | 'delivery' | 'drop_off';
  isAnonymous: boolean;
  createdAt: string;
  updatedAt: string;
}

interface DonationListProps {
  onCreateNew?: () => void;
  className?: string;
}

interface DonationFilters {
  search: string;
  status: string;
  charityCategory: string;
  deliveryMethod: string;
  dateRange: string;
}

export const DonationList: React.FC<DonationListProps> = ({
  onCreateNew,
  className = ''
}) => {
  const [donations, setDonations] = useState<DonationSummary[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string>('');
  const [selectedDonation, setSelectedDonation] = useState<string | null>(null);
  const [filters, setFilters] = useState<DonationFilters>({
    search: '',
    status: '',
    charityCategory: '',
    deliveryMethod: '',
    dateRange: ''
  });
  const [sortBy, setSortBy] = useState<'date' | 'value' | 'status'>('date');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 0
  });

  const STATUS_OPTIONS = [
    { value: '', label: 'All Statuses' },
    { value: 'pending', label: 'Pending' },
    { value: 'accepted', label: 'Accepted' },
    { value: 'completed', label: 'Completed' },
    { value: 'cancelled', label: 'Cancelled' }
  ];

  const CHARITY_CATEGORIES = [
    'Children & Youth', 'Women Empowerment', 'Education', 'Healthcare',
    'Environment', 'Community Development', 'Disaster Relief', 'Elderly Care',
    'Disability Support', 'General Welfare'
  ];

  const DELIVERY_METHODS = [
    { value: '', label: 'All Methods' },
    { value: 'pickup', label: 'Charity Pickup' },
    { value: 'delivery', label: 'Personal Delivery' },
    { value: 'drop_off', label: 'Drop-off Point' }
  ];

  useEffect(() => {
    fetchDonations();
  }, [filters, sortBy, sortOrder, pagination.page]);

  const fetchDonations = async () => {
    setIsLoading(true);
    setError('');

    try {
      const queryParams = new URLSearchParams({
        page: pagination.page.toString(),
        limit: pagination.limit.toString(),
        sortBy,
        sortOrder
      });

      // Add filters
      if (filters.search) queryParams.append('search', filters.search);
      if (filters.status) queryParams.append('status', filters.status);
      if (filters.charityCategory) queryParams.append('charityCategory', filters.charityCategory);
      if (filters.deliveryMethod) queryParams.append('deliveryMethod', filters.deliveryMethod);
      if (filters.dateRange) queryParams.append('dateRange', filters.dateRange);

      const response = await fetch(`/api/donations?${queryParams}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to fetch donations');
      }

      const data = await response.json();
      setDonations(data.data.donations || []);
      setPagination(prev => ({
        ...prev,
        total: data.data.pagination?.total || 0,
        totalPages: data.data.pagination?.totalPages || 0
      }));
    } catch (error: any) {
      setError(error.message);
    } finally {
      setIsLoading(false);
    }
  };

  const handleFilterChange = (key: keyof DonationFilters, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }));
    setPagination(prev => ({ ...prev, page: 1 })); // Reset to first page
  };

  const handleSort = (field: 'date' | 'value' | 'status') => {
    if (sortBy === field) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(field);
      setSortOrder('desc');
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'accepted': return 'bg-blue-100 text-blue-800';
      case 'completed': return 'bg-green-100 text-green-800';
      case 'cancelled': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending': return Clock;
      case 'accepted': return CheckCircle;
      case 'completed': return CheckCircle;
      case 'cancelled': return XCircle;
      default: return Clock;
    }
  };

  const getDeliveryMethodDisplay = (method: string) => {
    switch (method) {
      case 'pickup': return 'Charity Pickup';
      case 'delivery': return 'Personal Delivery';
      case 'drop_off': return 'Drop-off Point';
      default: return method;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-KE', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  if (selectedDonation) {
    return (
      <DonationTracking
        donationId={selectedDonation}
        onClose={() => setSelectedDonation(null)}
        className={className}
      />
    );
  }

  return (
    <div className={className}>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold">My Donations</h2>
            <p className="text-gray-600">Track and manage your charitable donations</p>
          </div>
          {onCreateNew && (
            <Button onClick={onCreateNew} className="bg-red-600 hover:bg-red-700">
              <Plus className="h-4 w-4 mr-2" />
              New Donation
            </Button>
          )}
        </div>

        {/* Filters */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-base">
              <Filter className="h-4 w-4" />
              Filter & Search
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-4">
              <Input
                placeholder="Search donations..."
                value={filters.search}
                onChange={(e) => handleFilterChange('search', e.target.value)}
              />

              <select
                value={filters.status}
                onChange={(e) => handleFilterChange('status', e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                {STATUS_OPTIONS.map(option => (
                  <option key={option.value} value={option.value}>{option.label}</option>
                ))}
              </select>

              <select
                value={filters.charityCategory}
                onChange={(e) => handleFilterChange('charityCategory', e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">All Categories</option>
                {CHARITY_CATEGORIES.map(category => (
                  <option key={category} value={category}>{category}</option>
                ))}
              </select>

              <select
                value={filters.deliveryMethod}
                onChange={(e) => handleFilterChange('deliveryMethod', e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                {DELIVERY_METHODS.map(method => (
                  <option key={method.value} value={method.value}>{method.label}</option>
                ))}
              </select>

              <select
                value={filters.dateRange}
                onChange={(e) => handleFilterChange('dateRange', e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">All Time</option>
                <option value="today">Today</option>
                <option value="week">This Week</option>
                <option value="month">This Month</option>
                <option value="quarter">This Quarter</option>
                <option value="year">This Year</option>
              </select>
            </div>
          </CardContent>
        </Card>

        {/* Error Display */}
        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* Donations List */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="text-base">
                Donations ({pagination.total})
              </CardTitle>
              <div className="flex items-center gap-2">
                <span className="text-sm text-gray-600">Sort by:</span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleSort('date')}
                  className={sortBy === 'date' ? 'bg-blue-50' : ''}
                >
                  Date
                  {sortBy === 'date' && (
                    <ArrowUpDown className="h-3 w-3 ml-1" />
                  )}
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleSort('value')}
                  className={sortBy === 'value' ? 'bg-blue-50' : ''}
                >
                  Value
                  {sortBy === 'value' && (
                    <ArrowUpDown className="h-3 w-3 ml-1" />
                  )}
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleSort('status')}
                  className={sortBy === 'status' ? 'bg-blue-50' : ''}
                >
                  Status
                  {sortBy === 'status' && (
                    <ArrowUpDown className="h-3 w-3 ml-1" />
                  )}
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="flex items-center justify-center py-12">
                <Loader2 className="h-6 w-6 animate-spin mr-2" />
                <span>Loading donations...</span>
              </div>
            ) : donations.length === 0 ? (
              <div className="text-center py-12">
                <Heart className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500 mb-2">No donations found</p>
                <p className="text-sm text-gray-400 mb-4">
                  {Object.values(filters).some(f => f) 
                    ? "Try adjusting your filters to see more donations"
                    : "Start making a difference by creating your first donation"
                  }
                </p>
                {onCreateNew && (
                  <Button onClick={onCreateNew} className="bg-red-600 hover:bg-red-700">
                    <Plus className="h-4 w-4 mr-2" />
                    Create First Donation
                  </Button>
                )}
              </div>
            ) : (
              <div className="space-y-4">
                {donations.map((donation) => {
                  const StatusIcon = getStatusIcon(donation.status);
                  
                  return (
                    <Card 
                      key={donation._id}
                      className="cursor-pointer hover:shadow-md transition-shadow"
                      onClick={() => setSelectedDonation(donation._id)}
                    >
                      <CardContent className="p-4">
                        <div className="flex items-center justify-between">
                          <div className="flex items-start gap-4 flex-1">
                            <div className={`p-2 rounded-lg ${
                              donation.status === 'completed' ? 'bg-green-100' :
                              donation.status === 'accepted' ? 'bg-blue-100' :
                              donation.status === 'cancelled' ? 'bg-red-100' : 'bg-yellow-100'
                            }`}>
                              <StatusIcon className={`h-5 w-5 ${
                                donation.status === 'completed' ? 'text-green-600' :
                                donation.status === 'accepted' ? 'text-blue-600' :
                                donation.status === 'cancelled' ? 'text-red-600' : 'text-yellow-600'
                              }`} />
                            </div>
                            
                            <div className="flex-1">
                              <div className="flex items-center gap-2 mb-2">
                                <h4 className="font-semibold">{donation.charityPartner.name}</h4>
                                <Badge className={getStatusColor(donation.status)}>
                                  {donation.status.charAt(0).toUpperCase() + donation.status.slice(1)}
                                </Badge>
                                {donation.isAnonymous && (
                                  <Badge variant="outline" className="text-xs">
                                    Anonymous
                                  </Badge>
                                )}
                              </div>
                              
                              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm text-gray-600">
                                <div className="flex items-center gap-1">
                                  <Package className="h-3 w-3" />
                                  <span>{donation.itemCount} items</span>
                                </div>
                                <div className="flex items-center gap-1">
                                  <DollarSign className="h-3 w-3" />
                                  <span>KES {donation.estimatedValue.toLocaleString()}</span>
                                </div>
                                <div className="flex items-center gap-1">
                                  <Building className="h-3 w-3" />
                                  <span>{donation.charityPartner.category}</span>
                                </div>
                                <div className="flex items-center gap-1">
                                  <Calendar className="h-3 w-3" />
                                  <span>{formatDate(donation.createdAt)}</span>
                                </div>
                              </div>
                              
                              <p className="text-xs text-gray-500 mt-2">
                                {getDeliveryMethodDisplay(donation.deliveryMethod)}
                              </p>
                            </div>
                          </div>
                          
                          <Button variant="outline" size="sm">
                            <Eye className="h-4 w-4 mr-2" />
                            View Details
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  );
                })}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Pagination */}
        {pagination.totalPages > 1 && (
          <div className="flex items-center justify-between">
            <p className="text-sm text-gray-600">
              Showing {((pagination.page - 1) * pagination.limit) + 1} to{' '}
              {Math.min(pagination.page * pagination.limit, pagination.total)} of{' '}
              {pagination.total} donations
            </p>
            
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setPagination(prev => ({ ...prev, page: prev.page - 1 }))}
                disabled={pagination.page === 1}
              >
                Previous
              </Button>
              
              <span className="text-sm">
                Page {pagination.page} of {pagination.totalPages}
              </span>
              
              <Button
                variant="outline"
                size="sm"
                onClick={() => setPagination(prev => ({ ...prev, page: prev.page + 1 }))}
                disabled={pagination.page === pagination.totalPages}
              >
                Next
              </Button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
