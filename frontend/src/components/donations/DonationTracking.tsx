import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Package, 
  Clock, 
  CheckCircle, 
  XCircle, 
  AlertTriangle,
  Truck,
  Building,
  MapPin,
  Calendar,
  User,
  MessageSquare,
  Star,
  Heart,
  Coins,
  DollarSign,
  Phone,
  Mail,
  Loader2,
  RefreshCw,
  Eye,
  Download
} from 'lucide-react';

interface DonationTransaction {
  _id: string;
  type: 'donation';
  status: 'pending' | 'accepted' | 'completed' | 'cancelled';
  initiator: {
    _id: string;
    firstName: string;
    lastName: string;
    profilePicture?: string;
  };
  initiatorItems: {
    _id: string;
    title: string;
    category: string;
    condition: string;
    images: string[];
    estimatedValue?: number;
  }[];
  charityPartner: {
    name: string;
    id: string;
    category: string;
  };
  deliveryMethod: 'pickup' | 'delivery' | 'drop_off';
  deliveryAddress?: {
    county: string;
    town: string;
    specificLocation: string;
    contactPhone: string;
  };
  estimatedValue?: number;
  isAnonymous: boolean;
  timeline: {
    status: string;
    timestamp: string;
    note?: string;
    updatedBy: string;
  }[];
  completionData?: {
    receiptNumber?: string;
    itemsReceived: {
      itemId: string;
      condition: string;
      estimatedValue: number;
    }[];
    impactStatement?: string;
    beneficiaryCount?: number;
  };
  createdAt: string;
  updatedAt: string;
}

interface DonationTrackingProps {
  donationId: string;
  onClose?: () => void;
  className?: string;
}

export const DonationTracking: React.FC<DonationTrackingProps> = ({
  donationId,
  onClose,
  className = ''
}) => {
  const [donation, setDonation] = useState<DonationTransaction | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string>('');
  const [isRefreshing, setIsRefreshing] = useState(false);

  useEffect(() => {
    fetchDonation();
  }, [donationId]);

  const fetchDonation = async (refresh = false) => {
    if (refresh) {
      setIsRefreshing(true);
    } else {
      setIsLoading(true);
    }
    setError('');

    try {
      const response = await fetch(`/api/donations/${donationId}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to fetch donation details');
      }

      const data = await response.json();
      setDonation(data.data);
    } catch (error: any) {
      setError(error.message);
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'accepted': return 'bg-blue-100 text-blue-800';
      case 'completed': return 'bg-green-100 text-green-800';
      case 'cancelled': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending': return Clock;
      case 'accepted': return CheckCircle;
      case 'completed': return CheckCircle;
      case 'cancelled': return XCircle;
      default: return AlertTriangle;
    }
  };

  const getDeliveryMethodDisplay = (method: string) => {
    switch (method) {
      case 'pickup': return { title: 'Charity Pickup', icon: Truck };
      case 'delivery': return { title: 'Personal Delivery', icon: MapPin };
      case 'drop_off': return { title: 'Drop-off Point', icon: Building };
      default: return { title: 'Unknown', icon: Package };
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-KE', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const handleRefresh = () => {
    fetchDonation(true);
  };

  if (isLoading) {
    return (
      <Card className={className}>
        <CardContent className="flex items-center justify-center py-12">
          <Loader2 className="h-6 w-6 animate-spin mr-2" />
          <span>Loading donation details...</span>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className={className}>
        <CardContent className="py-12">
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
          <div className="flex justify-center mt-4">
            <Button onClick={() => fetchDonation()} variant="outline">
              Try Again
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!donation) {
    return (
      <Card className={className}>
        <CardContent className="text-center py-12">
          <Package className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-500">Donation not found</p>
        </CardContent>
      </Card>
    );
  }

  const StatusIcon = getStatusIcon(donation.status);
  const deliveryMethod = getDeliveryMethodDisplay(donation.deliveryMethod);
  const DeliveryIcon = deliveryMethod.icon;

  return (
    <div className={className}>
      <div className="space-y-6">
        {/* Header */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="flex items-center gap-2">
                  <Heart className="h-6 w-6 text-red-500" />
                  Donation Tracking
                </CardTitle>
                <CardDescription>
                  Donation ID: {donation._id}
                </CardDescription>
              </div>
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleRefresh}
                  disabled={isRefreshing}
                >
                  {isRefreshing ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : (
                    <RefreshCw className="h-4 w-4" />
                  )}
                </Button>
                {onClose && (
                  <Button variant="outline" size="sm" onClick={onClose}>
                    Close
                  </Button>
                )}
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-4">
              <div className={`p-3 rounded-lg ${
                donation.status === 'completed' ? 'bg-green-100' :
                donation.status === 'accepted' ? 'bg-blue-100' :
                donation.status === 'cancelled' ? 'bg-red-100' : 'bg-yellow-100'
              }`}>
                <StatusIcon className={`h-6 w-6 ${
                  donation.status === 'completed' ? 'text-green-600' :
                  donation.status === 'accepted' ? 'text-blue-600' :
                  donation.status === 'cancelled' ? 'text-red-600' : 'text-yellow-600'
                }`} />
              </div>
              <div>
                <Badge className={getStatusColor(donation.status)}>
                  {donation.status.charAt(0).toUpperCase() + donation.status.slice(1)}
                </Badge>
                <p className="text-sm text-gray-600 mt-1">
                  Created on {formatDate(donation.createdAt)}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Summary Stats */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <Package className="h-5 w-5 text-blue-600" />
                <div>
                  <p className="text-sm text-gray-600">Items Donated</p>
                  <p className="text-xl font-semibold">{donation.initiatorItems.length}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <DollarSign className="h-5 w-5 text-green-600" />
                <div>
                  <p className="text-sm text-gray-600">Total Value</p>
                  <p className="text-xl font-semibold">
                    KES {(donation.estimatedValue || 0).toLocaleString()}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <Building className="h-5 w-5 text-purple-600" />
                <div>
                  <p className="text-sm text-gray-600">Charity Partner</p>
                  <p className="text-lg font-semibold">{donation.charityPartner.name}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Timeline */}
        <Card>
          <CardHeader>
            <CardTitle className="text-base">Donation Timeline</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {donation.timeline.map((event, index) => (
                <div key={index} className="flex items-start gap-4">
                  <div className={`w-3 h-3 rounded-full mt-2 ${
                    event.status === 'completed' ? 'bg-green-500' :
                    event.status === 'accepted' ? 'bg-blue-500' :
                    event.status === 'cancelled' ? 'bg-red-500' : 'bg-yellow-500'
                  }`} />
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <span className="font-medium text-sm">
                        {event.status.charAt(0).toUpperCase() + event.status.slice(1)}
                      </span>
                      <span className="text-xs text-gray-500">
                        {formatDate(event.timestamp)}
                      </span>
                    </div>
                    {event.note && (
                      <p className="text-sm text-gray-600">{event.note}</p>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Donated Items */}
        <Card>
          <CardHeader>
            <CardTitle className="text-base">Donated Items</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-3">
              {donation.initiatorItems.map((item) => (
                <div key={item._id} className="flex items-center gap-4 p-3 bg-gray-50 rounded-lg">
                  <div className="w-12 h-12 bg-gray-200 rounded-lg overflow-hidden flex-shrink-0">
                    {item.images && item.images.length > 0 ? (
                      <img
                        src={item.images[0]}
                        alt={item.title}
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <div className="w-full h-full flex items-center justify-center">
                        <Package className="h-4 w-4 text-gray-400" />
                      </div>
                    )}
                  </div>
                  
                  <div className="flex-1">
                    <h4 className="font-medium text-sm">{item.title}</h4>
                    <div className="flex gap-2 mt-1">
                      <Badge variant="outline" className="text-xs">
                        {item.category}
                      </Badge>
                      <Badge variant="outline" className="text-xs">
                        {item.condition}
                      </Badge>
                    </div>
                  </div>
                  
                  <div className="text-right">
                    <p className="font-medium text-sm">
                      KES {(item.estimatedValue || 0).toLocaleString()}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Delivery Information */}
        <Card>
          <CardHeader>
            <CardTitle className="text-base">Delivery Information</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-start gap-4">
              <div className="p-3 bg-blue-100 rounded-lg">
                <DeliveryIcon className="h-6 w-6 text-blue-600" />
              </div>
              <div className="flex-1">
                <h4 className="font-medium mb-2">{deliveryMethod.title}</h4>
                
                {donation.deliveryMethod === 'delivery' && donation.deliveryAddress && (
                  <div className="space-y-1 text-sm text-gray-600">
                    <p>{donation.deliveryAddress.specificLocation}</p>
                    <p>{donation.deliveryAddress.town}, {donation.deliveryAddress.county}</p>
                    <div className="flex items-center gap-2">
                      <Phone className="h-3 w-3" />
                      <span>{donation.deliveryAddress.contactPhone}</span>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Completion Details */}
        {donation.status === 'completed' && donation.completionData && (
          <Card>
            <CardHeader>
              <CardTitle className="text-base">Donation Impact</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {donation.completionData.receiptNumber && (
                <div>
                  <span className="text-sm font-medium text-gray-700">Receipt Number: </span>
                  <span className="text-sm">{donation.completionData.receiptNumber}</span>
                </div>
              )}
              
              {donation.completionData.beneficiaryCount && (
                <div>
                  <span className="text-sm font-medium text-gray-700">People Helped: </span>
                  <span className="text-sm">{donation.completionData.beneficiaryCount}</span>
                </div>
              )}
              
              {donation.completionData.impactStatement && (
                <div>
                  <span className="text-sm font-medium text-gray-700 block mb-1">Impact Statement:</span>
                  <div className="bg-green-50 p-3 rounded-lg">
                    <p className="text-sm text-green-800">{donation.completionData.impactStatement}</p>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        )}

        {/* Actions */}
        {donation.status === 'completed' && (
          <Card>
            <CardHeader>
              <CardTitle className="text-base">Actions</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex flex-wrap gap-2">
                <Button variant="outline" size="sm">
                  <Download className="h-4 w-4 mr-2" />
                  Download Receipt
                </Button>
                <Button variant="outline" size="sm">
                  <Star className="h-4 w-4 mr-2" />
                  Rate Experience
                </Button>
                <Button variant="outline" size="sm">
                  <MessageSquare className="h-4 w-4 mr-2" />
                  Contact Charity
                </Button>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
};
