import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { 
  Heart, 
  Package, 
  Building, 
  Truck, 
  CheckCircle, 
  ArrowRight, 
  ArrowLeft,
  Loader2,
  AlertCircle
} from 'lucide-react';
import { ItemSelector } from './ItemSelector';
import { CharityPartnerSelector } from './CharityPartnerSelector';
import { DeliveryMethodSelector } from './DeliveryMethodSelector';
import { DonationConfirmation } from './DonationConfirmation';

export interface ClothingItem {
  _id: string;
  title: string;
  description: string;
  category: string;
  size: string;
  color: string;
  condition: string;
  images: string[];
  originalPrice?: number;
  estimatedValue?: number;
  owner: {
    _id: string;
    firstName: string;
    lastName: string;
  };
}

export interface CharityPartner {
  _id: string;
  name: string;
  description: string;
  category: string;
  contactInfo: {
    email: string;
    phone: string;
    address: {
      county: string;
      town: string;
      specificLocation: string;
    };
  };
  logo?: string;
  acceptedItemTypes: string[];
  requirements: {
    condition: string[];
    categories: string[];
    notes?: string;
  };
  stats: {
    totalDonationsReceived: number;
    totalBeneficiaries: number;
    averageRating: number;
  };
  settings: {
    autoAcceptDonations: boolean;
    requireQualityCheck: boolean;
    providesPickup: boolean;
    maxPickupDistance: number;
    issuesReceipts: boolean;
  };
}

export interface DonationData {
  items: string[];
  charityPartnerId: string;
  deliveryMethod: 'pickup' | 'delivery' | 'drop_off';
  deliveryAddress?: {
    county: string;
    town: string;
    specificLocation: string;
    contactPhone: string;
  };
  dropOffLocation?: {
    name: string;
    address: string;
    operatingHours: string;
  };
  message?: string;
  isAnonymous?: boolean;
}

interface DonationFormProps {
  onDonationSuccess?: (donation: any) => void;
  onCancel?: () => void;
  preSelectedItems?: ClothingItem[];
  preSelectedCharity?: CharityPartner;
  className?: string;
}

type DonationStep = 'items' | 'charity' | 'delivery' | 'confirmation';

export const DonationForm: React.FC<DonationFormProps> = ({
  onDonationSuccess,
  onCancel,
  preSelectedItems = [],
  preSelectedCharity,
  className = ''
}) => {
  const [currentStep, setCurrentStep] = useState<DonationStep>('items');
  const [selectedItems, setSelectedItems] = useState<ClothingItem[]>(preSelectedItems);
  const [selectedCharity, setSelectedCharity] = useState<CharityPartner | null>(preSelectedCharity || null);
  const [donationData, setDonationData] = useState<Partial<DonationData>>({
    items: preSelectedItems.map(item => item._id),
    deliveryMethod: 'pickup',
    isAnonymous: false
  });
  
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string>('');
  const [estimatedValue, setEstimatedValue] = useState<number>(0);
  const [estimatedTokens, setEstimatedTokens] = useState<number>(0);

  useEffect(() => {
    if (preSelectedItems.length > 0) {
      setSelectedItems(preSelectedItems);
      setDonationData(prev => ({
        ...prev,
        items: preSelectedItems.map(item => item._id)
      }));
    }
  }, [preSelectedItems]);

  useEffect(() => {
    if (preSelectedCharity) {
      setSelectedCharity(preSelectedCharity);
      setDonationData(prev => ({
        ...prev,
        charityPartnerId: preSelectedCharity._id
      }));
    }
  }, [preSelectedCharity]);

  // Calculate estimated value and tokens when items change
  useEffect(() => {
    const totalValue = selectedItems.reduce((sum, item) => {
      return sum + (item.estimatedValue || item.originalPrice || 0);
    }, 0);
    setEstimatedValue(totalValue);
    
    // Estimate token reward (this would typically come from the backend)
    const tokenReward = Math.round(totalValue * 0.1 + selectedItems.length * 5);
    setEstimatedTokens(tokenReward);
  }, [selectedItems]);

  const handleItemsSelected = (items: ClothingItem[]) => {
    setSelectedItems(items);
    setDonationData(prev => ({
      ...prev,
      items: items.map(item => item._id)
    }));
  };

  const handleCharitySelected = (charity: CharityPartner) => {
    setSelectedCharity(charity);
    setDonationData(prev => ({
      ...prev,
      charityPartnerId: charity._id
    }));
  };

  const handleDeliveryConfigured = (deliveryConfig: Partial<DonationData>) => {
    setDonationData(prev => ({
      ...prev,
      ...deliveryConfig
    }));
  };

  const canProceedToNextStep = () => {
    switch (currentStep) {
      case 'items':
        return selectedItems.length > 0;
      case 'charity':
        return selectedCharity !== null;
      case 'delivery':
        return donationData.deliveryMethod && 
               (donationData.deliveryMethod === 'pickup' || 
                donationData.deliveryAddress || 
                donationData.dropOffLocation);
      case 'confirmation':
        return true;
      default:
        return false;
    }
  };

  const handleNext = () => {
    if (!canProceedToNextStep()) return;
    
    const steps: DonationStep[] = ['items', 'charity', 'delivery', 'confirmation'];
    const currentIndex = steps.indexOf(currentStep);
    if (currentIndex < steps.length - 1) {
      setCurrentStep(steps[currentIndex + 1]);
    }
  };

  const handlePrevious = () => {
    const steps: DonationStep[] = ['items', 'charity', 'delivery', 'confirmation'];
    const currentIndex = steps.indexOf(currentStep);
    if (currentIndex > 0) {
      setCurrentStep(steps[currentIndex - 1]);
    }
  };

  const handleSubmitDonation = async () => {
    if (!donationData.items || !donationData.charityPartnerId || !donationData.deliveryMethod) {
      setError('Please complete all required fields');
      return;
    }

    setIsSubmitting(true);
    setError('');

    try {
      const response = await fetch('/api/donations', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}` // Adjust based on your auth implementation
        },
        body: JSON.stringify(donationData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to create donation');
      }

      const result = await response.json();
      onDonationSuccess?.(result.data);
    } catch (error: any) {
      setError(error.message);
    } finally {
      setIsSubmitting(false);
    }
  };

  const getStepIcon = (step: DonationStep) => {
    switch (step) {
      case 'items': return Package;
      case 'charity': return Building;
      case 'delivery': return Truck;
      case 'confirmation': return CheckCircle;
    }
  };

  const getStepTitle = (step: DonationStep) => {
    switch (step) {
      case 'items': return 'Select Items';
      case 'charity': return 'Choose Charity';
      case 'delivery': return 'Delivery Method';
      case 'confirmation': return 'Confirm Donation';
    }
  };

  const steps: DonationStep[] = ['items', 'charity', 'delivery', 'confirmation'];

  return (
    <div className={`max-w-4xl mx-auto ${className}`}>
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Heart className="h-6 w-6 text-red-500" />
            Make a Donation
          </CardTitle>
          <CardDescription>
            Share your clothing with those in need and make a positive impact in your community
          </CardDescription>
        </CardHeader>

        <CardContent>
          {/* Progress Steps */}
          <div className="flex items-center justify-between mb-8">
            {steps.map((step, index) => {
              const Icon = getStepIcon(step);
              const isActive = step === currentStep;
              const isCompleted = steps.indexOf(currentStep) > index;
              
              return (
                <div key={step} className="flex items-center">
                  <div className={`flex items-center gap-2 ${
                    isActive ? 'text-blue-600' : isCompleted ? 'text-green-600' : 'text-gray-400'
                  }`}>
                    <div className={`w-10 h-10 rounded-full flex items-center justify-center border-2 ${
                      isActive ? 'border-blue-600 bg-blue-50' : 
                      isCompleted ? 'border-green-600 bg-green-50' : 
                      'border-gray-300 bg-gray-50'
                    }`}>
                      <Icon className="h-5 w-5" />
                    </div>
                    <span className="font-medium">{getStepTitle(step)}</span>
                  </div>
                  {index < steps.length - 1 && (
                    <ArrowRight className="h-4 w-4 text-gray-400 mx-4" />
                  )}
                </div>
              );
            })}
          </div>

          {/* Error Display */}
          {error && (
            <Alert variant="destructive" className="mb-6">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {/* Step Content */}
          <div className="min-h-[400px]">
            {currentStep === 'items' && (
              <ItemSelector
                selectedItems={selectedItems}
                onItemsSelected={handleItemsSelected}
                estimatedValue={estimatedValue}
                estimatedTokens={estimatedTokens}
              />
            )}

            {currentStep === 'charity' && (
              <CharityPartnerSelector
                selectedCharity={selectedCharity}
                onCharitySelected={handleCharitySelected}
                selectedItems={selectedItems}
              />
            )}

            {currentStep === 'delivery' && (
              <DeliveryMethodSelector
                charityPartner={selectedCharity}
                deliveryData={donationData}
                onDeliveryConfigured={handleDeliveryConfigured}
              />
            )}

            {currentStep === 'confirmation' && (
              <DonationConfirmation
                selectedItems={selectedItems}
                selectedCharity={selectedCharity!}
                donationData={donationData as DonationData}
                estimatedValue={estimatedValue}
                estimatedTokens={estimatedTokens}
                onDataChange={setDonationData}
              />
            )}
          </div>

          {/* Navigation Buttons */}
          <div className="flex justify-between mt-8">
            <div>
              {currentStep !== 'items' && (
                <Button variant="outline" onClick={handlePrevious}>
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Previous
                </Button>
              )}
              {currentStep === 'items' && onCancel && (
                <Button variant="outline" onClick={onCancel}>
                  Cancel
                </Button>
              )}
            </div>

            <div>
              {currentStep !== 'confirmation' ? (
                <Button 
                  onClick={handleNext} 
                  disabled={!canProceedToNextStep()}
                >
                  Next
                  <ArrowRight className="h-4 w-4 ml-2" />
                </Button>
              ) : (
                <Button 
                  onClick={handleSubmitDonation} 
                  disabled={isSubmitting || !canProceedToNextStep()}
                  className="bg-red-600 hover:bg-red-700"
                >
                  {isSubmitting ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Creating Donation...
                    </>
                  ) : (
                    <>
                      <Heart className="h-4 w-4 mr-2" />
                      Complete Donation
                    </>
                  )}
                </Button>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
