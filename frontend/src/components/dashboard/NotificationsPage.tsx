'use client';

import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Bell, 
  Settings, 
  BarChart3, 
  MessageSquare,
  ArrowLeft,
  ExternalLink
} from 'lucide-react';
import {
  NotificationList,
  NotificationPreferences,
  NotificationStats,
  type BaseNotification
} from '@/components/notifications';

interface NotificationsPageProps {
  onNavigate?: (page: string) => void;
}

export default function NotificationsPage({ onNavigate }: NotificationsPageProps) {
  const [selectedNotification, setSelectedNotification] = useState<BaseNotification | null>(null);
  const [activeTab, setActiveTab] = useState('notifications');

  const handleNotificationClick = (notification: BaseNotification) => {
    setSelectedNotification(notification);
  };

  const handleActionClick = (action: { 
    type: string; 
    label: string; 
    url?: string; 
    data?: any; 
  }) => {
    if (action.url) {
      if (action.url.startsWith('/dashboard/')) {
        // Internal dashboard navigation
        const page = action.url.replace('/dashboard/', '').replace('/', '');
        onNavigate?.(page);
      } else {
        // External navigation - open in new tab
        window.open(action.url, '_blank');
      }
    }
  };

  const handleBackToList = () => {
    setSelectedNotification(null);
  };

  if (selectedNotification) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Button 
            variant="ghost" 
            onClick={handleBackToList}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            Back to Notifications
          </Button>
        </div>

        <Card>
          <CardHeader>
            <div className="flex items-start justify-between">
              <div className="space-y-2">
                <CardTitle className="flex items-center gap-2">
                  <Bell className="h-5 w-5" />
                  {selectedNotification.title}
                </CardTitle>
                <CardDescription>
                  {new Date(selectedNotification.createdAt).toLocaleString()}
                </CardDescription>
              </div>
              <Badge variant={selectedNotification.isRead ? "secondary" : "default"}>
                {selectedNotification.isRead ? 'Read' : 'Unread'}
              </Badge>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="prose max-w-none">
              <p>{selectedNotification.message}</p>
            </div>

            {selectedNotification.actions && selectedNotification.actions.length > 0 && (
              <div className="space-y-2">
                <h4 className="font-medium">Actions</h4>
                <div className="flex flex-wrap gap-2">
                  {selectedNotification.actions.map((action, index) => (
                    <Button
                      key={index}
                      variant={index === 0 ? "default" : "outline"}
                      size="sm"
                      onClick={() => handleActionClick(action)}
                      className="flex items-center gap-2"
                    >
                      {action.url && !action.url.startsWith('/dashboard/') && (
                        <ExternalLink className="h-3 w-3" />
                      )}
                      {action.label}
                    </Button>
                  ))}
                </div>
              </div>
            )}

            {selectedNotification.metadata && (
              <div className="space-y-2">
                <h4 className="font-medium">Details</h4>
                <div className="bg-gray-50 p-3 rounded-lg">
                  <pre className="text-sm text-gray-600 whitespace-pre-wrap">
                    {JSON.stringify(selectedNotification.metadata, null, 2)}
                  </pre>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold text-gray-900 mb-2">Notifications</h1>
        <p className="text-gray-600">
          Manage your notification preferences and view your notification history
        </p>
      </div>

      {/* Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="notifications" className="flex items-center gap-2">
            <Bell className="h-4 w-4" />
            Notifications
          </TabsTrigger>
          <TabsTrigger value="preferences" className="flex items-center gap-2">
            <Settings className="h-4 w-4" />
            Preferences
          </TabsTrigger>
          <TabsTrigger value="analytics" className="flex items-center gap-2">
            <BarChart3 className="h-4 w-4" />
            Analytics
          </TabsTrigger>
        </TabsList>

        <TabsContent value="notifications" className="space-y-6">
          <NotificationList
            onNotificationClick={handleNotificationClick}
            showFilters={true}
            pageSize={20}
            autoRefresh={true}
          />
        </TabsContent>

        <TabsContent value="preferences" className="space-y-6">
          <NotificationPreferences />
        </TabsContent>

        <TabsContent value="analytics" className="space-y-6">
          <NotificationStats />
        </TabsContent>
      </Tabs>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MessageSquare className="h-5 w-5" />
            Quick Actions
          </CardTitle>
          <CardDescription>
            Common notification management tasks
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Button variant="outline" className="h-auto p-4 flex flex-col items-center gap-2">
              <Bell className="h-6 w-6 text-blue-600" />
              <span className="font-medium">Mark All Read</span>
              <span className="text-xs text-gray-500">Clear unread notifications</span>
            </Button>
            
            <Button variant="outline" className="h-auto p-4 flex flex-col items-center gap-2">
              <Settings className="h-6 w-6 text-gray-600" />
              <span className="font-medium">Notification Settings</span>
              <span className="text-xs text-gray-500">Customize preferences</span>
            </Button>
            
            <Button 
              variant="outline" 
              className="h-auto p-4 flex flex-col items-center gap-2"
              onClick={() => onNavigate?.('messages')}
            >
              <MessageSquare className="h-6 w-6 text-green-600" />
              <span className="font-medium">View Messages</span>
              <span className="text-xs text-gray-500">Check your inbox</span>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
