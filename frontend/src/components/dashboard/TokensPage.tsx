'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Coins, 
  CreditCard, 
  Smartphone, 
  DollarSign,
  CheckCircle,
  AlertCircle,
  TrendingUp,
  History,
  Gift
} from 'lucide-react';

interface TokenPackage {
  id: string;
  tokens: number;
  priceKES: number;
  priceUSD: number;
  bonus: number;
  popular?: boolean;
  description: string;
}

interface Transaction {
  _id: string;
  type: 'purchase' | 'earned' | 'spent';
  amount: number;
  description: string;
  createdAt: string;
  status: 'completed' | 'pending' | 'failed';
}

const tokenPackages: TokenPackage[] = [
  {
    id: 'starter',
    tokens: 100,
    priceKES: 500,
    priceUSD: 5,
    bonus: 0,
    description: 'Perfect for getting started'
  },
  {
    id: 'popular',
    tokens: 250,
    priceKES: 1200,
    priceUSD: 12,
    bonus: 25,
    popular: true,
    description: 'Most popular choice'
  },
  {
    id: 'value',
    tokens: 500,
    priceKES: 2300,
    priceUSD: 23,
    bonus: 75,
    description: 'Best value for money'
  },
  {
    id: 'premium',
    tokens: 1000,
    priceKES: 4500,
    priceUSD: 45,
    bonus: 200,
    description: 'For power users'
  }
];

export default function TokensPage() {
  const [user, setUser] = useState<any>(null);
  const [selectedPackage, setSelectedPackage] = useState<TokenPackage | null>(null);
  const [paymentMethod, setPaymentMethod] = useState<'mpesa' | 'card'>('mpesa');
  const [phoneNumber, setPhoneNumber] = useState('');
  const [loading, setLoading] = useState(false);
  const [transactions, setTransactions] = useState<Transaction[]>([]);

  useEffect(() => {
    const userData = localStorage.getItem('user');
    if (userData) {
      setUser(JSON.parse(userData));
    }
    fetchTransactions();
  }, []);

  const fetchTransactions = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch('/api/tokens/history', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setTransactions(data.data || []);
      }
    } catch (error) {
      console.error('Error fetching transactions:', error);
    }
  };

  const handlePurchase = async () => {
    if (!selectedPackage) return;

    setLoading(true);
    try {
      const token = localStorage.getItem('token');
      const response = await fetch('/api/token-purchases', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({
          packageId: selectedPackage.id,
          paymentMethod,
          phoneNumber: paymentMethod === 'mpesa' ? phoneNumber : undefined,
        }),
      });

      if (response.ok) {
        alert('Purchase initiated! You will receive an M-Pesa prompt shortly.');
        setSelectedPackage(null);
        setPhoneNumber('');
        fetchTransactions();
      } else {
        const error = await response.json();
        alert(error.message || 'Purchase failed');
      }
    } catch (error) {
      console.error('Error purchasing tokens:', error);
      alert('Purchase failed. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  if (!user) {
    return <div>Loading...</div>;
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Pedi Tokens</h1>
          <p className="text-gray-600">Purchase tokens to exchange for clothing items</p>
        </div>
        <div className="text-right">
          <div className="flex items-center gap-2 text-2xl font-bold text-yellow-600">
            <Coins className="h-6 w-6" />
            {user.pediTokens}
          </div>
          <p className="text-sm text-gray-500">Current Balance</p>
        </div>
      </div>

      <Tabs defaultValue="purchase" className="space-y-6">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="purchase">Purchase Tokens</TabsTrigger>
          <TabsTrigger value="history">Transaction History</TabsTrigger>
        </TabsList>

        <TabsContent value="purchase" className="space-y-6">
          {/* Token Packages */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {tokenPackages.map((pkg) => (
              <Card 
                key={pkg.id}
                className={`cursor-pointer transition-all duration-200 hover:shadow-lg ${
                  selectedPackage?.id === pkg.id 
                    ? 'ring-2 ring-[#01796F] border-[#01796F]' 
                    : 'hover:border-[#01796F]'
                } ${pkg.popular ? 'relative' : ''}`}
                onClick={() => setSelectedPackage(pkg)}
              >
                {pkg.popular && (
                  <Badge className="absolute -top-2 left-1/2 transform -translate-x-1/2 bg-[#01796F]">
                    Most Popular
                  </Badge>
                )}
                <CardHeader className="text-center pb-2">
                  <CardTitle className="text-2xl font-bold text-[#032221]">
                    {pkg.tokens} {pkg.bonus > 0 && `+ ${pkg.bonus}`}
                  </CardTitle>
                  <CardDescription>{pkg.description}</CardDescription>
                </CardHeader>
                <CardContent className="text-center">
                  <div className="text-3xl font-bold text-gray-900 mb-2">
                    KES {pkg.priceKES.toLocaleString()}
                  </div>
                  <div className="text-sm text-gray-500 mb-4">
                    ${pkg.priceUSD} USD
                  </div>
                  {pkg.bonus > 0 && (
                    <div className="flex items-center justify-center gap-1 text-green-600 text-sm">
                      <Gift className="h-4 w-4" />
                      +{pkg.bonus} bonus tokens
                    </div>
                  )}
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Payment Section */}
          {selectedPackage && (
            <Card>
              <CardHeader>
                <CardTitle>Complete Purchase</CardTitle>
                <CardDescription>
                  You're purchasing {selectedPackage.tokens} 
                  {selectedPackage.bonus > 0 && ` + ${selectedPackage.bonus} bonus`} tokens 
                  for KES {selectedPackage.priceKES.toLocaleString()}
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Payment Method Selection */}
                <div className="space-y-3">
                  <Label>Payment Method</Label>
                  <div className="grid grid-cols-2 gap-4">
                    <Button
                      variant={paymentMethod === 'mpesa' ? 'default' : 'outline'}
                      onClick={() => setPaymentMethod('mpesa')}
                      className="flex items-center gap-2 h-12"
                    >
                      <Smartphone className="h-5 w-5" />
                      M-Pesa
                    </Button>
                    <Button
                      variant={paymentMethod === 'card' ? 'default' : 'outline'}
                      onClick={() => setPaymentMethod('card')}
                      className="flex items-center gap-2 h-12"
                    >
                      <CreditCard className="h-5 w-5" />
                      Card
                    </Button>
                  </div>
                </div>

                {/* M-Pesa Phone Number */}
                {paymentMethod === 'mpesa' && (
                  <div className="space-y-2">
                    <Label htmlFor="phone">M-Pesa Phone Number</Label>
                    <Input
                      id="phone"
                      type="tel"
                      placeholder="254712345678"
                      value={phoneNumber}
                      onChange={(e) => setPhoneNumber(e.target.value)}
                    />
                  </div>
                )}

                {/* Purchase Button */}
                <Button
                  onClick={handlePurchase}
                  disabled={loading || (paymentMethod === 'mpesa' && !phoneNumber)}
                  className="w-full bg-[#01796F] hover:bg-[#032221]"
                >
                  {loading ? 'Processing...' : `Purchase for KES ${selectedPackage.priceKES.toLocaleString()}`}
                </Button>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="history" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <History className="h-5 w-5" />
                Transaction History
              </CardTitle>
            </CardHeader>
            <CardContent>
              {transactions.length === 0 ? (
                <p className="text-center text-gray-500 py-8">No transactions yet</p>
              ) : (
                <div className="space-y-4">
                  {transactions.map((transaction) => (
                    <div key={transaction._id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center gap-3">
                        <div className={`p-2 rounded-full ${
                          transaction.type === 'purchase' ? 'bg-blue-100 text-blue-600' :
                          transaction.type === 'earned' ? 'bg-green-100 text-green-600' :
                          'bg-red-100 text-red-600'
                        }`}>
                          {transaction.type === 'purchase' ? <DollarSign className="h-4 w-4" /> :
                           transaction.type === 'earned' ? <TrendingUp className="h-4 w-4" /> :
                           <Coins className="h-4 w-4" />}
                        </div>
                        <div>
                          <p className="font-medium">{transaction.description}</p>
                          <p className="text-sm text-gray-500">
                            {new Date(transaction.createdAt).toLocaleDateString()}
                          </p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className={`font-bold ${
                          transaction.type === 'spent' ? 'text-red-600' : 'text-green-600'
                        }`}>
                          {transaction.type === 'spent' ? '-' : '+'}{transaction.amount} tokens
                        </p>
                        <Badge variant={
                          transaction.status === 'completed' ? 'default' :
                          transaction.status === 'pending' ? 'secondary' : 'destructive'
                        }>
                          {transaction.status}
                        </Badge>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
