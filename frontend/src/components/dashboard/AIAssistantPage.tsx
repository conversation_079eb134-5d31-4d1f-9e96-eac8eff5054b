'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, Ta<PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { 
  Bot, 
  Sparkles, 
  Shirt, 
  BarChart3, 
  Lightbulb,
  Wand2,
  Heart,
  TrendingUp,
  MessageCircle,
  Recycle,
  Star,
  Users,
  Target
} from 'lucide-react';
import { 
  FashionAssistant, 
  OutfitSuggestions, 
  WardrobeAnalysis, 
  StylingTips 
} from '@/components/ai';

interface AIAssistantPageProps {
  onNavigate?: (page: string) => void;
}

export default function AIAssistantPage({ onNavigate }: AIAssistantPageProps) {
  const [activeTab, setActiveTab] = useState('assistant');

  const features = [
    {
      icon: Bo<PERSON>,
      title: 'AI Fashion Assistant',
      description: 'Get personalized fashion advice and styling tips',
      color: 'text-purple-600',
      bgColor: 'bg-purple-50',
      tab: 'assistant'
    },
    {
      icon: Shirt,
      title: 'Outfit Suggestions',
      description: 'Discover new outfit combinations from your wardrobe',
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
      tab: 'outfits'
    },
    {
      icon: BarChart3,
      title: 'Wardrobe Analysis',
      description: 'Analyze your clothing patterns and sustainability score',
      color: 'text-green-600',
      bgColor: 'bg-green-50',
      tab: 'analysis'
    },
    {
      icon: Lightbulb,
      title: 'Styling Tips',
      description: 'Learn professional styling techniques and trends',
      color: 'text-yellow-600',
      bgColor: 'bg-yellow-50',
      tab: 'tips'
    }
  ];

  const stats = [
    {
      icon: MessageCircle,
      label: 'Conversations',
      value: '24',
      description: 'AI chat sessions'
    },
    {
      icon: Star,
      label: 'Recommendations',
      value: '156',
      description: 'Outfit suggestions'
    },
    {
      icon: TrendingUp,
      label: 'Style Score',
      value: '8.5',
      description: 'Your fashion rating'
    },
    {
      icon: Target,
      label: 'Goals Met',
      value: '12',
      description: 'Styling objectives'
    }
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center">
        <div className="flex items-center justify-center gap-3 mb-4">
          <div className="p-3 bg-purple-100 rounded-full">
            <Bot className="h-8 w-8 text-purple-600" />
          </div>
          <h1 className="text-3xl font-bold text-gray-900">AI Fashion Assistant</h1>
        </div>
        <p className="text-gray-600 max-w-2xl mx-auto">
          Get personalized fashion advice, outfit suggestions, and styling tips powered by AI. 
          Enhance your sustainable fashion journey with intelligent recommendations.
        </p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {stats.map((stat, index) => {
          const Icon = stat.icon;
          return (
            <Card key={index} className="hover:shadow-lg transition-shadow duration-200">
              <CardContent className="p-6">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-brand-light-green rounded-lg">
                    <Icon className="h-5 w-5 text-brand-pine-green" />
                  </div>
                  <div>
                    <p className="text-2xl font-bold text-brand-black">{stat.value}</p>
                    <p className="text-sm font-medium text-brand-black">{stat.label}</p>
                    <p className="text-xs text-brand-black">{stat.description}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Feature Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {features.map((feature, index) => {
          const Icon = feature.icon;
          return (
            <Card 
              key={index} 
              className="hover:shadow-lg transition-shadow duration-200 cursor-pointer"
              onClick={() => setActiveTab(feature.tab)}
            >
              <CardContent className="p-6">
                <div className="flex items-start gap-4">
                  <div className={`p-3 ${feature.bgColor} rounded-lg`}>
                    <Icon className={`h-6 w-6 ${feature.color}`} />
                  </div>
                  <div className="flex-1">
                    <h3 className="font-semibold text-gray-900 mb-2">{feature.title}</h3>
                    <p className="text-gray-600 text-sm">{feature.description}</p>
                    <Button 
                      variant="outline" 
                      size="sm" 
                      className="mt-3"
                      onClick={(e) => {
                        e.stopPropagation();
                        setActiveTab(feature.tab);
                      }}
                    >
                      Try Now
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* AI Assistant Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="assistant" className="flex items-center gap-2">
            <Bot className="h-4 w-4" />
            Assistant
          </TabsTrigger>
          <TabsTrigger value="outfits" className="flex items-center gap-2">
            <Shirt className="h-4 w-4" />
            Outfits
          </TabsTrigger>
          <TabsTrigger value="analysis" className="flex items-center gap-2">
            <BarChart3 className="h-4 w-4" />
            Analysis
          </TabsTrigger>
          <TabsTrigger value="tips" className="flex items-center gap-2">
            <Lightbulb className="h-4 w-4" />
            Tips
          </TabsTrigger>
        </TabsList>

        <TabsContent value="assistant" className="space-y-6">
          <FashionAssistant />
        </TabsContent>

        <TabsContent value="outfits" className="space-y-6">
          <OutfitSuggestions />
        </TabsContent>

        <TabsContent value="analysis" className="space-y-6">
          <WardrobeAnalysis />
        </TabsContent>

        <TabsContent value="tips" className="space-y-6">
          <StylingTips />
        </TabsContent>
      </Tabs>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Wand2 className="h-5 w-5" />
            Quick Actions
          </CardTitle>
          <CardDescription>
            Common styling tasks and recommendations
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Button variant="outline" className="h-auto p-4 flex flex-col items-center gap-2">
              <Recycle className="h-6 w-6 text-green-600" />
              <span className="font-medium">Sustainable Styling</span>
              <span className="text-xs text-gray-500">Eco-friendly outfit ideas</span>
            </Button>
            
            <Button variant="outline" className="h-auto p-4 flex flex-col items-center gap-2">
              <Heart className="h-6 w-6 text-red-600" />
              <span className="font-medium">Favorite Looks</span>
              <span className="text-xs text-gray-500">Save and organize outfits</span>
            </Button>
            
            <Button 
              variant="outline" 
              className="h-auto p-4 flex flex-col items-center gap-2"
              onClick={() => onNavigate?.('browse')}
            >
              <Users className="h-6 w-6 text-blue-600" />
              <span className="font-medium">Community Trends</span>
              <span className="text-xs text-gray-500">See what others are wearing</span>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
