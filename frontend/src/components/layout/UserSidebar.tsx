'use client';

import React from 'react';
import Link from 'next/link';
import { usePathname, useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Home,
  Shirt,
  Recycle,
  Heart,
  Coins,
  MessageCircle,
  Bot,
  Bell,
  User,
  Settings,
  LogOut,
  ShoppingBag,
  TrendingUp
} from 'lucide-react';

interface UserSidebarProps {
  user: {
    firstName: string;
    lastName: string;
    pediTokens: number;
    profilePicture?: string;
  };
  onLogout: () => void;
  currentPage?: string;
  onNavigate?: (page: string) => void;
}

// All dashboard navigation (internal component-based routing)
const dashboardNavigation = [
  {
    name: 'Dashboard',
    page: 'home',
    icon: Home,
    description: 'Overview & stats'
  },
  {
    name: 'Browse Items',
    page: 'browse',
    icon: ShoppingBag,
    description: 'Find clothing to swap'
  },
  {
    name: 'List Item',
    page: 'list-item',
    icon: Shirt,
    description: 'List your clothing'
  },
  {
    name: 'My Listings',
    page: 'my-listings',
    icon: TrendingUp,
    description: 'Manage your items'
  },
  {
    name: 'Messages',
    page: 'messages',
    icon: MessageCircle,
    description: 'Chat with users'
  },
  {
    name: 'Donations',
    page: 'donations',
    icon: Heart,
    description: 'Donate to charity'
  },
  {
    name: 'Recycling',
    page: 'recycling',
    icon: Recycle,
    description: 'Recycle old clothes'
  },
  {
    name: 'Tokens',
    page: 'tokens',
    icon: Coins,
    description: 'Buy & manage tokens'
  },
  {
    name: 'AI Assistant',
    page: 'ai-assistant',
    icon: Bot,
    description: 'Fashion advice'
  },
  {
    name: 'Notifications',
    page: 'notifications',
    icon: Bell,
    description: 'Your updates'
  },
];

export default function UserSidebar({ user, onLogout, currentPage, onNavigate }: UserSidebarProps) {
  const pathname = usePathname();
  const router = useRouter();

  const isDashboardRoute = pathname?.startsWith('/dashboard');

  return (
    <div className="flex flex-col h-full bg-white border-r border-gray-200">
      {/* Logo & Brand */}
      <div className="flex items-center gap-3 p-6 border-b border-gray-200">
        <img 
          src="/favicon.ico" 
          alt="Pedi Logo" 
          className="h-8 w-8"
        />
        <div>
          <h1 className="text-xl font-bold text-[#032221]">Pedi</h1>
          <p className="text-xs text-gray-500">Sustainable Fashion</p>
        </div>
      </div>

      {/* User Profile */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center gap-3">
          {user.profilePicture ? (
            <img 
              src={user.profilePicture} 
              alt="Profile" 
              className="h-10 w-10 rounded-full"
            />
          ) : (
            <div className="h-10 w-10 rounded-full bg-[#01796F] flex items-center justify-center">
              <User className="h-5 w-5 text-white" />
            </div>
          )}
          <div className="flex-1 min-w-0">
            <p className="text-sm font-medium text-gray-900 truncate">
              {user.firstName} {user.lastName}
            </p>
            <div className="flex items-center gap-1">
              <Coins className="h-3 w-3 text-yellow-600" />
              <span className="text-xs text-gray-500">{user.pediTokens} tokens</span>
            </div>
          </div>
        </div>
      </div>

      {/* Navigation */}
      <nav className="flex-1 p-4 space-y-1 overflow-y-auto">
        {/* Dashboard Internal Navigation */}
        {dashboardNavigation.map((item) => {
          const isActive = currentPage === item.page;
          const Icon = item.icon;

          return (
            <button
              key={item.name}
              onClick={() => {
                if (isDashboardRoute && onNavigate) {
                  onNavigate(item.page);
                } else {
                  router.push(`/dashboard?page=${item.page}`);
                }
              }}
              className={`
                w-full flex items-center gap-3 px-3 py-2 rounded-lg text-sm font-medium transition-colors text-left
                ${isActive
                  ? 'bg-[#01796F] text-white'
                  : 'text-gray-700 hover:bg-gray-100 hover:text-[#032221]'
                }
              `}
            >
              <Icon className="h-4 w-4 flex-shrink-0" />
              <div className="flex-1 min-w-0">
                <div className="truncate">{item.name}</div>
                <div className={`text-xs truncate ${isActive ? 'text-green-100' : 'text-gray-500'}`}>
                  {item.description}
                </div>
              </div>
            </button>
          );
        })}
      </nav>

      {/* Bottom Actions */}
      <div className="p-4 border-t border-gray-200 space-y-2">
        <Button
          variant="outline"
          size="sm"
          className="w-full justify-start"
          onClick={() => {
            if (isDashboardRoute && onNavigate) {
              onNavigate('profile');
            } else {
              router.push('/dashboard?page=profile');
            }
          }}
        >
          <Settings className="h-4 w-4 mr-2" />
          Settings
        </Button>
        
        <Button
          variant="outline"
          size="sm"
          className="w-full justify-start text-red-600 hover:text-red-700 hover:bg-red-50"
          onClick={onLogout}
        >
          <LogOut className="h-4 w-4 mr-2" />
          Logout
        </Button>
      </div>
    </div>
  );
}
