'use client';

import React from 'react';
import Link from 'next/link';
import { usePathname, useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Home,
  Users,
  Shirt,
  Heart,
  Building,
  BarChart3,
  Settings,
  Shield,
  AlertTriangle,
  MessageSquare,
  Coins,
  TrendingUp,
  Package,
  LogOut,
  User
} from 'lucide-react';

interface AdminSidebarProps {
  user: {
    firstName: string;
    lastName: string;
    role: string;
    profilePicture?: string;
  };
  onLogout: () => void;
}

const navigation = [
  {
    name: 'Dashboard',
    href: '/admin/dashboard',
    icon: Home,
    description: 'Admin overview'
  },
  {
    name: 'Users',
    href: '/admin/users',
    icon: Users,
    description: 'Manage users'
  },
  {
    name: 'Clothing Items',
    href: '/admin/clothing',
    icon: Shirt,
    description: 'Manage listings'
  },
  {
    name: 'Donations',
    href: '/admin/donations',
    icon: Heart,
    description: 'Track donations'
  },
  {
    name: 'Charity Partners',
    href: '/admin/charity-partners',
    icon: Building,
    description: 'Manage partners'
  },
  {
    name: 'Recycling Centers',
    href: '/admin/recycling-centers',
    icon: Package,
    description: 'Manage centers'
  },
  {
    name: 'Analytics',
    href: '/admin/analytics',
    icon: BarChart3,
    description: 'Platform stats'
  },
  {
    name: 'Token Management',
    href: '/admin/tokens',
    icon: Coins,
    description: 'Token system'
  },
  {
    name: 'Reports',
    href: '/admin/reports',
    icon: TrendingUp,
    description: 'Generate reports'
  },
  {
    name: 'Messages',
    href: '/admin/messages',
    icon: MessageSquare,
    description: 'Platform messages'
  },
  {
    name: 'Moderation',
    href: '/admin/moderation',
    icon: Shield,
    description: 'Content moderation'
  },
  {
    name: 'System Alerts',
    href: '/admin/alerts',
    icon: AlertTriangle,
    description: 'System issues'
  }
];

export default function AdminSidebar({ user, onLogout }: AdminSidebarProps) {
  const pathname = usePathname();
  const router = useRouter();

  return (
    <div className="flex flex-col h-full bg-white border-r border-gray-200">
      {/* Logo & Brand */}
      <div className="flex items-center gap-3 p-6 border-b border-gray-200">
        <img 
          src="/favicon.ico" 
          alt="Pedi Logo" 
          className="h-8 w-8"
        />
        <div>
          <h1 className="text-xl font-bold text-[#032221]">Pedi Admin</h1>
          <p className="text-xs text-gray-500">Management Portal</p>
        </div>
      </div>

      {/* Admin Profile */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center gap-3">
          {user.profilePicture ? (
            <img 
              src={user.profilePicture} 
              alt="Profile" 
              className="h-10 w-10 rounded-full"
            />
          ) : (
            <div className="h-10 w-10 rounded-full bg-[#032221] flex items-center justify-center">
              <Shield className="h-5 w-5 text-white" />
            </div>
          )}
          <div className="flex-1 min-w-0">
            <p className="text-sm font-medium text-gray-900 truncate">
              {user.firstName} {user.lastName}
            </p>
            <Badge variant="secondary" className="text-xs">
              {user.role.toUpperCase()}
            </Badge>
          </div>
        </div>
      </div>

      {/* Navigation */}
      <nav className="flex-1 p-4 space-y-1 overflow-y-auto">
        {navigation.map((item) => {
          const isActive = pathname === item.href;
          const Icon = item.icon;
          
          return (
            <Link
              key={item.name}
              href={item.href}
              className={`
                flex items-center gap-3 px-3 py-2 rounded-lg text-sm font-medium transition-colors
                ${isActive
                  ? 'bg-[#032221] text-white'
                  : 'text-brand-black hover:bg-brand-light-green hover:text-brand-dark-green'
                }
              `}
            >
              <Icon className="h-4 w-4 flex-shrink-0" />
              <div className="flex-1 min-w-0">
                <div className="truncate">{item.name}</div>
                <div className={`text-xs truncate ${isActive ? 'text-gray-300' : 'text-brand-black/70'}`}>
                  {item.description}
                </div>
              </div>
            </Link>
          );
        })}
      </nav>

      {/* Bottom Actions */}
      <div className="p-4 border-t border-gray-200 space-y-2">
        <Button
          variant="outline"
          size="sm"
          className="w-full justify-start"
          onClick={() => router.push('/admin/settings')}
        >
          <Settings className="h-4 w-4 mr-2" />
          Admin Settings
        </Button>
        
        <Button
          variant="outline"
          size="sm"
          className="w-full justify-start text-red-600 hover:text-red-700 hover:bg-red-50"
          onClick={onLogout}
        >
          <LogOut className="h-4 w-4 mr-2" />
          Logout
        </Button>
      </div>
    </div>
  );
}
