'use client';

import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import UserSidebar from './UserSidebar';
import AdminSidebar from './AdminSidebar';

interface User {
  id: string;
  phoneNumber: string;
  email?: string;
  firstName: string;
  lastName: string;
  fullName: string;
  profilePicture?: string;
  location: {
    county: string;
    town: string;
  };
  isVerified: boolean;
  role: string;
  pediTokens: number;
  rating: {
    average: number;
    count: number;
  };
  sustainabilityScore: number;
  totalDonations: number;
  totalSwaps: number;
  joinedAt: string;
  lastActive: string;
}

interface DashboardLayoutProps {
  children: React.ReactNode;
  currentPage?: string;
  onNavigate?: (page: string) => void;
}

export default function DashboardLayout({ children, currentPage, onNavigate }: DashboardLayoutProps) {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const router = useRouter();

  useEffect(() => {
    // Check if user is logged in
    const token = localStorage.getItem('token');
    const userData = localStorage.getItem('user');

    if (!token || !userData) {
      router.push('/auth');
      return;
    }

    try {
      const parsedUser = JSON.parse(userData);
      setUser(parsedUser);
    } catch (error) {
      console.error('Error parsing user data:', error);
      router.push('/auth');
    } finally {
      setLoading(false);
    }
  }, [router]);

  const handleLogout = () => {
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    router.push('/auth');
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#01796F] mx-auto mb-4"></div>
          <p className="text-gray-600">Loading your dashboard...</p>
        </div>
      </div>
    );
  }

  if (!user) {
    return null;
  }

  const isAdmin = user.role === 'admin';

  return (
    <div className="flex h-screen bg-gray-50 overflow-hidden">
      {/* Sidebar */}
      <div className="w-64 flex-shrink-0 h-full">
        {isAdmin ? (
          <AdminSidebar user={user} onLogout={handleLogout} />
        ) : (
          <UserSidebar
            user={user}
            onLogout={handleLogout}
            currentPage={currentPage}
            onNavigate={onNavigate}
          />
        )}
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col h-full overflow-hidden">
        {/* Header */}
        <header className="bg-white shadow-sm border-b border-gray-200 px-6 py-4 flex-shrink-0">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-[#032221]">
                {isAdmin ? 'Admin Dashboard' : 'Dashboard'}
              </h1>
              <p className="text-sm text-gray-600">
                Welcome back, {user.firstName}!
              </p>
            </div>

            <div className="flex items-center gap-4">
              {!isAdmin && (
                <div className="text-right">
                  <p className="text-sm font-medium text-gray-900">{user.fullName}</p>
                  <p className="text-xs text-gray-500">{user.pediTokens} Pedi Tokens</p>
                </div>
              )}
            </div>
          </div>
        </header>

        {/* Page Content */}
        <main className="flex-1 overflow-y-auto overflow-x-hidden p-6 min-h-0">
          <div className="h-full">
            {children}
          </div>
        </main>
      </div>
    </div>
  );
}
