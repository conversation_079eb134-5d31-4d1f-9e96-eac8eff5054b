import * as React from "react"
import { Slot } from "@radix-ui/react-slot"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"

const buttonVariants = cva(
  "inline-flex items-center justify-center whitespace-nowrap rounded-lg text-sm font-subheading ring-offset-background transition-all-smooth focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-brand-pine-green focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 shadow-soft hover:shadow-medium click-scale",
  {
    variants: {
      variant: {
        default: "bg-gradient-primary text-primary-foreground hover:shadow-glow hover-lift",
        destructive:
          "bg-destructive text-destructive-foreground hover:bg-destructive/90 hover-lift",
        outline:
          "border-2 border-brand-sage-green bg-transparent text-brand-pine-green hover:bg-brand-pine-green hover:text-brand-white hover-scale-sm",
        secondary:
          "bg-gradient-soft text-brand-dark-green hover:shadow-colored hover-lift",
        ghost: "hover:bg-brand-light-green/30 hover:text-brand-pine-green hover-scale-sm shadow-none hover:shadow-soft",
        link: "text-brand-pine-green underline-offset-4 hover:underline shadow-none hover:text-brand-dark-green",
      },
      size: {
        default: "h-10 px-4 py-2",
        sm: "h-9 rounded-md px-3 text-xs",
        lg: "h-12 rounded-xl px-8 text-base",
        icon: "h-10 w-10",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
)

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, asChild = false, ...props }, ref) => {
    const Comp = asChild ? Slot : "button"
    return (
      <Comp
        className={cn(buttonVariants({ variant, size, className }))}
        ref={ref}
        {...props}
      />
    )
  }
)
Button.displayName = "Button"

export { Button, buttonVariants }