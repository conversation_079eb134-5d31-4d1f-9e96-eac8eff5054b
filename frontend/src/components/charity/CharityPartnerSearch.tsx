import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { 
  Search, 
  Filter, 
  X, 
  MapPin, 
  Tag, 
  Star,
  SlidersHorizontal
} from 'lucide-react';
import { KENYAN_COUNTIES } from '@/constants/kenyan-counties';

export interface SearchFilters {
  search: string;
  category: string;
  county: string;
  town: string;
  acceptedItemTypes: string[];
  verificationStatus: string;
  minRating: number;
  sortBy: string;
  sortOrder: 'asc' | 'desc';
}

interface CharityPartnerSearchProps {
  filters: SearchFilters;
  onFiltersChange: (filters: SearchFilters) => void;
  onSearch: () => void;
  isLoading?: boolean;
  resultCount?: number;
  className?: string;
}

const CATEGORIES = [
  'Children & Youth',
  'Women Empowerment', 
  'Education',
  'Healthcare',
  'Environment',
  'Community Development',
  'Disaster Relief',
  'Elderly Care',
  'Disability Support',
  'General Welfare'
];



const ITEM_TYPES = [
  'Tops', 'Bottoms', 'Dresses', 'Outerwear', 'Shoes', 'Accessories',
  'Formal', 'Casual', 'Sportswear', 'Traditional', 'Children Clothing',
  'Baby Items', 'School Uniforms'
];

const SORT_OPTIONS = [
  { value: 'name', label: 'Name' },
  { value: 'createdAt', label: 'Date Added' },
  { value: 'averageRating', label: 'Rating' },
  { value: 'totalDonationsReceived', label: 'Donations Received' }
];

export const CharityPartnerSearch: React.FC<CharityPartnerSearchProps> = ({
  filters,
  onFiltersChange,
  onSearch,
  isLoading = false,
  resultCount,
  className = ''
}) => {
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false);
  const [localFilters, setLocalFilters] = useState<SearchFilters>(filters);

  useEffect(() => {
    setLocalFilters(filters);
  }, [filters]);

  const handleFilterChange = (key: keyof SearchFilters, value: any) => {
    const newFilters = { ...localFilters, [key]: value };
    setLocalFilters(newFilters);
    onFiltersChange(newFilters);
  };

  const handleItemTypeToggle = (itemType: string) => {
    const currentTypes = localFilters.acceptedItemTypes;
    const newTypes = currentTypes.includes(itemType)
      ? currentTypes.filter(type => type !== itemType)
      : [...currentTypes, itemType];
    
    handleFilterChange('acceptedItemTypes', newTypes);
  };

  const clearFilters = () => {
    const clearedFilters: SearchFilters = {
      search: '',
      category: '',
      county: '',
      town: '',
      acceptedItemTypes: [],
      verificationStatus: 'verified',
      minRating: 0,
      sortBy: 'name',
      sortOrder: 'asc'
    };
    setLocalFilters(clearedFilters);
    onFiltersChange(clearedFilters);
  };

  const hasActiveFilters = () => {
    return localFilters.search || 
           localFilters.category || 
           localFilters.county || 
           localFilters.town ||
           localFilters.acceptedItemTypes.length > 0 ||
           localFilters.minRating > 0;
  };

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Search className="h-5 w-5" />
            Find Charity Partners
          </CardTitle>
          {resultCount !== undefined && (
            <Badge variant="secondary">
              {resultCount} {resultCount === 1 ? 'partner' : 'partners'} found
            </Badge>
          )}
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Search Bar */}
        <div className="flex gap-2">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Search charity partners..."
              value={localFilters.search}
              onChange={(e) => handleFilterChange('search', e.target.value)}
              className="pl-10"
              onKeyPress={(e) => e.key === 'Enter' && onSearch()}
            />
          </div>
          <Button onClick={onSearch} disabled={isLoading}>
            {isLoading ? 'Searching...' : 'Search'}
          </Button>
        </div>

        {/* Quick Filters */}
        <div className="flex flex-wrap gap-2">
          <Select value={localFilters.category} onValueChange={(value) => handleFilterChange('category', value)}>
            <SelectTrigger className="w-48">
              <SelectValue placeholder="All Categories" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="">All Categories</SelectItem>
              {CATEGORIES.map((category) => (
                <SelectItem key={category} value={category}>
                  {category}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Select value={localFilters.county} onValueChange={(value) => handleFilterChange('county', value)}>
            <SelectTrigger className="w-48">
              <SelectValue placeholder="All Counties" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="">All Counties</SelectItem>
              {KENYAN_COUNTIES.map((county) => (
                <SelectItem key={county} value={county}>
                  {county}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Button
            variant="outline"
            onClick={() => setShowAdvancedFilters(!showAdvancedFilters)}
            className="flex items-center gap-2"
          >
            <SlidersHorizontal className="h-4 w-4" />
            Advanced Filters
          </Button>

          {hasActiveFilters() && (
            <Button variant="ghost" onClick={clearFilters} className="flex items-center gap-2">
              <X className="h-4 w-4" />
              Clear Filters
            </Button>
          )}
        </div>

        {/* Advanced Filters */}
        {showAdvancedFilters && (
          <div className="border-t pt-4 space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Town Filter */}
              <div>
                <Label htmlFor="town">Town</Label>
                <Input
                  id="town"
                  placeholder="Enter town name"
                  value={localFilters.town}
                  onChange={(e) => handleFilterChange('town', e.target.value)}
                />
              </div>

              {/* Minimum Rating */}
              <div>
                <Label htmlFor="minRating">Minimum Rating</Label>
                <Select 
                  value={localFilters.minRating.toString()} 
                  onValueChange={(value) => handleFilterChange('minRating', parseInt(value))}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Any Rating" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="0">Any Rating</SelectItem>
                    <SelectItem value="1">1+ Stars</SelectItem>
                    <SelectItem value="2">2+ Stars</SelectItem>
                    <SelectItem value="3">3+ Stars</SelectItem>
                    <SelectItem value="4">4+ Stars</SelectItem>
                    <SelectItem value="5">5 Stars</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Accepted Item Types */}
            <div>
              <Label>Accepted Item Types</Label>
              <div className="flex flex-wrap gap-2 mt-2">
                {ITEM_TYPES.map((itemType) => (
                  <Badge
                    key={itemType}
                    variant={localFilters.acceptedItemTypes.includes(itemType) ? "default" : "outline"}
                    className="cursor-pointer hover:bg-gray-100"
                    onClick={() => handleItemTypeToggle(itemType)}
                  >
                    {itemType}
                  </Badge>
                ))}
              </div>
            </div>

            {/* Sort Options */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="sortBy">Sort By</Label>
                <Select value={localFilters.sortBy} onValueChange={(value) => handleFilterChange('sortBy', value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {SORT_OPTIONS.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="sortOrder">Sort Order</Label>
                <Select 
                  value={localFilters.sortOrder} 
                  onValueChange={(value: 'asc' | 'desc') => handleFilterChange('sortOrder', value)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="asc">Ascending</SelectItem>
                    <SelectItem value="desc">Descending</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
