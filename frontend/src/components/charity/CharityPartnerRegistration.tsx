import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Loader2, 
  CheckCircle, 
  AlertCircle, 
  Building, 
  Mail, 
  Phone, 
  Globe, 
  MapPin,
  Tag,
  FileText,
  Settings
} from 'lucide-react';
import { KENYAN_COUNTIES } from '@/constants/kenyan-counties';

interface RegistrationData {
  name: string;
  description: string;
  category: string;
  contactInfo: {
    email: string;
    phone: string;
    website: string;
    address: {
      county: string;
      town: string;
      specificLocation: string;
    };
  };
  acceptedItemTypes: string[];
  requirements: {
    condition: string[];
    categories: string[];
    notes: string;
  };
  settings: {
    autoAcceptDonations: boolean;
    requireQualityCheck: boolean;
    providesPickup: boolean;
    maxPickupDistance: number;
    issuesReceipts: boolean;
  };
}

interface CharityPartnerRegistrationProps {
  onRegistrationSuccess?: (data: any) => void;
  onCancel?: () => void;
  className?: string;
}

const CATEGORIES = [
  'Children & Youth', 'Women Empowerment', 'Education', 'Healthcare',
  'Environment', 'Community Development', 'Disaster Relief', 'Elderly Care',
  'Disability Support', 'General Welfare'
];



const ITEM_TYPES = [
  'Tops', 'Bottoms', 'Dresses', 'Outerwear', 'Shoes', 'Accessories',
  'Formal', 'Casual', 'Sportswear', 'Traditional', 'Children Clothing',
  'Baby Items', 'School Uniforms'
];

const CONDITIONS = ['New', 'Like New', 'Good', 'Fair'];

export const CharityPartnerRegistration: React.FC<CharityPartnerRegistrationProps> = ({
  onRegistrationSuccess,
  onCancel,
  className = ''
}) => {
  const [formData, setFormData] = useState<RegistrationData>({
    name: '',
    description: '',
    category: '',
    contactInfo: {
      email: '',
      phone: '',
      website: '',
      address: {
        county: '',
        town: '',
        specificLocation: ''
      }
    },
    acceptedItemTypes: [],
    requirements: {
      condition: [],
      categories: [],
      notes: ''
    },
    settings: {
      autoAcceptDonations: true,
      requireQualityCheck: false,
      providesPickup: true,
      maxPickupDistance: 30,
      issuesReceipts: true
    }
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string>('');
  const [success, setSuccess] = useState(false);
  const [validationErrors, setValidationErrors] = useState<{ [key: string]: string }>({});

  const validateForm = (): boolean => {
    const errors: { [key: string]: string } = {};

    if (!formData.name.trim()) errors.name = 'Organization name is required';
    if (!formData.description.trim()) errors.description = 'Description is required';
    if (!formData.category) errors.category = 'Category is required';
    if (!formData.contactInfo.email.trim()) errors.email = 'Email is required';
    if (!formData.contactInfo.phone.trim()) errors.phone = 'Phone number is required';
    if (!formData.contactInfo.address.county) errors.county = 'County is required';
    if (!formData.contactInfo.address.town.trim()) errors.town = 'Town is required';
    if (!formData.contactInfo.address.specificLocation.trim()) errors.specificLocation = 'Specific location is required';
    if (formData.acceptedItemTypes.length === 0) errors.acceptedItemTypes = 'At least one accepted item type is required';
    if (formData.requirements.condition.length === 0) errors.conditions = 'At least one condition requirement is required';
    if (formData.requirements.categories.length === 0) errors.categories = 'At least one category requirement is required';

    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (formData.contactInfo.email && !emailRegex.test(formData.contactInfo.email)) {
      errors.email = 'Please enter a valid email address';
    }

    // Phone validation (Kenyan format)
    const phoneRegex = /^(\+254|0)[17]\d{8}$/;
    if (formData.contactInfo.phone && !phoneRegex.test(formData.contactInfo.phone)) {
      errors.phone = 'Please enter a valid Kenyan phone number';
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleInputChange = (field: string, value: any) => {
    const keys = field.split('.');
    setFormData(prev => {
      const newData = { ...prev };
      let current: any = newData;
      
      for (let i = 0; i < keys.length - 1; i++) {
        current = current[keys[i]];
      }
      
      current[keys[keys.length - 1]] = value;
      return newData;
    });

    // Clear validation error for this field
    if (validationErrors[field]) {
      setValidationErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }
  };

  const handleArrayToggle = (field: string, value: string) => {
    const keys = field.split('.');
    setFormData(prev => {
      const newData = { ...prev };
      let current: any = newData;
      
      for (let i = 0; i < keys.length - 1; i++) {
        current = current[keys[i]];
      }
      
      const currentArray = current[keys[keys.length - 1]] as string[];
      current[keys[keys.length - 1]] = currentArray.includes(value)
        ? currentArray.filter(item => item !== value)
        : [...currentArray, value];
      
      return newData;
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    setError('');

    try {
      const response = await fetch('/api/charity-partners/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Registration failed');
      }

      const data = await response.json();
      setSuccess(true);
      onRegistrationSuccess?.(data);
    } catch (error: any) {
      setError(error.message);
    } finally {
      setIsSubmitting(false);
    }
  };

  if (success) {
    return (
      <Card className={className}>
        <CardContent className="text-center py-12">
          <CheckCircle className="h-16 w-16 text-green-500 mx-auto mb-4" />
          <h3 className="text-xl font-semibold mb-2">Registration Submitted!</h3>
          <p className="text-gray-600 mb-6">
            Your charity partner registration has been submitted successfully. 
            Our team will review your application and contact you within 2-3 business days.
          </p>
          <Button onClick={onCancel}>Close</Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Building className="h-5 w-5" />
          Register as Charity Partner
        </CardTitle>
        <CardDescription>
          Join our platform to receive clothing donations and help your community
        </CardDescription>
      </CardHeader>

      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Error Display */}
          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {/* Basic Information */}
          <div className="space-y-4">
            <h4 className="font-medium flex items-center gap-2">
              <Building className="h-4 w-4" />
              Basic Information
            </h4>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="name">Organization Name *</Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                  placeholder="Enter organization name"
                  className={validationErrors.name ? 'border-red-500' : ''}
                />
                {validationErrors.name && (
                  <p className="text-sm text-red-500 mt-1">{validationErrors.name}</p>
                )}
              </div>

              <div>
                <Label htmlFor="category">Category *</Label>
                <Select value={formData.category} onValueChange={(value) => handleInputChange('category', value)}>
                  <SelectTrigger className={validationErrors.category ? 'border-red-500' : ''}>
                    <SelectValue placeholder="Select category" />
                  </SelectTrigger>
                  <SelectContent>
                    {CATEGORIES.map((category) => (
                      <SelectItem key={category} value={category}>
                        {category}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {validationErrors.category && (
                  <p className="text-sm text-red-500 mt-1">{validationErrors.category}</p>
                )}
              </div>
            </div>

            <div>
              <Label htmlFor="description">Description *</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                placeholder="Describe your organization's mission and activities"
                rows={3}
                className={validationErrors.description ? 'border-red-500' : ''}
              />
              {validationErrors.description && (
                <p className="text-sm text-red-500 mt-1">{validationErrors.description}</p>
              )}
            </div>
          </div>

          {/* Contact Information */}
          <div className="space-y-4">
            <h4 className="font-medium flex items-center gap-2">
              <Mail className="h-4 w-4" />
              Contact Information
            </h4>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="email">Email Address *</Label>
                <Input
                  id="email"
                  type="email"
                  value={formData.contactInfo.email}
                  onChange={(e) => handleInputChange('contactInfo.email', e.target.value)}
                  placeholder="<EMAIL>"
                  className={validationErrors.email ? 'border-red-500' : ''}
                />
                {validationErrors.email && (
                  <p className="text-sm text-red-500 mt-1">{validationErrors.email}</p>
                )}
              </div>

              <div>
                <Label htmlFor="phone">Phone Number *</Label>
                <Input
                  id="phone"
                  value={formData.contactInfo.phone}
                  onChange={(e) => handleInputChange('contactInfo.phone', e.target.value)}
                  placeholder="+254712345678 or 0712345678"
                  className={validationErrors.phone ? 'border-red-500' : ''}
                />
                {validationErrors.phone && (
                  <p className="text-sm text-red-500 mt-1">{validationErrors.phone}</p>
                )}
              </div>
            </div>

            <div>
              <Label htmlFor="website">Website (Optional)</Label>
              <Input
                id="website"
                type="url"
                value={formData.contactInfo.website}
                onChange={(e) => handleInputChange('contactInfo.website', e.target.value)}
                placeholder="https://www.yourorganization.org"
              />
            </div>
          </div>

          {/* Address Information */}
          <div className="space-y-4">
            <h4 className="font-medium flex items-center gap-2">
              <MapPin className="h-4 w-4" />
              Address Information
            </h4>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="county">County *</Label>
                <Select
                  value={formData.contactInfo.address.county}
                  onValueChange={(value) => handleInputChange('contactInfo.address.county', value)}
                >
                  <SelectTrigger className={validationErrors.county ? 'border-red-500' : ''}>
                    <SelectValue placeholder="Select county" />
                  </SelectTrigger>
                  <SelectContent>
                    {KENYAN_COUNTIES.map((county) => (
                      <SelectItem key={county} value={county}>
                        {county}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {validationErrors.county && (
                  <p className="text-sm text-red-500 mt-1">{validationErrors.county}</p>
                )}
              </div>

              <div>
                <Label htmlFor="town">Town *</Label>
                <Input
                  id="town"
                  value={formData.contactInfo.address.town}
                  onChange={(e) => handleInputChange('contactInfo.address.town', e.target.value)}
                  placeholder="Enter town name"
                  className={validationErrors.town ? 'border-red-500' : ''}
                />
                {validationErrors.town && (
                  <p className="text-sm text-red-500 mt-1">{validationErrors.town}</p>
                )}
              </div>
            </div>

            <div>
              <Label htmlFor="specificLocation">Specific Location *</Label>
              <Input
                id="specificLocation"
                value={formData.contactInfo.address.specificLocation}
                onChange={(e) => handleInputChange('contactInfo.address.specificLocation', e.target.value)}
                placeholder="Building name, street, or landmark"
                className={validationErrors.specificLocation ? 'border-red-500' : ''}
              />
              {validationErrors.specificLocation && (
                <p className="text-sm text-red-500 mt-1">{validationErrors.specificLocation}</p>
              )}
            </div>
          </div>

          {/* Accepted Item Types */}
          <div className="space-y-4">
            <h4 className="font-medium flex items-center gap-2">
              <Tag className="h-4 w-4" />
              Accepted Item Types *
            </h4>
            <p className="text-sm text-gray-600">Select the types of clothing items your organization accepts</p>

            <div className="flex flex-wrap gap-2">
              {ITEM_TYPES.map((itemType) => (
                <Badge
                  key={itemType}
                  variant={formData.acceptedItemTypes.includes(itemType) ? "default" : "outline"}
                  className="cursor-pointer hover:bg-gray-100"
                  onClick={() => handleArrayToggle('acceptedItemTypes', itemType)}
                >
                  {itemType}
                </Badge>
              ))}
            </div>
            {validationErrors.acceptedItemTypes && (
              <p className="text-sm text-red-500">{validationErrors.acceptedItemTypes}</p>
            )}
          </div>

          {/* Requirements */}
          <div className="space-y-4">
            <h4 className="font-medium flex items-center gap-2">
              <FileText className="h-4 w-4" />
              Donation Requirements
            </h4>

            <div>
              <Label>Accepted Conditions *</Label>
              <p className="text-sm text-gray-600 mb-2">Select the minimum condition of items you accept</p>
              <div className="flex flex-wrap gap-2">
                {CONDITIONS.map((condition) => (
                  <Badge
                    key={condition}
                    variant={formData.requirements.condition.includes(condition) ? "default" : "outline"}
                    className="cursor-pointer hover:bg-gray-100"
                    onClick={() => handleArrayToggle('requirements.condition', condition)}
                  >
                    {condition}
                  </Badge>
                ))}
              </div>
              {validationErrors.conditions && (
                <p className="text-sm text-red-500 mt-1">{validationErrors.conditions}</p>
              )}
            </div>

            <div>
              <Label>Preferred Categories *</Label>
              <p className="text-sm text-gray-600 mb-2">Select the categories you most need</p>
              <div className="flex flex-wrap gap-2">
                {ITEM_TYPES.map((category) => (
                  <Badge
                    key={category}
                    variant={formData.requirements.categories.includes(category) ? "default" : "outline"}
                    className="cursor-pointer hover:bg-gray-100"
                    onClick={() => handleArrayToggle('requirements.categories', category)}
                  >
                    {category}
                  </Badge>
                ))}
              </div>
              {validationErrors.categories && (
                <p className="text-sm text-red-500 mt-1">{validationErrors.categories}</p>
              )}
            </div>

            <div>
              <Label htmlFor="requirementNotes">Additional Requirements (Optional)</Label>
              <Textarea
                id="requirementNotes"
                value={formData.requirements.notes}
                onChange={(e) => handleInputChange('requirements.notes', e.target.value)}
                placeholder="Any specific requirements or preferences for donations"
                rows={2}
              />
            </div>
          </div>

          {/* Settings */}
          <div className="space-y-4">
            <h4 className="font-medium flex items-center gap-2">
              <Settings className="h-4 w-4" />
              Operational Settings
            </h4>

            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <div>
                  <Label>Auto-accept donations</Label>
                  <p className="text-sm text-gray-600">Automatically accept donations that meet your requirements</p>
                </div>
                <input
                  type="checkbox"
                  checked={formData.settings.autoAcceptDonations}
                  onChange={(e) => handleInputChange('settings.autoAcceptDonations', e.target.checked)}
                  className="h-4 w-4"
                />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <Label>Require quality check</Label>
                  <p className="text-sm text-gray-600">Review items before accepting them</p>
                </div>
                <input
                  type="checkbox"
                  checked={formData.settings.requireQualityCheck}
                  onChange={(e) => handleInputChange('settings.requireQualityCheck', e.target.checked)}
                  className="h-4 w-4"
                />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <Label>Provide pickup service</Label>
                  <p className="text-sm text-gray-600">Offer to pick up donations from donors</p>
                </div>
                <input
                  type="checkbox"
                  checked={formData.settings.providesPickup}
                  onChange={(e) => handleInputChange('settings.providesPickup', e.target.checked)}
                  className="h-4 w-4"
                />
              </div>

              {formData.settings.providesPickup && (
                <div>
                  <Label htmlFor="maxPickupDistance">Maximum pickup distance (km)</Label>
                  <Input
                    id="maxPickupDistance"
                    type="number"
                    min="0"
                    max="100"
                    value={formData.settings.maxPickupDistance}
                    onChange={(e) => handleInputChange('settings.maxPickupDistance', parseInt(e.target.value) || 0)}
                    placeholder="30"
                  />
                </div>
              )}

              <div className="flex items-center justify-between">
                <div>
                  <Label>Issue donation receipts</Label>
                  <p className="text-sm text-gray-600">Provide receipts for tax deduction purposes</p>
                </div>
                <input
                  type="checkbox"
                  checked={formData.settings.issuesReceipts}
                  onChange={(e) => handleInputChange('settings.issuesReceipts', e.target.checked)}
                  className="h-4 w-4"
                />
              </div>
            </div>
          </div>

          <div className="flex justify-between">
            <Button type="button" variant="outline" onClick={onCancel}>
              Cancel
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Submitting...
                </>
              ) : (
                'Submit Registration'
              )}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
};
