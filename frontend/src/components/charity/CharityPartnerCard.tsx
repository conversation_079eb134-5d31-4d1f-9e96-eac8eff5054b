import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  MapPin, 
  Phone, 
  Mail, 
  Globe, 
  Star, 
  Users, 
  Package, 
  Heart,
  CheckCircle,
  Clock,
  AlertTriangle
} from 'lucide-react';

export interface CharityPartner {
  _id: string;
  name: string;
  description: string;
  category: string;
  contactInfo: {
    email: string;
    phone: string;
    website?: string;
    address: {
      county: string;
      town: string;
      specificLocation: string;
    };
  };
  logo?: string;
  isActive: boolean;
  acceptedItemTypes: string[];
  requirements: {
    condition: string[];
    categories: string[];
    notes?: string;
  };
  stats: {
    totalDonationsReceived: number;
    totalItemsReceived: number;
    totalBeneficiaries: number;
    averageRating: number;
    totalRatings: number;
    responseTime: number;
  };
  verificationStatus: 'pending' | 'verified' | 'suspended';
  verificationDate?: string;
  createdAt: string;
  updatedAt: string;
}

interface CharityPartnerCardProps {
  charityPartner: CharityPartner;
  onSelect?: (charityPartner: CharityPartner) => void;
  onViewDetails?: (charityPartner: CharityPartner) => void;
  showSelectButton?: boolean;
  showDetailsButton?: boolean;
  compact?: boolean;
  className?: string;
}

export const CharityPartnerCard: React.FC<CharityPartnerCardProps> = ({
  charityPartner,
  onSelect,
  onViewDetails,
  showSelectButton = false,
  showDetailsButton = true,
  compact = false,
  className = ''
}) => {
  const getVerificationIcon = () => {
    switch (charityPartner.verificationStatus) {
      case 'verified':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'pending':
        return <Clock className="h-4 w-4 text-yellow-500" />;
      case 'suspended':
        return <AlertTriangle className="h-4 w-4 text-red-500" />;
      default:
        return null;
    }
  };

  const getVerificationBadge = () => {
    switch (charityPartner.verificationStatus) {
      case 'verified':
        return <Badge variant="default" className="bg-green-100 text-green-800">Verified</Badge>;
      case 'pending':
        return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">Pending</Badge>;
      case 'suspended':
        return <Badge variant="destructive">Suspended</Badge>;
      default:
        return null;
    }
  };

  const getCategoryColor = (category: string) => {
    const colors: { [key: string]: string } = {
      'Children & Youth': 'bg-blue-50 text-blue-700 border border-blue-200',
      'Women Empowerment': 'bg-purple-50 text-purple-700 border border-purple-200',
      'Education': 'bg-brand-pine-green/10 text-brand-pine-green border border-brand-pine-green/20',
      'Healthcare': 'bg-red-50 text-red-700 border border-red-200',
      'Environment': 'bg-brand-dark-green/10 text-brand-dark-green border border-brand-dark-green/20',
      'Community Development': 'bg-orange-50 text-orange-700 border border-orange-200',
      'Disaster Relief': 'bg-yellow-50 text-yellow-700 border border-yellow-200',
      'Elderly Care': 'bg-indigo-50 text-indigo-700 border border-indigo-200',
      'Disability Support': 'bg-pink-50 text-pink-700 border border-pink-200',
      'General Welfare': 'bg-gray-50 text-gray-700 border border-gray-200'
    };
    return colors[category] || 'bg-gray-50 text-gray-700 border border-gray-200';
  };

  return (
    <Card className={`hover:shadow-lg transition-shadow duration-200 ${className}`}>
      <CardHeader className={compact ? 'pb-2' : ''}>
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <div className="flex items-center gap-2 mb-2">
              <CardTitle className={`${compact ? 'text-lg' : 'text-xl'} font-semibold`}>
                {charityPartner.name}
              </CardTitle>
              {getVerificationIcon()}
            </div>
            <div className="flex flex-wrap gap-2 mb-2">
              <Badge className={getCategoryColor(charityPartner.category)}>
                {charityPartner.category}
              </Badge>
              {getVerificationBadge()}
            </div>
          </div>
          {charityPartner.logo && (
            <div className="ml-4">
              <img
                src={charityPartner.logo}
                alt={`${charityPartner.name} logo`}
                className="w-12 h-12 rounded-lg object-cover"
              />
            </div>
          )}
        </div>
        
        {!compact && (
          <CardDescription className="text-sm text-gray-600 line-clamp-2">
            {charityPartner.description}
          </CardDescription>
        )}
      </CardHeader>

      <CardContent className={compact ? 'pt-0' : ''}>
        {/* Location */}
        <div className="flex items-center gap-2 text-sm text-gray-600 mb-3">
          <MapPin className="h-4 w-4" />
          <span>{charityPartner.contactInfo.address.town}, {charityPartner.contactInfo.address.county}</span>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-3 gap-4 mb-4">
          <div className="text-center">
            <div className="flex items-center justify-center gap-1 text-sm text-gray-600">
              <Package className="h-4 w-4" />
              <span className="font-semibold">{charityPartner.stats.totalDonationsReceived}</span>
            </div>
            <p className="text-xs text-gray-500">Donations</p>
          </div>
          <div className="text-center">
            <div className="flex items-center justify-center gap-1 text-sm text-gray-600">
              <Users className="h-4 w-4" />
              <span className="font-semibold">{charityPartner.stats.totalBeneficiaries}</span>
            </div>
            <p className="text-xs text-gray-500">Beneficiaries</p>
          </div>
          <div className="text-center">
            <div className="flex items-center justify-center gap-1 text-sm text-gray-600">
              <Star className="h-4 w-4" />
              <span className="font-semibold">
                {charityPartner.stats.averageRating > 0 
                  ? charityPartner.stats.averageRating.toFixed(1) 
                  : 'N/A'
                }
              </span>
            </div>
            <p className="text-xs text-gray-500">Rating</p>
          </div>
        </div>

        {/* Accepted Items */}
        {!compact && (
          <div className="mb-4">
            <p className="text-sm font-medium text-gray-700 mb-2">Accepts:</p>
            <div className="flex flex-wrap gap-1">
              {charityPartner.acceptedItemTypes.slice(0, 3).map((type) => (
                <Badge key={type} variant="outline" className="text-xs">
                  {type}
                </Badge>
              ))}
              {charityPartner.acceptedItemTypes.length > 3 && (
                <Badge variant="outline" className="text-xs">
                  +{charityPartner.acceptedItemTypes.length - 3} more
                </Badge>
              )}
            </div>
          </div>
        )}

        {/* Contact Info */}
        {!compact && (
          <div className="flex flex-wrap gap-4 text-sm text-gray-600 mb-4">
            <div className="flex items-center gap-1">
              <Phone className="h-3 w-3" />
              <span>{charityPartner.contactInfo.phone}</span>
            </div>
            <div className="flex items-center gap-1">
              <Mail className="h-3 w-3" />
              <span className="truncate">{charityPartner.contactInfo.email}</span>
            </div>
            {charityPartner.contactInfo.website && (
              <div className="flex items-center gap-1">
                <Globe className="h-3 w-3" />
                <a 
                  href={charityPartner.contactInfo.website} 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="text-blue-600 hover:underline"
                >
                  Website
                </a>
              </div>
            )}
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex gap-2">
          {showSelectButton && (
            <Button
              onClick={() => onSelect?.(charityPartner)}
              className="flex-1"
              disabled={charityPartner.verificationStatus !== 'verified' || !charityPartner.isActive}
            >
              <Heart className="h-4 w-4 mr-2" />
              Select for Donation
            </Button>
          )}
          {showDetailsButton && (
            <Button
              variant="outline"
              onClick={() => onViewDetails?.(charityPartner)}
              className={showSelectButton ? '' : 'flex-1'}
            >
              View Details
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  );
};
