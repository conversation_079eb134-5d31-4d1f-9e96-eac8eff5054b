import React, { useState, useEffect } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, AlertCircle, ChevronLeft, ChevronRight } from 'lucide-react';
import { CharityPartnerCard, CharityPartner } from './CharityPartnerCard';
import { CharityPartnerSearch, SearchFilters } from './CharityPartnerSearch';

interface PaginationData {
  currentPage: number;
  totalPages: number;
  totalItems: number;
  itemsPerPage: number;
  hasNextPage: boolean;
  hasPrevPage: boolean;
}

interface CharityPartnerListProps {
  onSelectPartner?: (partner: CharityPartner) => void;
  onViewPartnerDetails?: (partner: CharityPartner) => void;
  showSelectButton?: boolean;
  showDetailsButton?: boolean;
  compact?: boolean;
  className?: string;
}

export const CharityPartnerList: React.FC<CharityPartnerListProps> = ({
  onSelectPartner,
  onViewPartnerDetails,
  showSelectButton = false,
  showDetailsButton = true,
  compact = false,
  className = ''
}) => {
  const [charityPartners, setCharityPartners] = useState<CharityPartner[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string>('');
  const [pagination, setPagination] = useState<PaginationData>({
    currentPage: 1,
    totalPages: 1,
    totalItems: 0,
    itemsPerPage: 12,
    hasNextPage: false,
    hasPrevPage: false
  });

  const [filters, setFilters] = useState<SearchFilters>({
    search: '',
    category: '',
    county: '',
    town: '',
    acceptedItemTypes: [],
    verificationStatus: 'verified',
    minRating: 0,
    sortBy: 'name',
    sortOrder: 'asc'
  });

  const fetchCharityPartners = async (page: number = 1) => {
    setIsLoading(true);
    setError('');

    try {
      const queryParams = new URLSearchParams({
        page: page.toString(),
        limit: pagination.itemsPerPage.toString(),
        sortBy: filters.sortBy,
        sortOrder: filters.sortOrder,
        verificationStatus: filters.verificationStatus
      });

      // Add optional filters
      if (filters.search) queryParams.append('search', filters.search);
      if (filters.category) queryParams.append('category', filters.category);
      if (filters.county) queryParams.append('county', filters.county);
      if (filters.town) queryParams.append('town', filters.town);
      if (filters.acceptedItemTypes.length > 0) {
        queryParams.append('acceptedItemTypes', filters.acceptedItemTypes.join(','));
      }

      const response = await fetch(`/api/charity-partners?${queryParams}`, {
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to fetch charity partners');
      }

      const data = await response.json();
      setCharityPartners(data.data.charityPartners);
      setPagination(data.data.pagination);
    } catch (error: any) {
      setError(error.message);
      setCharityPartners([]);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchCharityPartners(1);
  }, []);

  const handleSearch = () => {
    fetchCharityPartners(1);
  };

  const handleFiltersChange = (newFilters: SearchFilters) => {
    setFilters(newFilters);
  };

  const handlePageChange = (newPage: number) => {
    if (newPage >= 1 && newPage <= pagination.totalPages) {
      fetchCharityPartners(newPage);
    }
  };

  const handleSelectPartner = (partner: CharityPartner) => {
    onSelectPartner?.(partner);
  };

  const handleViewDetails = (partner: CharityPartner) => {
    onViewPartnerDetails?.(partner);
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Search and Filters */}
      <CharityPartnerSearch
        filters={filters}
        onFiltersChange={handleFiltersChange}
        onSearch={handleSearch}
        isLoading={isLoading}
        resultCount={pagination.totalItems}
      />

      {/* Error Display */}
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Loading State */}
      {isLoading && (
        <Card>
          <CardContent className="flex items-center justify-center py-12">
            <div className="flex items-center gap-2">
              <Loader2 className="h-6 w-6 animate-spin" />
              <span>Loading charity partners...</span>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Results */}
      {!isLoading && !error && (
        <>
          {charityPartners.length === 0 ? (
            <Card>
              <CardContent className="text-center py-12">
                <div className="text-gray-500">
                  <p className="text-lg font-medium mb-2">No charity partners found</p>
                  <p className="text-sm">Try adjusting your search criteria or filters</p>
                </div>
              </CardContent>
            </Card>
          ) : (
            <>
              {/* Charity Partner Grid */}
              <div className={`grid gap-6 ${
                compact 
                  ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4' 
                  : 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3'
              }`}>
                {charityPartners.map((partner) => (
                  <CharityPartnerCard
                    key={partner._id}
                    charityPartner={partner}
                    onSelect={handleSelectPartner}
                    onViewDetails={handleViewDetails}
                    showSelectButton={showSelectButton}
                    showDetailsButton={showDetailsButton}
                    compact={compact}
                  />
                ))}
              </div>

              {/* Pagination */}
              {pagination.totalPages > 1 && (
                <Card>
                  <CardContent className="flex items-center justify-between py-4">
                    <div className="text-sm text-gray-600">
                      Showing {((pagination.currentPage - 1) * pagination.itemsPerPage) + 1} to{' '}
                      {Math.min(pagination.currentPage * pagination.itemsPerPage, pagination.totalItems)} of{' '}
                      {pagination.totalItems} charity partners
                    </div>
                    
                    <div className="flex items-center gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handlePageChange(pagination.currentPage - 1)}
                        disabled={!pagination.hasPrevPage}
                      >
                        <ChevronLeft className="h-4 w-4" />
                        Previous
                      </Button>
                      
                      <div className="flex items-center gap-1">
                        {Array.from({ length: Math.min(5, pagination.totalPages) }, (_, i) => {
                          let pageNum;
                          if (pagination.totalPages <= 5) {
                            pageNum = i + 1;
                          } else if (pagination.currentPage <= 3) {
                            pageNum = i + 1;
                          } else if (pagination.currentPage >= pagination.totalPages - 2) {
                            pageNum = pagination.totalPages - 4 + i;
                          } else {
                            pageNum = pagination.currentPage - 2 + i;
                          }
                          
                          return (
                            <Button
                              key={pageNum}
                              variant={pageNum === pagination.currentPage ? "default" : "outline"}
                              size="sm"
                              onClick={() => handlePageChange(pageNum)}
                              className="w-8 h-8 p-0"
                            >
                              {pageNum}
                            </Button>
                          );
                        })}
                      </div>
                      
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handlePageChange(pagination.currentPage + 1)}
                        disabled={!pagination.hasNextPage}
                      >
                        Next
                        <ChevronRight className="h-4 w-4" />
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              )}
            </>
          )}
        </>
      )}
    </div>
  );
};
