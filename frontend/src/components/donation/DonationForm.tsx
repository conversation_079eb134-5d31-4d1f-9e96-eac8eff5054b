'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Upload, X, Heart, Coins, Gift } from 'lucide-react';

interface DonationFormData {
  title: string;
  description: string;
  category: string;
  subcategory: string;
  brand: string;
  size: string;
  color: string;
  condition: string;
  tags: string;
  location: {
    county: string;
    town: string;
  };
  sustainabilityInfo: {
    material: string;
    careInstructions: string;
    estimatedLifespan: string;
    recyclingInfo: string;
  };
}

interface DonationFormProps {
  onSubmit: (data: DonationFormData, images: File[]) => void;
  loading?: boolean;
}

const categories = [
  'Tops', 'Bottoms', 'Dresses', 'Outerwear', 'Shoes', 'Accessories', 
  'Formal', 'Casual', 'Sportswear', 'Traditional', 'Vintage', 'Underwear'
];

const sizes = ['XS', 'S', 'M', 'L', 'XL', 'XXL', 'XXXL', 'One Size', 'Custom'];
const conditions = ['New', 'Like New', 'Good', 'Fair', 'Poor'];

export default function DonationForm({ onSubmit, loading = false }: DonationFormProps) {
  const [formData, setFormData] = useState<DonationFormData>({
    title: '',
    description: '',
    category: '',
    subcategory: '',
    brand: '',
    size: '',
    color: '',
    condition: '',
    tags: '',
    location: {
      county: '',
      town: '',
    },
    sustainabilityInfo: {
      material: '',
      careInstructions: '',
      estimatedLifespan: '',
      recyclingInfo: '',
    },
  });

  const [images, setImages] = useState<File[]>([]);
  const [imagePreviews, setImagePreviews] = useState<string[]>([]);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [dragActive, setDragActive] = useState(false);

  const handleInputChange = (field: string, value: string) => {
    if (field.startsWith('sustainabilityInfo.')) {
      const subField = field.split('.')[1];
      setFormData(prev => ({
        ...prev,
        sustainabilityInfo: {
          ...prev.sustainabilityInfo,
          [subField]: value,
        },
      }));
    } else if (field.startsWith('location.')) {
      const subField = field.split('.')[1];
      setFormData(prev => ({
        ...prev,
        location: {
          ...prev.location,
          [subField]: value,
        },
      }));
    } else {
      setFormData(prev => ({ ...prev, [field]: value }));
    }
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const handleImageUpload = (files: FileList | null) => {
    if (!files) return;

    const newFiles = Array.from(files).slice(0, 5 - images.length);
    const newImages = [...images, ...newFiles];
    setImages(newImages);

    // Create previews
    const newPreviews = [...imagePreviews];
    newFiles.forEach(file => {
      const reader = new FileReader();
      reader.onload = (e) => {
        newPreviews.push(e.target?.result as string);
        setImagePreviews([...newPreviews]);
      };
      reader.readAsDataURL(file);
    });
  };

  const removeImage = (index: number) => {
    const newImages = images.filter((_, i) => i !== index);
    const newPreviews = imagePreviews.filter((_, i) => i !== index);
    setImages(newImages);
    setImagePreviews(newPreviews);
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.title.trim()) newErrors.title = 'Title is required';
    if (!formData.description.trim()) newErrors.description = 'Description is required';
    if (!formData.category) newErrors.category = 'Category is required';
    if (!formData.size) newErrors.size = 'Size is required';
    if (!formData.color.trim()) newErrors.color = 'Color is required';
    if (!formData.condition) newErrors.condition = 'Condition is required';
    if (!formData.location.county.trim()) newErrors['location.county'] = 'County is required';
    if (!formData.location.town.trim()) newErrors['location.town'] = 'Town is required';
    if (!formData.sustainabilityInfo.material.trim()) newErrors['sustainabilityInfo.material'] = 'Material is required';
    if (!formData.sustainabilityInfo.careInstructions.trim()) newErrors['sustainabilityInfo.careInstructions'] = 'Care instructions are required';
    if (!formData.sustainabilityInfo.estimatedLifespan.trim()) newErrors['sustainabilityInfo.estimatedLifespan'] = 'Estimated lifespan is required';

    if (images.length === 0) newErrors.images = 'At least one image is required';

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (validateForm()) {
      onSubmit(formData, images);
    }
  };

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    handleImageUpload(e.dataTransfer.files);
  };

  return (
    <div className="max-w-4xl mx-auto p-6">
      {/* Header with donation info */}
      <Card className="mb-8 bg-gradient-to-r from-green-50 to-blue-50 border-green-200">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-green-700">
            <Heart className="h-6 w-6" />
            Donate Your Clothing
          </CardTitle>
          <CardDescription className="text-green-600">
            Help others in need while earning tokens for your generosity. Your donations make a real difference in the community.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="flex items-center gap-3 p-3 bg-white rounded-lg border border-green-100">
              <Coins className="h-8 w-8 text-yellow-500" />
              <div>
                <p className="font-medium text-green-700">Earn Tokens</p>
                <p className="text-sm text-green-600">50-100 tokens per donation</p>
              </div>
            </div>
            <div className="flex items-center gap-3 p-3 bg-white rounded-lg border border-green-100">
              <Heart className="h-8 w-8 text-red-500" />
              <div>
                <p className="font-medium text-green-700">Help Others</p>
                <p className="text-sm text-green-600">Support those in need</p>
              </div>
            </div>
            <div className="flex items-center gap-3 p-3 bg-white rounded-lg border border-green-100">
              <Gift className="h-8 w-8 text-purple-500" />
              <div>
                <p className="font-medium text-green-700">Make Impact</p>
                <p className="text-sm text-green-600">Reduce clothing waste</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <form onSubmit={handleSubmit} className="space-y-8">
        <Card>
          <CardHeader>
            <CardTitle>Item Details</CardTitle>
            <CardDescription>
              Provide details about the clothing item you want to donate
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Image Upload */}
            <div className="space-y-4">
              <Label className="text-base font-medium">Photos * (Max 5)</Label>
              <div
                className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
                  dragActive ? 'border-green-400 bg-green-50' : 'border-gray-300 hover:border-green-400'
                }`}
                onDragEnter={handleDrag}
                onDragLeave={handleDrag}
                onDragOver={handleDrag}
                onDrop={handleDrop}
              >
                <Upload className="h-12 w-12 mx-auto mb-4 text-gray-400" />
                <p className="text-lg font-medium mb-2">Upload photos of your item</p>
                <p className="text-gray-500 mb-4">Drag and drop or click to browse</p>
                <input
                  type="file"
                  multiple
                  accept="image/*"
                  onChange={(e) => handleImageUpload(e.target.files)}
                  className="hidden"
                  id="image-upload"
                />
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => document.getElementById('image-upload')?.click()}
                  disabled={images.length >= 5}
                >
                  Choose Files
                </Button>
              </div>
              {errors.images && <p className="text-sm text-red-500">{errors.images}</p>}
              
              {imagePreviews.length > 0 && (
                <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
                  {imagePreviews.map((preview, index) => (
                    <div key={index} className="relative group">
                      <img
                        src={preview}
                        alt={`Preview ${index + 1}`}
                        className="w-full h-24 object-cover rounded-lg border"
                      />
                      <button
                        type="button"
                        onClick={() => removeImage(index)}
                        className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity"
                      >
                        <X className="h-4 w-4" />
                      </button>
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* Basic Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="title">Title *</Label>
                <Input
                  id="title"
                  value={formData.title}
                  onChange={(e) => handleInputChange('title', e.target.value)}
                  placeholder="e.g., Vintage Denim Jacket"
                  className={errors.title ? 'border-red-500' : ''}
                />
                {errors.title && <p className="text-sm text-red-500">{errors.title}</p>}
              </div>

              <div className="space-y-2">
                <Label htmlFor="brand">Brand</Label>
                <Input
                  id="brand"
                  value={formData.brand}
                  onChange={(e) => handleInputChange('brand', e.target.value)}
                  placeholder="e.g., Levi's, H&M, Local Brand"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Description *</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                placeholder="Describe your item's style, fit, and any special features..."
                rows={4}
                className={errors.description ? 'border-red-500' : ''}
              />
              {errors.description && <p className="text-sm text-red-500">{errors.description}</p>}
            </div>

            {/* Category and Details */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="space-y-2">
                <Label>Category *</Label>
                <Select value={formData.category} onValueChange={(value) => handleInputChange('category', value)}>
                  <SelectTrigger className={errors.category ? 'border-red-500' : ''}>
                    <SelectValue placeholder="Select category" />
                  </SelectTrigger>
                  <SelectContent>
                    {categories.map((category) => (
                      <SelectItem key={category} value={category}>
                        {category}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {errors.category && <p className="text-sm text-red-500">{errors.category}</p>}
              </div>

              <div className="space-y-2">
                <Label>Size *</Label>
                <Select value={formData.size} onValueChange={(value) => handleInputChange('size', value)}>
                  <SelectTrigger className={errors.size ? 'border-red-500' : ''}>
                    <SelectValue placeholder="Select size" />
                  </SelectTrigger>
                  <SelectContent>
                    {sizes.map((size) => (
                      <SelectItem key={size} value={size}>
                        {size}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {errors.size && <p className="text-sm text-red-500">{errors.size}</p>}
              </div>

              <div className="space-y-2">
                <Label htmlFor="color">Color *</Label>
                <Input
                  id="color"
                  value={formData.color}
                  onChange={(e) => handleInputChange('color', e.target.value)}
                  placeholder="e.g., Blue, Red, Multi-color"
                  className={errors.color ? 'border-red-500' : ''}
                />
                {errors.color && <p className="text-sm text-red-500">{errors.color}</p>}
              </div>
            </div>

            <div className="space-y-2">
              <Label>Condition *</Label>
              <Select value={formData.condition} onValueChange={(value) => handleInputChange('condition', value)}>
                <SelectTrigger className={errors.condition ? 'border-red-500' : ''}>
                  <SelectValue placeholder="Select condition" />
                </SelectTrigger>
                <SelectContent>
                  {conditions.map((condition) => (
                    <SelectItem key={condition} value={condition}>
                      {condition}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.condition && <p className="text-sm text-red-500">{errors.condition}</p>}
            </div>

            {/* Location */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="county">County *</Label>
                <Input
                  id="county"
                  value={formData.location.county}
                  onChange={(e) => handleInputChange('location.county', e.target.value)}
                  placeholder="e.g., Nairobi, Mombasa, Kisumu"
                  className={errors['location.county'] ? 'border-red-500' : ''}
                />
                {errors['location.county'] && <p className="text-sm text-red-500">{errors['location.county']}</p>}
              </div>

              <div className="space-y-2">
                <Label htmlFor="town">Town *</Label>
                <Input
                  id="town"
                  value={formData.location.town}
                  onChange={(e) => handleInputChange('location.town', e.target.value)}
                  placeholder="e.g., Westlands, Nyali, Milimani"
                  className={errors['location.town'] ? 'border-red-500' : ''}
                />
                {errors['location.town'] && <p className="text-sm text-red-500">{errors['location.town']}</p>}
              </div>
            </div>

            {/* Tags */}
            <div className="space-y-2">
              <Label htmlFor="tags">Tags (Optional)</Label>
              <Input
                id="tags"
                value={formData.tags}
                onChange={(e) => handleInputChange('tags', e.target.value)}
                placeholder="e.g., vintage, casual, summer (separate with commas)"
              />
              <p className="text-sm text-gray-500">Add tags to help others find your item</p>
            </div>

            {/* Sustainability Information */}
            <div className="space-y-4">
              <Label className="text-base font-medium">Sustainability Information *</Label>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="material">Material *</Label>
                  <Input
                    id="material"
                    value={formData.sustainabilityInfo.material}
                    onChange={(e) => handleInputChange('sustainabilityInfo.material', e.target.value)}
                    placeholder="e.g., 100% Cotton, Polyester blend"
                    className={errors['sustainabilityInfo.material'] ? 'border-red-500' : ''}
                  />
                  {errors['sustainabilityInfo.material'] && (
                    <p className="text-sm text-red-500">{errors['sustainabilityInfo.material']}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="estimatedLifespan">Estimated Lifespan *</Label>
                  <Input
                    id="estimatedLifespan"
                    value={formData.sustainabilityInfo.estimatedLifespan}
                    onChange={(e) => handleInputChange('sustainabilityInfo.estimatedLifespan', e.target.value)}
                    placeholder="e.g., 5+ years, 2-3 years"
                    className={errors['sustainabilityInfo.estimatedLifespan'] ? 'border-red-500' : ''}
                  />
                  {errors['sustainabilityInfo.estimatedLifespan'] && (
                    <p className="text-sm text-red-500">{errors['sustainabilityInfo.estimatedLifespan']}</p>
                  )}
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="careInstructions">Care Instructions *</Label>
                <Textarea
                  id="careInstructions"
                  value={formData.sustainabilityInfo.careInstructions}
                  onChange={(e) => handleInputChange('sustainabilityInfo.careInstructions', e.target.value)}
                  placeholder="e.g., Machine wash cold, hang dry, iron on low heat"
                  rows={3}
                  className={errors['sustainabilityInfo.careInstructions'] ? 'border-red-500' : ''}
                />
                {errors['sustainabilityInfo.careInstructions'] && (
                  <p className="text-sm text-red-500">{errors['sustainabilityInfo.careInstructions']}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="recyclingInfo">Recycling Information (Optional)</Label>
                <Textarea
                  id="recyclingInfo"
                  value={formData.sustainabilityInfo.recyclingInfo}
                  onChange={(e) => handleInputChange('sustainabilityInfo.recyclingInfo', e.target.value)}
                  placeholder="e.g., Can be recycled at textile collection points, biodegradable materials"
                  rows={2}
                />
              </div>
            </div>
          </CardContent>
        </Card>

        <div className="flex justify-end space-x-4">
          <Button type="button" variant="outline">
            Cancel
          </Button>
          <Button type="submit" disabled={loading} className="bg-green-600 hover:bg-green-700">
            {loading ? 'Listing for Donation...' : 'List for Donation'}
          </Button>
        </div>
      </form>
    </div>
  );
}
