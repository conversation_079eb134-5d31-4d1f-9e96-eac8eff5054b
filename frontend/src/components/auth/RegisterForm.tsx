'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card } from '@/components/ui/card';
import { CardContent } from '@/components/ui/card';
import { CardDescription } from '@/components/ui/card';
import { CardHeader } from '@/components/ui/card';
import { CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Loader2,
  Phone,
  User,
  MapPin,
  Shield,
} from 'lucide-react';
import { KENYAN_COUNTIES } from '@/constants/kenyan-counties';

interface RegisterFormProps {
  onSwitchToLogin?: () => void;
}

export default function RegisterForm({ onSwitchToLogin }: RegisterFormProps) {
  const [step, setStep] = useState<'details' | 'otp'>('details');
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    phoneNumber: '',
    email: '',
    county: '',
    town: '',
  });
  const [otp, setOtp] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const router = useRouter();

  const formatPhoneNumber = (value: string) => {
    const digits = value.replace(/\D/g, '');
    if (digits.startsWith('254')) return '+' + digits;
    if (digits.startsWith('0')) return '+254' + digits.substring(1);
    if (digits.length <= 9) return '+254' + digits;
    return value;
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData((prev) => ({
      ...prev,
      [field]: field === 'phoneNumber' ? formatPhoneNumber(value) : value,
    }));
  };

  const handleDetailsSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      const response = await fetch('/api/auth/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...formData,
          location: {
            county: formData.county,
            town: formData.town,
          },
        }),
      });

      const data = await response.json();

      if (data.success) {
        setSuccess('Registration successful! Please verify your phone number.');
        setStep('otp');
      } else {
        setError(data.message || 'Registration failed');
      }
    } catch (err) {
      setError('Network error. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleOtpSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      const response = await fetch('/api/auth/verify-otp', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          phoneNumber: formData.phoneNumber,
          otp,
        }),
      });

      const data = await response.json();

      if (data.success) {
        localStorage.setItem('token', data.data.token);
        localStorage.setItem('user', JSON.stringify(data.data.user));
        setSuccess('Account verified successfully! Welcome to Pedi!');
        setTimeout(() => {
          router.push('/dashboard');
        }, 1000);
      } else {
        setError(data.message || 'Invalid OTP');
      }
    } catch (err) {
      setError('Network error. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleResendOtp = async () => {
    setLoading(true);
    setError('');

    try {
      const response = await fetch('/api/auth/request-otp', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ phoneNumber: formData.phoneNumber }),
      });

      const data = await response.json();

      if (data.success) {
        setSuccess('New OTP sent successfully!');
      } else {
        setError(data.message || 'Failed to resend OTP');
      }
    } catch (err) {
      setError('Network error. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card className="w-full max-w-md mx-auto shadow-lg border border-gray-100 bg-white rounded-xl transition-all duration-300 hover:shadow-xl">
      <CardHeader className="text-center space-y-4 pb-4 pt-8">
        <CardTitle className="flex items-center justify-center gap-2 text-2xl md:text-3xl font-semibold text-[#032221]">
          <Shield className="h-7 w-7 text-[#01796F]" />
          Join Pedi
        </CardTitle>
        <CardDescription className="text-gray-600 font-medium text-sm md:text-base">
          {step === 'details'
            ? 'Create your account to start your sustainable fashion journey'
            : 'Enter the OTP sent to your phone to complete registration'}
        </CardDescription>
      </CardHeader>

      <CardContent className="space-y-6 p-6">
        {error && (
          <Alert variant="destructive" className="animate-fade-in">
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}
        {success && (
          <Alert className="bg-green-50 border-green-200 animate-fade-in">
            <AlertDescription className="text-green-800">{success}</AlertDescription>
          </Alert>
        )}

        {step === 'details' ? (
          <form onSubmit={handleDetailsSubmit} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="firstName" className="font-medium text-gray-700">
                  First Name
                </Label>
                <div className="relative">
                  <User className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    id="firstName"
                    type="text"
                    placeholder="John"
                    value={formData.firstName}
                    onChange={(e) => handleInputChange('firstName', e.target.value)}
                    className="pl-10 border-gray-200 focus:border-[#01796F] focus:ring-[#01796F] rounded-lg"
                    required
                  />
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="lastName" className="font-medium text-gray-700">
                  Last Name
                </Label>
                <Input
                  id="lastName"
                  type="text"
                  placeholder="Doe"
                  value={formData.lastName}
                  onChange={(e) => handleInputChange('lastName', e.target.value)}
                  className="border-gray-200 focus:border-[#01796F] focus:ring-[#01796F] rounded-lg"
                  required
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="phone" className="font-medium text-gray-700">
                Phone Number
              </Label>
              <div className="relative">
                <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  id="phone"
                  type="tel"
                  placeholder="0712345678 or +254712345678"
                  value={formData.phoneNumber}
                  onChange={(e) => handleInputChange('phoneNumber', e.target.value)}
                  className="pl-10 border-gray-200 focus:border-[#01796F] focus:ring-[#01796F] rounded-lg"
                  required
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="email" className="font-medium text-gray-700">
                Email (Optional)
              </Label>
              <Input
                id="email"
                type="email"
                placeholder="<EMAIL>"
                value={formData.email}
                onChange={(e) => handleInputChange('email', e.target.value)}
                className="border-gray-200 focus:border-[#01796F] focus:ring-[#01796F] rounded-lg"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="county" className="font-medium text-gray-700">
                County
              </Label>
              <div className="relative">
                <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 z-10" />
                <Select
                  value={formData.county}
                  onValueChange={(value) => handleInputChange('county', value)}
                >
                  <SelectTrigger className="pl-10 border-gray-200 focus:border-[#01796F] focus:ring-[#01796F] rounded-lg">
                    <SelectValue placeholder="Select your county" />
                  </SelectTrigger>
                  <SelectContent>
                    {KENYAN_COUNTIES.map((county) => (
                      <SelectItem key={county} value={county}>
                        {county}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="town" className="font-medium text-gray-700">
                Town/Area
              </Label>
              <Input
                id="town"
                type="text"
                placeholder="e.g., Westlands, CBD, Kisumu Town"
                value={formData.town}
                onChange={(e) => handleInputChange('town', e.target.value)}
                className="border-gray-200 focus:border-[#01796F] focus:ring-[#01796F] rounded-lg"
                required
              />
            </div>

            <Button
              type="submit"
              className="w-full bg-[#01796F] hover:bg-[#005d55] text-white font-medium rounded-lg transition-colors"
              disabled={loading}
            >
              {loading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Creating Account...
                </>
              ) : (
                'Create Account'
              )}
            </Button>
          </form>
        ) : (
          <form onSubmit={handleOtpSubmit} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="otp" className="font-medium text-gray-700">
                Verification Code
              </Label>
              <Input
                id="otp"
                type="text"
                placeholder="Enter 6-digit code"
                value={otp}
                onChange={(e) =>
                  setOtp(e.target.value.replace(/\D/g, '').slice(0, 6))
                }
                maxLength={6}
                className="text-center text-lg tracking-widest border-gray-200 focus:border-[#01796F] focus:ring-[#01796F] rounded-lg"
                required
              />
              <p className="text-sm text-gray-500">
                Code sent to {formData.phoneNumber}
              </p>
            </div>

            <Button
              type="submit"
              className="w-full bg-[#01796F] hover:bg-[#005d55] text-white font-medium rounded-lg transition-colors"
              disabled={loading || otp.length !== 6}
            >
              {loading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Verifying...
                </>
              ) : (
                'Verify & Complete Registration'
              )}
            </Button>

            <div className="flex justify-between text-sm mt-2">
              <button
                type="button"
                onClick={() => setStep('details')}
                className="text-blue-600 hover:underline font-medium"
              >
                Change Details
              </button>
              <button
                type="button"
                onClick={handleResendOtp}
                className="text-blue-600 hover:underline font-medium"
                disabled={loading}
              >
                Resend OTP
              </button>
            </div>
          </form>
        )}

        {onSwitchToLogin && (
          <div className="mt-6 text-center">
            <p className="text-sm text-gray-600">
              Already have an account?{' '}
              <button
                onClick={onSwitchToLogin}
                className="text-[#01796F] hover:underline font-medium"
              >
                Sign in here
              </button>
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}