'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  Shirt, 
  Sun, 
  Cloud, 
  CloudRain, 
  Snowflake,
  Calendar,
  Sparkles,
  RefreshCw,
  Heart,
  Loader2,
  AlertCircle,
  CheckCircle,
  Wand2
} from 'lucide-react';

interface ClothingItem {
  _id: string;
  title: string;
  category: string;
  color: string;
  images: string[];
}

interface OutfitSuggestion {
  success: boolean;
  advice?: string;
  recommendations?: {
    items?: ClothingItem[];
    tips?: string[];
    sustainabilityScore?: number;
  };
  error?: string;
}

interface OutfitSuggestionsProps {
  className?: string;
}

export function OutfitSuggestions({ className }: OutfitSuggestionsProps) {
  const [occasion, setOccasion] = useState('casual');
  const [weather, setWeather] = useState('warm');
  const [suggestions, setSuggestions] = useState<OutfitSuggestion | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const occasions = [
    { value: 'casual', label: 'Casual Day Out', icon: Shirt },
    { value: 'work', label: 'Work/Professional', icon: Calendar },
    { value: 'formal', label: 'Formal Event', icon: Sparkles },
    { value: 'date', label: 'Date Night', icon: Heart },
    { value: 'exercise', label: 'Exercise/Sports', icon: RefreshCw },
    { value: 'party', label: 'Party/Social', icon: Sparkles }
  ];

  const weatherOptions = [
    { value: 'hot', label: 'Hot & Sunny', icon: Sun, color: 'text-orange-500' },
    { value: 'warm', label: 'Warm', icon: Sun, color: 'text-yellow-500' },
    { value: 'cool', label: 'Cool', icon: Cloud, color: 'text-blue-500' },
    { value: 'cold', label: 'Cold', icon: Snowflake, color: 'text-blue-700' },
    { value: 'rainy', label: 'Rainy', icon: CloudRain, color: 'text-gray-500' }
  ];

  const generateOutfitSuggestions = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('Please log in to get outfit suggestions');
      }

      const response = await fetch('/api/ai?endpoint=outfit-suggestions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({
          occasion,
          weather
        }),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.message || 'Failed to get outfit suggestions');
      }

      setSuggestions(result.data);
    } catch (error: any) {
      console.error('Error getting outfit suggestions:', error);
      setError(error.message);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    generateOutfitSuggestions();
  }, []);

  const handleRefresh = () => {
    generateOutfitSuggestions();
  };

  const getWeatherIcon = (weatherValue: string) => {
    const option = weatherOptions.find(w => w.value === weatherValue);
    if (!option) return Sun;
    return option.icon;
  };

  const getWeatherColor = (weatherValue: string) => {
    const option = weatherOptions.find(w => w.value === weatherValue);
    return option?.color || 'text-gray-500';
  };

  const getOccasionIcon = (occasionValue: string) => {
    const option = occasions.find(o => o.value === occasionValue);
    return option?.icon || Shirt;
  };

  const getSustainabilityBadge = (score?: number) => {
    if (!score) return null;
    
    let color = 'bg-gray-100 text-gray-800';
    let label = 'Low';
    
    if (score >= 70) {
      color = 'bg-green-100 text-green-800';
      label = 'High';
    } else if (score >= 40) {
      color = 'bg-yellow-100 text-yellow-800';
      label = 'Medium';
    }

    return (
      <Badge className={`${color} text-xs`}>
        <Heart className="h-3 w-3 mr-1" />
        Sustainability: {label}
      </Badge>
    );
  };

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Wand2 className="h-5 w-5 text-purple-600" />
          AI Outfit Suggestions
        </CardTitle>
        <CardDescription>
          Get personalized outfit recommendations based on occasion and weather
        </CardDescription>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Controls */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <label className="text-sm font-medium">Occasion</label>
            <Select value={occasion} onValueChange={setOccasion}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {occasions.map((occ) => {
                  const IconComponent = occ.icon;
                  return (
                    <SelectItem key={occ.value} value={occ.value}>
                      <div className="flex items-center gap-2">
                        <IconComponent className="h-4 w-4" />
                        {occ.label}
                      </div>
                    </SelectItem>
                  );
                })}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium">Weather</label>
            <Select value={weather} onValueChange={setWeather}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {weatherOptions.map((w) => {
                  const IconComponent = w.icon;
                  return (
                    <SelectItem key={w.value} value={w.value}>
                      <div className="flex items-center gap-2">
                        <IconComponent className={`h-4 w-4 ${w.color}`} />
                        {w.label}
                      </div>
                    </SelectItem>
                  );
                })}
              </SelectContent>
            </Select>
          </div>
        </div>

        <div className="flex justify-between items-center">
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2 text-sm text-gray-600">
              {React.createElement(getOccasionIcon(occasion), { className: "h-4 w-4" })}
              {occasions.find(o => o.value === occasion)?.label}
            </div>
            <div className={`flex items-center gap-2 text-sm ${getWeatherColor(weather)}`}>
              {React.createElement(getWeatherIcon(weather), { className: "h-4 w-4" })}
              {weatherOptions.find(w => w.value === weather)?.label}
            </div>
          </div>
          
          <Button
            onClick={handleRefresh}
            disabled={isLoading}
            variant="outline"
            size="sm"
          >
            {isLoading ? (
              <Loader2 className="h-4 w-4 animate-spin mr-2" />
            ) : (
              <RefreshCw className="h-4 w-4 mr-2" />
            )}
            Refresh
          </Button>
        </div>

        {/* Error State */}
        {error && (
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* Loading State */}
        {isLoading && (
          <div className="flex items-center justify-center py-8">
            <div className="text-center">
              <Loader2 className="h-8 w-8 animate-spin mx-auto mb-2 text-purple-600" />
              <p className="text-sm text-gray-600">Generating outfit suggestions...</p>
            </div>
          </div>
        )}

        {/* Suggestions */}
        {suggestions && !isLoading && (
          <div className="space-y-4">
            {suggestions.success ? (
              <>
                {/* AI Advice */}
                <div className="bg-gradient-to-r from-purple-50 to-pink-50 rounded-lg p-4">
                  <div className="flex items-start gap-3">
                    <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center flex-shrink-0">
                      <Sparkles className="h-4 w-4 text-white" />
                    </div>
                    <div className="flex-1">
                      <h3 className="font-medium text-gray-900 mb-2">AI Styling Advice</h3>
                      <p className="text-sm text-gray-700 whitespace-pre-wrap">{suggestions.advice}</p>
                      
                      {suggestions.recommendations?.sustainabilityScore && (
                        <div className="mt-3">
                          {getSustainabilityBadge(suggestions.recommendations.sustainabilityScore)}
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                {/* Recommended Items */}
                {suggestions.recommendations?.items && suggestions.recommendations.items.length > 0 && (
                  <div>
                    <h3 className="font-medium text-gray-900 mb-3 flex items-center gap-2">
                      <Shirt className="h-4 w-4" />
                      Items from Your Wardrobe
                    </h3>
                    <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
                      {suggestions.recommendations.items.map((item) => (
                        <div key={item._id} className="bg-white border rounded-lg p-3 hover:shadow-md transition-shadow">
                          {item.images && item.images.length > 0 ? (
                            <img
                              src={item.images[0]}
                              alt={item.title}
                              className="w-full h-24 object-cover rounded-md mb-2"
                            />
                          ) : (
                            <div className="w-full h-24 bg-gray-100 rounded-md mb-2 flex items-center justify-center">
                              <Shirt className="h-8 w-8 text-gray-400" />
                            </div>
                          )}
                          <h4 className="font-medium text-sm text-gray-900 truncate">{item.title}</h4>
                          <div className="flex items-center gap-2 mt-1">
                            <Badge variant="secondary" className="text-xs">
                              {item.category}
                            </Badge>
                            <span className="text-xs text-gray-500">{item.color}</span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Additional Tips */}
                {suggestions.recommendations?.tips && suggestions.recommendations.tips.length > 0 && (
                  <div>
                    <h3 className="font-medium text-gray-900 mb-3 flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-green-600" />
                      Styling Tips
                    </h3>
                    <div className="space-y-2">
                      {suggestions.recommendations.tips.map((tip, index) => (
                        <div key={index} className="flex items-start gap-2 text-sm text-gray-700">
                          <div className="w-1.5 h-1.5 bg-purple-500 rounded-full mt-2 flex-shrink-0" />
                          <span>{tip}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </>
            ) : (
              <Alert>
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  {suggestions.advice || 'Unable to generate outfit suggestions at this time.'}
                </AlertDescription>
              </Alert>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
