'use client';

import { useState } from 'react';
import { Check, CheckChe<PERSON>, Clock, AlertCircle } from 'lucide-react';

interface MessageSender {
  id: string;
  firstName: string;
  lastName: string;
  profilePicture?: string;
}

interface Message {
  id: string;
  sender: MessageSender;
  content: string;
  type: 'text' | 'image' | 'system';
  timestamp: string;
  isRead: boolean;
  deliveryStatus?: 'sending' | 'sent' | 'delivered' | 'failed';
}

interface MessageBubbleProps {
  message: Message;
  isOwn: boolean;
  showAvatar?: boolean;
  showTimestamp?: boolean;
}

export default function MessageBubble({
  message,
  isOwn,
  showAvatar = true,
  showTimestamp = true
}: MessageBubbleProps) {
  const [imageError, setImageError] = useState(false);

  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  const getDeliveryIcon = () => {
    if (!isOwn) return null;

    switch (message.deliveryStatus) {
      case 'sending':
        return <Clock className="h-3 w-3 text-gray-400" />;
      case 'sent':
        return <Check className="h-3 w-3 text-gray-400" />;
      case 'delivered':
        return <CheckCheck className="h-3 w-3 text-gray-500" />;
      case 'failed':
        return <AlertCircle className="h-3 w-3 text-red-500" />;
      default:
        return message.isRead ? (
          <CheckCheck className="h-3 w-3 text-green-500" />
        ) : (
          <CheckCheck className="h-3 w-3 text-gray-400" />
        );
    }
  };

  // System messages (like "User joined the chat")
  if (message.type === 'system') {
    return (
      <div className="flex justify-center my-4">
        <div className="bg-gray-100 text-gray-600 text-xs px-3 py-1 rounded-full">
          {message.content}
        </div>
      </div>
    );
  }

  return (
    <div className={`flex ${isOwn ? 'justify-end' : 'justify-start'} mb-4`}>
      <div className={`flex ${isOwn ? 'flex-row-reverse' : 'flex-row'} items-end space-x-2 max-w-xs lg:max-w-md`}>
        {/* Avatar */}
        {showAvatar && !isOwn && (
          <div className="w-8 h-8 rounded-full bg-green-100 flex items-center justify-center flex-shrink-0">
            {message.sender.profilePicture ? (
              <img
                src={message.sender.profilePicture}
                alt={`${message.sender.firstName} ${message.sender.lastName}`}
                className="w-8 h-8 rounded-full object-cover"
              />
            ) : (
              <span className="text-green-600 text-sm font-medium">
                {message.sender.firstName[0]}{message.sender.lastName[0]}
              </span>
            )}
          </div>
        )}

        {/* Message Content */}
        <div className={`flex flex-col ${isOwn ? 'items-end' : 'items-start'}`}>
          {/* Sender name for received messages */}
          {!isOwn && showAvatar && (
            <span className="text-xs text-gray-500 mb-1 px-3">
              {message.sender.firstName} {message.sender.lastName}
            </span>
          )}

          {/* Message bubble */}
          <div
            className={`relative px-4 py-2 rounded-2xl ${
              isOwn
                ? 'bg-green-500 text-white rounded-br-md'
                : 'bg-gray-100 text-gray-900 rounded-bl-md'
            }`}
          >
            {message.type === 'image' ? (
              <div className="relative">
                {!imageError ? (
                  <img
                    src={message.content}
                    alt="Shared image"
                    className="max-w-full h-auto rounded-lg"
                    onError={() => setImageError(true)}
                  />
                ) : (
                  <div className="w-48 h-32 bg-gray-200 rounded-lg flex items-center justify-center">
                    <span className="text-gray-500 text-sm">Image failed to load</span>
                  </div>
                )}
              </div>
            ) : (
              <p className="text-sm whitespace-pre-wrap break-words">
                {message.content}
              </p>
            )}

            {/* Message status and timestamp */}
            <div className={`flex items-center justify-end mt-1 space-x-1 ${
              isOwn ? 'text-green-100' : 'text-gray-500'
            }`}>
              {showTimestamp && (
                <span className="text-xs">
                  {formatTimestamp(message.timestamp)}
                </span>
              )}
              {getDeliveryIcon()}
            </div>
          </div>

          {/* Delivery status text for failed messages */}
          {isOwn && message.deliveryStatus === 'failed' && (
            <span className="text-xs text-red-500 mt-1 px-3">
              Failed to send. Tap to retry.
            </span>
          )}
        </div>
      </div>
    </div>
  );
}
