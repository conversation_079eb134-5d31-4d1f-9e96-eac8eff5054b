'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Search, MessageCircle, Plus } from 'lucide-react';

interface ChatParticipant {
  id: string;
  firstName: string;
  lastName: string;
  profilePicture?: string;
}

interface ChatConversation {
  id: string;
  participants: ChatParticipant[];
  lastMessage?: {
    content: string;
    sender: string;
    timestamp: string;
  };
  unreadCount: number;
  relatedClothingItem?: {
    id: string;
    title: string;
    images: string[];
  };
  updatedAt: string;
}

interface ChatListProps {
  conversations: ChatConversation[];
  selectedChatId?: string;
  onChatSelect: (chatId: string) => void;
  onNewChat: () => void;
  currentUserId: string;
  loading?: boolean;
}

export default function ChatList({
  conversations,
  selectedChatId,
  onChatSelect,
  onNewChat,
  currentUserId,
  loading = false
}: ChatListProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [filteredConversations, setFilteredConversations] = useState<ChatConversation[]>([]);

  useEffect(() => {
    if (!searchTerm) {
      setFilteredConversations(conversations);
    } else {
      const filtered = conversations.filter(chat => {
        const otherParticipant = chat.participants.find(p => p.id !== currentUserId);
        const participantName = otherParticipant 
          ? `${otherParticipant.firstName} ${otherParticipant.lastName}`.toLowerCase()
          : '';
        const lastMessageContent = chat.lastMessage?.content.toLowerCase() || '';
        const clothingTitle = chat.relatedClothingItem?.title.toLowerCase() || '';
        
        return participantName.includes(searchTerm.toLowerCase()) ||
               lastMessageContent.includes(searchTerm.toLowerCase()) ||
               clothingTitle.includes(searchTerm.toLowerCase());
      });
      setFilteredConversations(filtered);
    }
  }, [conversations, searchTerm, currentUserId]);

  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 1) {
      return 'Just now';
    } else if (diffInHours < 24) {
      return `${Math.floor(diffInHours)}h ago`;
    } else if (diffInHours < 48) {
      return 'Yesterday';
    } else {
      return date.toLocaleDateString();
    }
  };

  const getOtherParticipant = (participants: ChatParticipant[]) => {
    return participants.find(p => p.id !== currentUserId);
  };

  if (loading) {
    return (
      <Card className="h-full">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MessageCircle className="h-5 w-5" />
            Messages
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="animate-pulse">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-gray-200 rounded-full"></div>
                  <div className="flex-1 space-y-2">
                    <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                    <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="h-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <MessageCircle className="h-5 w-5" />
            Messages
          </CardTitle>
          <Button size="sm" onClick={onNewChat}>
            <Plus className="h-4 w-4" />
          </Button>
        </div>
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <Input
            placeholder="Search conversations..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
      </CardHeader>
      <CardContent className="p-0">
        <div className="max-h-96 overflow-y-auto">
          {filteredConversations.length === 0 ? (
            <div className="p-6 text-center text-gray-500">
              {searchTerm ? 'No conversations found' : 'No conversations yet'}
            </div>
          ) : (
            filteredConversations.map((chat) => {
              const otherParticipant = getOtherParticipant(chat.participants);
              const isSelected = chat.id === selectedChatId;
              
              return (
                <div
                  key={chat.id}
                  onClick={() => onChatSelect(chat.id)}
                  className={`p-4 border-b cursor-pointer hover:bg-gray-50 transition-colors ${
                    isSelected ? 'bg-green-50 border-l-4 border-l-green-500' : ''
                  }`}
                >
                  <div className="flex items-start space-x-3">
                    <div className="relative">
                      <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                        {otherParticipant?.profilePicture ? (
                          <img
                            src={otherParticipant.profilePicture}
                            alt={`${otherParticipant.firstName} ${otherParticipant.lastName}`}
                            className="w-10 h-10 rounded-full object-cover"
                          />
                        ) : (
                          <span className="text-green-600 font-medium">
                            {otherParticipant?.firstName?.[0]}{otherParticipant?.lastName?.[0]}
                          </span>
                        )}
                      </div>
                      {chat.unreadCount > 0 && (
                        <Badge 
                          variant="destructive" 
                          className="absolute -top-1 -right-1 h-5 w-5 rounded-full p-0 flex items-center justify-center text-xs"
                        >
                          {chat.unreadCount > 9 ? '9+' : chat.unreadCount}
                        </Badge>
                      )}
                    </div>
                    
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between">
                        <h4 className="font-medium text-gray-900 truncate">
                          {otherParticipant ? `${otherParticipant.firstName} ${otherParticipant.lastName}` : 'Unknown User'}
                        </h4>
                        <span className="text-xs text-gray-500">
                          {chat.lastMessage && formatTimestamp(chat.lastMessage.timestamp)}
                        </span>
                      </div>
                      
                      {chat.relatedClothingItem && (
                        <p className="text-xs text-green-600 mb-1">
                          About: {chat.relatedClothingItem.title}
                        </p>
                      )}
                      
                      {chat.lastMessage && (
                        <p className="text-sm text-gray-600 truncate">
                          {chat.lastMessage.sender === currentUserId ? 'You: ' : ''}
                          {chat.lastMessage.content}
                        </p>
                      )}
                    </div>
                  </div>
                </div>
              );
            })
          )}
        </div>
      </CardContent>
    </Card>
  );
}
