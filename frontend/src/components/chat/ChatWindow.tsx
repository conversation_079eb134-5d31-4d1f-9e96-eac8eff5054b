'use client';

import { useState, useEffect, useRef } from 'react';
import { <PERSON>, CardContent, CardHeader } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ArrowLeft, MoreVertical, Phone, Video, Search } from 'lucide-react';
import MessageBubble from './MessageBubble';
import MessageInput from './MessageInput';
import MessageSearch from './MessageSearch';
import OnlineStatus from './OnlineStatus';

interface ChatParticipant {
  id: string;
  firstName: string;
  lastName: string;
  profilePicture?: string;
  isOnline?: boolean;
  lastSeen?: string;
}

interface Message {
  id: string;
  sender: ChatParticipant;
  content: string;
  type: 'text' | 'image' | 'system';
  timestamp: string;
  isRead: boolean;
  deliveryStatus?: 'sending' | 'sent' | 'delivered' | 'failed';
}

interface ChatConversation {
  id: string;
  participants: ChatParticipant[];
  messages: Message[];
  relatedClothingItem?: {
    id: string;
    title: string;
    images: string[];
  };
}

interface TypingUser {
  userId: string;
  user: {
    firstName: string;
    lastName: string;
  };
}

interface ChatWindowProps {
  conversation: ChatConversation | null;
  currentUserId: string;
  onSendMessage: (content: string, type?: 'text' | 'image') => void;
  onTypingStart: () => void;
  onTypingStop: () => void;
  onBack?: () => void;
  typingUsers: TypingUser[];
  loading?: boolean;
  loadingMessages?: boolean;
}

export default function ChatWindow({
  conversation,
  currentUserId,
  onSendMessage,
  onTypingStart,
  onTypingStop,
  onBack,
  typingUsers,
  loading = false,
  loadingMessages = false
}: ChatWindowProps) {
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const messagesContainerRef = useRef<HTMLDivElement>(null);
  const [isSearchOpen, setIsSearchOpen] = useState(false);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [conversation?.messages]);

  const getOtherParticipant = () => {
    if (!conversation) return null;
    return conversation.participants.find(p => p.id !== currentUserId);
  };



  const getTypingText = () => {
    if (typingUsers.length === 0) return '';
    if (typingUsers.length === 1) {
      return `${typingUsers[0].user.firstName} is typing...`;
    }
    return `${typingUsers.length} people are typing...`;
  };

  const handleSearchResultSelect = (messageId: string) => {
    // Scroll to the specific message
    const messageElement = document.getElementById(`message-${messageId}`);
    if (messageElement) {
      messageElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
      // Highlight the message briefly
      messageElement.classList.add('bg-yellow-100');
      setTimeout(() => {
        messageElement.classList.remove('bg-yellow-100');
      }, 2000);
    }
  };

  if (!conversation) {
    return (
      <Card className="h-full flex items-center justify-center">
        <CardContent>
          <div className="text-center text-gray-500">
            <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <span className="text-2xl">💬</span>
            </div>
            <h3 className="text-lg font-medium mb-2">No conversation selected</h3>
            <p className="text-sm">Choose a conversation from the list to start messaging</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  const otherParticipant = getOtherParticipant();

  return (
    <Card className="h-full flex flex-col">
      {/* Message Search */}
      <MessageSearch
        isOpen={isSearchOpen}
        onClose={() => setIsSearchOpen(false)}
        onResultSelect={handleSearchResultSelect}
        chatId={conversation.id}
      />

      {/* Chat Header */}
      <CardHeader className="border-b p-4 relative">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            {onBack && (
              <Button variant="ghost" size="sm" onClick={onBack} className="md:hidden">
                <ArrowLeft className="h-4 w-4" />
              </Button>
            )}
            
            <div className="relative">
              <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                {otherParticipant?.profilePicture ? (
                  <img
                    src={otherParticipant.profilePicture}
                    alt={`${otherParticipant.firstName} ${otherParticipant.lastName}`}
                    className="w-10 h-10 rounded-full object-cover"
                  />
                ) : (
                  <span className="text-green-600 font-medium">
                    {otherParticipant?.firstName?.[0]}{otherParticipant?.lastName?.[0]}
                  </span>
                )}
              </div>
              {otherParticipant && (
                <OnlineStatus userId={otherParticipant.id} />
              )}
            </div>
            
            <div>
              <h3 className="font-medium">
                {otherParticipant ? `${otherParticipant.firstName} ${otherParticipant.lastName}` : 'Unknown User'}
              </h3>
              {otherParticipant && (
                <OnlineStatus
                  userId={otherParticipant.id}
                  showText={true}
                  className="text-sm"
                />
              )}
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsSearchOpen(!isSearchOpen)}
              className={isSearchOpen ? 'bg-green-100' : ''}
            >
              <Search className="h-4 w-4" />
            </Button>
            <Button variant="ghost" size="sm">
              <Phone className="h-4 w-4" />
            </Button>
            <Button variant="ghost" size="sm">
              <Video className="h-4 w-4" />
            </Button>
            <Button variant="ghost" size="sm">
              <MoreVertical className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* Related clothing item */}
        {conversation.relatedClothingItem && (
          <div className="mt-3 p-3 bg-green-50 rounded-lg">
            <div className="flex items-center space-x-3">
              <img
                src={conversation.relatedClothingItem.images[0]}
                alt={conversation.relatedClothingItem.title}
                className="w-12 h-12 rounded-lg object-cover"
              />
              <div>
                <p className="text-sm font-medium text-green-800">
                  Discussing: {conversation.relatedClothingItem.title}
                </p>
                <p className="text-xs text-green-600">
                  Tap to view item details
                </p>
              </div>
            </div>
          </div>
        )}
      </CardHeader>

      {/* Messages Area */}
      <CardContent className="flex-1 overflow-hidden p-0">
        <div 
          ref={messagesContainerRef}
          className="h-full overflow-y-auto p-4 space-y-1"
        >
          {loadingMessages ? (
            <div className="flex justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-500"></div>
            </div>
          ) : (
            <>
              {conversation.messages.map((message, index) => {
                const isOwn = message.sender.id === currentUserId;
                const showAvatar = !isOwn && (
                  index === 0 || 
                  conversation.messages[index - 1].sender.id !== message.sender.id
                );
                const showTimestamp = index === conversation.messages.length - 1 || 
                  conversation.messages[index + 1].sender.id !== message.sender.id;

                return (
                  <div key={message.id} id={`message-${message.id}`}>
                    <MessageBubble
                      message={message}
                      isOwn={isOwn}
                      showAvatar={showAvatar}
                      showTimestamp={showTimestamp}
                    />
                  </div>
                );
              })}
              
              {/* Typing indicator */}
              {typingUsers.length > 0 && (
                <div className="flex justify-start mb-4">
                  <div className="bg-gray-100 text-gray-600 px-4 py-2 rounded-2xl rounded-bl-md">
                    <div className="flex items-center space-x-1">
                      <div className="flex space-x-1">
                        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                      </div>
                      <span className="text-xs ml-2">{getTypingText()}</span>
                    </div>
                  </div>
                </div>
              )}
              
              <div ref={messagesEndRef} />
            </>
          )}
        </div>
      </CardContent>

      {/* Message Input */}
      <MessageInput
        onSendMessage={onSendMessage}
        onTypingStart={onTypingStart}
        onTypingStop={onTypingStop}
        disabled={loading}
        placeholder={`Message ${otherParticipant?.firstName || 'user'}...`}
      />
    </Card>
  );
}
