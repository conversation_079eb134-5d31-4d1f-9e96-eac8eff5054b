# Pedi Notification Components

A comprehensive notification system for the Pedi sustainable fashion platform, providing real-time notifications, user preferences management, and analytics.

## Components Overview

### Core Components

1. **NotificationProvider** - Context provider for notification state management
2. **NotificationBell** - Header notification bell with dropdown
3. **NotificationList** - Full notification history with filtering
4. **NotificationPreferences** - User notification settings
5. **NotificationStats** - Analytics and system health monitoring
6. **NotificationToast** - Real-time notification toasts

## Quick Start

### 1. Wrap your app with NotificationProvider

```tsx
import { NotificationProvider } from '@/components/notifications';

function App() {
  return (
    <NotificationProvider>
      <YourAppContent />
    </NotificationProvider>
  );
}
```

### 2. Add NotificationBell to your header

```tsx
import { NotificationBell } from '@/components/notifications';

function Header() {
  return (
    <header>
      <NotificationBell
        onNotificationClick={(notification) => {
          // Handle notification click
          console.log('Clicked:', notification);
        }}
        onViewAllClick={() => {
          // Navigate to notifications page
          router.push('/notifications');
        }}
        onSettingsClick={() => {
          // Navigate to notification settings
          router.push('/notifications?tab=preferences');
        }}
      />
    </header>
  );
}
```

### 3. Add toast notifications

```tsx
import { NotificationToastContainer } from '@/components/notifications';

function Layout() {
  return (
    <div>
      <YourContent />
      <NotificationToastContainer
        position="top-right"
        maxToasts={3}
        autoClose={true}
        autoCloseDelay={5000}
      />
    </div>
  );
}
```

## Component Details

### NotificationProvider

Context provider that manages notification state across the application.

**Props:**
- `autoRefresh?: boolean` - Enable automatic notification fetching (default: true)
- `refreshInterval?: number` - Refresh interval in milliseconds (default: 30000)
- `maxNotifications?: number` - Maximum notifications to keep in memory (default: 50)

**Context API:**
```tsx
const {
  notifications,      // Array of notifications
  unreadCount,        // Number of unread notifications
  loading,            // Loading state
  error,              // Error message
  fetchNotifications, // Fetch notifications manually
  markAsRead,         // Mark notification as read
  markAllAsRead,      // Mark all notifications as read
  deleteNotification, // Delete a notification
  addNotification,    // Add new notification (for real-time)
  clearError,         // Clear error state
  isConnected,        // Real-time connection status
  lastUpdated,        // Last update timestamp
} = useNotifications();
```

### NotificationBell

Header notification bell component with dropdown preview.

**Props:**
- `onNotificationClick?: (notification) => void` - Handle notification click
- `onViewAllClick?: () => void` - Handle "View All" click
- `onSettingsClick?: () => void` - Handle settings click
- `maxDisplayCount?: number` - Max notifications in dropdown (default: 5)
- `autoRefresh?: boolean` - Auto-refresh notifications (default: true)
- `refreshInterval?: number` - Refresh interval (default: 30000ms)

### NotificationList

Full notification list with filtering and pagination.

**Props:**
- `onNotificationClick?: (notification) => void` - Handle notification click
- `showFilters?: boolean` - Show filter controls (default: true)
- `pageSize?: number` - Items per page (default: 20)
- `autoRefresh?: boolean` - Auto-refresh (default: true)

**Features:**
- Category filtering (payment, exchange, token, system, etc.)
- Status filtering (delivered, sent, pending, failed)
- Priority filtering (urgent, high, medium, low)
- Pagination
- Mark as read/delete actions
- Real-time updates

### NotificationPreferences

User notification preferences management.

**Features:**
- Channel preferences (SMS, push, email, in-app)
- Category preferences (payment, exchange, token, etc.)
- Quiet hours configuration
- Frequency limits
- Language settings
- Reset to defaults

### NotificationStats

Analytics and monitoring dashboard.

**Features:**
- Delivery statistics
- Performance by category/channel/priority
- Service health monitoring
- Failure analysis
- Real-time metrics
- Time range filtering

### NotificationToast

Real-time notification toast component.

**Props:**
- `notification: BaseNotification` - Notification to display
- `onClose: () => void` - Close handler
- `onAction?: (notification) => void` - Action button handler
- `autoClose?: boolean` - Auto-close (default: true)
- `autoCloseDelay?: number` - Auto-close delay (default: 5000ms)
- `position?: string` - Toast position (default: 'top-right')

**NotificationToastContainer Props:**
- `maxToasts?: number` - Max simultaneous toasts (default: 3)
- `position?: string` - Container position
- `autoClose?: boolean` - Auto-close toasts
- `autoCloseDelay?: number` - Auto-close delay
- `onNotificationAction?: (notification) => void` - Action handler

## Notification Types

### Categories
- `payment` - Payment confirmations, failures
- `exchange` - Exchange requests, updates
- `token` - Token earnings, achievements
- `system` - System alerts, maintenance
- `security` - Security notifications
- `promotion` - Promotional messages
- `reminder` - Pickup reminders, deadlines
- `welcome` - Welcome messages

### Priorities
- `urgent` - Critical notifications (red)
- `high` - Important notifications (orange)
- `medium` - Standard notifications (blue)
- `low` - Informational notifications (gray)

### Channels
- `sms` - SMS text messages
- `push` - Browser push notifications
- `email` - Email notifications
- `in-app` - In-app notifications

### Status
- `pending` - Queued for sending
- `sent` - Sent to provider
- `delivered` - Confirmed delivery
- `failed` - Delivery failed
- `read` - Read by user

## API Integration

The components automatically integrate with the backend notification API:

- `GET /api/notifications` - Fetch notifications
- `PATCH /api/notifications/:id/read` - Mark as read
- `DELETE /api/notifications/:id` - Delete notification
- `GET /api/notifications/preferences` - Get preferences
- `PUT /api/notifications/preferences` - Update preferences
- `GET /api/notifications/stats` - Get statistics
- `GET /api/notifications/health` - Service health

## Real-time Features

### Server-Sent Events (SSE)
Automatic real-time updates via `/api/notifications/stream` endpoint.

### Browser Notifications
Automatic browser notifications for high-priority messages (requires permission).

### Auto-refresh
Configurable auto-refresh for components that don't support real-time.

## Styling

Components use Tailwind CSS and shadcn/ui components for consistent styling:

- Responsive design
- Dark mode support (via CSS variables)
- Accessible color schemes
- Priority-based color coding
- Smooth animations

## Best Practices

1. **Wrap your app** with NotificationProvider at the root level
2. **Use NotificationBell** in your main header/navigation
3. **Add ToastContainer** to your main layout
4. **Handle notification clicks** to navigate to relevant pages
5. **Respect user preferences** - check settings before showing notifications
6. **Monitor performance** using NotificationStats
7. **Test thoroughly** using the demo page

## Demo

Use the `NotificationDemo` component to test and preview all notification features:

```tsx
import NotificationDemo from '@/pages/NotificationDemo';

// Render the demo page
<NotificationDemo />
```

## Troubleshooting

### Common Issues

1. **Notifications not appearing**: Check NotificationProvider is wrapping your app
2. **Real-time not working**: Verify SSE endpoint and authentication
3. **Toasts not showing**: Ensure ToastContainer is in your layout
4. **API errors**: Check authentication tokens and API endpoints

### Debug Mode

Enable debug logging by setting localStorage:
```javascript
localStorage.setItem('pedi_debug_notifications', 'true');
```

## Future Enhancements

- Push notification service worker
- Email template customization
- Advanced analytics dashboard
- A/B testing for notification content
- Multi-language support
- Notification scheduling
- Rich media notifications
