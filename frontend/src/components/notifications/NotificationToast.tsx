import React, { useState, useEffect } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  X, 
  Bell, 
  CheckCircle, 
  AlertCircle, 
  Info, 
  Gift,
  CreditCard,
  MessageSquare,
  Settings,
  Shield,
  Megaphone,
  Calendar,
  UserPlus
} from 'lucide-react';
import { useNotifications, type BaseNotification } from './index';

interface NotificationToastProps {
  notification: BaseNotification;
  onClose: () => void;
  onAction?: (notification: BaseNotification) => void;
  autoClose?: boolean;
  autoCloseDelay?: number;
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left';
}

const NotificationToast: React.FC<NotificationToastProps> = ({
  notification,
  onClose,
  onAction,
  autoClose = true,
  autoCloseDelay = 5000,
  position = 'top-right',
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [isClosing, setIsClosing] = useState(false);
  const { markAsRead } = useNotifications();

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'payment': return <CreditCard className="h-4 w-4" />;
      case 'exchange': return <MessageSquare className="h-4 w-4" />;
      case 'token': return <Gift className="h-4 w-4" />;
      case 'system': return <Settings className="h-4 w-4" />;
      case 'security': return <Shield className="h-4 w-4" />;
      case 'promotion': return <Megaphone className="h-4 w-4" />;
      case 'reminder': return <Calendar className="h-4 w-4" />;
      case 'welcome': return <UserPlus className="h-4 w-4" />;
      default: return <Bell className="h-4 w-4" />;
    }
  };

  const getPriorityStyles = (priority: string) => {
    switch (priority) {
      case 'urgent':
        return {
          border: 'border-l-4 border-l-red-500',
          bg: 'bg-red-50',
          icon: <AlertCircle className="h-4 w-4 text-red-500" />,
        };
      case 'high':
        return {
          border: 'border-l-4 border-l-orange-500',
          bg: 'bg-orange-50',
          icon: <AlertCircle className="h-4 w-4 text-orange-500" />,
        };
      case 'medium':
        return {
          border: 'border-l-4 border-l-blue-500',
          bg: 'bg-blue-50',
          icon: <Info className="h-4 w-4 text-blue-500" />,
        };
      case 'low':
        return {
          border: 'border-l-4 border-l-gray-500',
          bg: 'bg-gray-50',
          icon: <Info className="h-4 w-4 text-gray-500" />,
        };
      default:
        return {
          border: 'border-l-4 border-l-blue-500',
          bg: 'bg-blue-50',
          icon: <Info className="h-4 w-4 text-blue-500" />,
        };
    }
  };

  const getPositionStyles = (position: string) => {
    switch (position) {
      case 'top-right':
        return 'top-4 right-4';
      case 'top-left':
        return 'top-4 left-4';
      case 'bottom-right':
        return 'bottom-4 right-4';
      case 'bottom-left':
        return 'bottom-4 left-4';
      default:
        return 'top-4 right-4';
    }
  };

  const handleClose = async () => {
    setIsClosing(true);
    
    // Mark as read if not already read
    if (!notification.isRead) {
      try {
        await markAsRead(notification._id);
      } catch (error) {
        console.error('Failed to mark notification as read:', error);
      }
    }
    
    setTimeout(() => {
      onClose();
    }, 300); // Animation duration
  };

  const handleAction = () => {
    if (onAction) {
      onAction(notification);
    }
    handleClose();
  };

  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));

    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    return `${Math.floor(diffInMinutes / 60)}h ago`;
  };

  useEffect(() => {
    // Trigger entrance animation
    setIsVisible(true);

    // Auto-close timer
    if (autoClose) {
      const timer = setTimeout(() => {
        handleClose();
      }, autoCloseDelay);

      return () => clearTimeout(timer);
    }
  }, [autoClose, autoCloseDelay]);

  const priorityStyles = getPriorityStyles(notification.priority);
  const positionStyles = getPositionStyles(position);

  return (
    <div
      className={`fixed z-50 ${positionStyles} transition-all duration-300 ease-in-out ${
        isVisible && !isClosing
          ? 'translate-x-0 opacity-100'
          : position.includes('right')
          ? 'translate-x-full opacity-0'
          : '-translate-x-full opacity-0'
      }`}
    >
      <Card className={`w-80 shadow-lg ${priorityStyles.border} ${priorityStyles.bg}`}>
        <CardContent className="p-4">
          <div className="flex items-start gap-3">
            {/* Category Icon */}
            <div className="flex-shrink-0 mt-0.5">
              {getCategoryIcon(notification.category)}
            </div>

            {/* Content */}
            <div className="flex-1 min-w-0">
              <div className="flex items-start justify-between mb-1">
                <h4 className="font-medium text-sm text-gray-900 line-clamp-1">
                  {notification.title}
                </h4>
                
                <div className="flex items-center gap-1 ml-2">
                  {/* Priority Indicator */}
                  {priorityStyles.icon}
                  
                  {/* Close Button */}
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-6 w-6 p-0 hover:bg-gray-200"
                    onClick={handleClose}
                  >
                    <X className="h-3 w-3" />
                  </Button>
                </div>
              </div>

              <p className="text-sm text-gray-700 mb-2 line-clamp-2">
                {notification.message}
              </p>

              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Badge variant="outline" className="text-xs capitalize">
                    {notification.category}
                  </Badge>
                  <span className="text-xs text-gray-500">
                    {formatTimeAgo(notification.createdAt)}
                  </span>
                </div>

                {/* Action Button */}
                {notification.relatedEntity && onAction && (
                  <Button
                    variant="ghost"
                    size="sm"
                    className="text-xs h-6 px-2"
                    onClick={handleAction}
                  >
                    View
                  </Button>
                )}
              </div>
            </div>
          </div>

          {/* Progress Bar for Auto-close */}
          {autoClose && (
            <div className="mt-3 h-1 bg-gray-200 rounded-full overflow-hidden">
              <div
                className="h-full bg-blue-500 rounded-full transition-all ease-linear"
                style={{
                  animation: `shrink ${autoCloseDelay}ms linear`,
                }}
              />
            </div>
          )}
        </CardContent>
      </Card>

      <style jsx>{`
        @keyframes shrink {
          from {
            width: 100%;
          }
          to {
            width: 0%;
          }
        }
      `}</style>
    </div>
  );
};

interface NotificationToastContainerProps {
  maxToasts?: number;
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left';
  autoClose?: boolean;
  autoCloseDelay?: number;
  onNotificationAction?: (notification: BaseNotification) => void;
}

export const NotificationToastContainer: React.FC<NotificationToastContainerProps> = ({
  maxToasts = 3,
  position = 'top-right',
  autoClose = true,
  autoCloseDelay = 5000,
  onNotificationAction,
}) => {
  const { notifications } = useNotifications();
  const [activeToasts, setActiveToasts] = useState<BaseNotification[]>([]);

  useEffect(() => {
    // Show toasts for new unread notifications
    const newNotifications = notifications
      .filter(n => !n.isRead && n.status === 'delivered')
      .slice(0, maxToasts);

    // Only show notifications that aren't already being displayed
    const newToasts = newNotifications.filter(
      newNotif => !activeToasts.some(activeNotif => activeNotif._id === newNotif._id)
    );

    if (newToasts.length > 0) {
      setActiveToasts(prev => [...newToasts, ...prev].slice(0, maxToasts));
    }
  }, [notifications, maxToasts, activeToasts]);

  const handleCloseToast = (notificationId: string) => {
    setActiveToasts(prev => prev.filter(n => n._id !== notificationId));
  };

  const handleToastAction = (notification: BaseNotification) => {
    if (onNotificationAction) {
      onNotificationAction(notification);
    }
    handleCloseToast(notification._id);
  };

  return (
    <>
      {activeToasts.map((notification, index) => (
        <div
          key={notification._id}
          style={{
            zIndex: 1000 - index,
            transform: position.includes('top')
              ? `translateY(${index * 90}px)`
              : `translateY(${-index * 90}px)`,
          }}
        >
          <NotificationToast
            notification={notification}
            onClose={() => handleCloseToast(notification._id)}
            onAction={handleToastAction}
            autoClose={autoClose}
            autoCloseDelay={autoCloseDelay}
            position={position}
          />
        </div>
      ))}
    </>
  );
};

export default NotificationToast;
