import React, { createContext, useContext, useState, useEffect, useCallback, ReactNode } from 'react';
import { notificationApi, type BaseNotification } from './index';

interface NotificationContextType {
  notifications: BaseNotification[];
  unreadCount: number;
  loading: boolean;
  error: string | null;
  
  // Actions
  fetchNotifications: () => Promise<void>;
  markAsRead: (notificationId: string) => Promise<void>;
  markAllAsRead: () => Promise<void>;
  deleteNotification: (notificationId: string) => Promise<void>;
  addNotification: (notification: BaseNotification) => void;
  clearError: () => void;
  
  // Real-time updates
  isConnected: boolean;
  lastUpdated: Date | null;
}

const NotificationContext = createContext<NotificationContextType | undefined>(undefined);

interface NotificationProviderProps {
  children: ReactNode;
  autoRefresh?: boolean;
  refreshInterval?: number;
  maxNotifications?: number;
}

const NotificationProvider: React.FC<NotificationProviderProps> = ({
  children,
  autoRefresh = true,
  refreshInterval = 30000, // 30 seconds
  maxNotifications = 50,
}) => {
  const [notifications, setNotifications] = useState<BaseNotification[]>([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isConnected, setIsConnected] = useState(true);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);

  const fetchNotifications = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await notificationApi.getNotifications({
        limit: maxNotifications,
        page: 1,
      });

      if (response.success) {
        setNotifications(response.data.notifications || []);
        setUnreadCount(response.data.unreadCount || 0);
        setLastUpdated(new Date());
        setIsConnected(true);
      } else {
        throw new Error(response.error || 'Failed to fetch notifications');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch notifications';
      setError(errorMessage);
      setIsConnected(false);
      console.error('Failed to fetch notifications:', err);
    } finally {
      setLoading(false);
    }
  }, [maxNotifications]);

  const markAsRead = useCallback(async (notificationId: string) => {
    try {
      await notificationApi.markAsRead(notificationId);
      
      setNotifications(prev => 
        prev.map(notification => 
          notification._id === notificationId 
            ? { ...notification, isRead: true, status: 'read' as const }
            : notification
        )
      );
      
      setUnreadCount(prev => Math.max(0, prev - 1));
      setLastUpdated(new Date());
    } catch (err) {
      console.error('Failed to mark notification as read:', err);
      setError('Failed to mark notification as read');
    }
  }, []);

  const markAllAsRead = useCallback(async () => {
    try {
      const unreadNotifications = notifications.filter(n => !n.isRead);
      
      await Promise.all(
        unreadNotifications.map(notification => 
          notificationApi.markAsRead(notification._id)
        )
      );

      setNotifications(prev => 
        prev.map(notification => ({ 
          ...notification, 
          isRead: true, 
          status: 'read' as const 
        }))
      );
      
      setUnreadCount(0);
      setLastUpdated(new Date());
    } catch (err) {
      console.error('Failed to mark all notifications as read:', err);
      setError('Failed to mark all notifications as read');
    }
  }, [notifications]);

  const deleteNotification = useCallback(async (notificationId: string) => {
    try {
      await notificationApi.deleteNotification(notificationId);
      
      const deletedNotification = notifications.find(n => n._id === notificationId);
      
      setNotifications(prev => prev.filter(n => n._id !== notificationId));
      
      if (deletedNotification && !deletedNotification.isRead) {
        setUnreadCount(prev => Math.max(0, prev - 1));
      }
      
      setLastUpdated(new Date());
    } catch (err) {
      console.error('Failed to delete notification:', err);
      setError('Failed to delete notification');
    }
  }, [notifications]);

  const addNotification = useCallback((notification: BaseNotification) => {
    setNotifications(prev => {
      // Check if notification already exists
      const exists = prev.some(n => n._id === notification._id);
      if (exists) return prev;
      
      // Add new notification to the beginning
      const updated = [notification, ...prev];
      
      // Keep only the max number of notifications
      return updated.slice(0, maxNotifications);
    });
    
    if (!notification.isRead) {
      setUnreadCount(prev => prev + 1);
    }
    
    setLastUpdated(new Date());
  }, [maxNotifications]);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  // Auto-refresh notifications
  useEffect(() => {
    // Initial fetch
    fetchNotifications();
    
    if (autoRefresh) {
      const interval = setInterval(fetchNotifications, refreshInterval);
      return () => clearInterval(interval);
    }
  }, [fetchNotifications, autoRefresh, refreshInterval]);

  // Set up real-time notifications (WebSocket or Server-Sent Events)
  useEffect(() => {
    // Check if browser supports Server-Sent Events
    if (typeof EventSource !== 'undefined') {
      const token = localStorage.getItem('token');
      if (!token) return;

      const eventSource = new EventSource(`/api/notifications/stream?token=${token}`);
      
      eventSource.onopen = () => {
        setIsConnected(true);
        setError(null);
      };

      eventSource.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          
          if (data.type === 'notification') {
            addNotification(data.notification);
          } else if (data.type === 'notification_update') {
            // Handle notification status updates
            setNotifications(prev => 
              prev.map(n => 
                n._id === data.notificationId 
                  ? { ...n, ...data.updates }
                  : n
              )
            );
          }
        } catch (err) {
          console.error('Failed to parse SSE message:', err);
        }
      };

      eventSource.onerror = () => {
        setIsConnected(false);
        setError('Real-time connection lost');
      };

      return () => {
        eventSource.close();
      };
    }
  }, [addNotification]);

  // Browser notification permission and display
  useEffect(() => {
    // Request notification permission if not already granted
    if ('Notification' in window && Notification.permission === 'default') {
      Notification.requestPermission();
    }
  }, []);

  // Show browser notifications for high priority notifications
  useEffect(() => {
    if ('Notification' in window && Notification.permission === 'granted') {
      notifications.forEach(notification => {
        if (
          !notification.isRead && 
          (notification.priority === 'urgent' || notification.priority === 'high') &&
          notification.status === 'delivered'
        ) {
          // Check if we've already shown this notification
          const notificationKey = `pedi_notification_${notification._id}`;
          if (!sessionStorage.getItem(notificationKey)) {
            const browserNotification = new Notification(notification.title, {
              body: notification.message,
              icon: '/favicon.ico',
              tag: notification._id,
              requireInteraction: notification.priority === 'urgent',
            });

            browserNotification.onclick = () => {
              window.focus();
              markAsRead(notification._id);
              browserNotification.close();
            };

            // Mark as shown
            sessionStorage.setItem(notificationKey, 'shown');
          }
        }
      });
    }
  }, [notifications, markAsRead]);

  const contextValue: NotificationContextType = {
    notifications,
    unreadCount,
    loading,
    error,
    fetchNotifications,
    markAsRead,
    markAllAsRead,
    deleteNotification,
    addNotification,
    clearError,
    isConnected,
    lastUpdated,
  };

  return (
    <NotificationContext.Provider value={contextValue}>
      {children}
    </NotificationContext.Provider>
  );
};

export const useNotifications = (): NotificationContextType => {
  const context = useContext(NotificationContext);
  if (context === undefined) {
    throw new Error('useNotifications must be used within a NotificationProvider');
  }
  return context;
};

export default NotificationProvider;
