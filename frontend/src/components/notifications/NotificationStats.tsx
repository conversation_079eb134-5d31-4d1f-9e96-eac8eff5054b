import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  BarChart3, 
  TrendingUp, 
  TrendingDown, 
  Activity, 
  CheckCircle, 
  XCircle, 
  Clock, 
  Bell,
  Smartphone,
  Mail,
  MessageSquare,
  RefreshCw,
  Calendar,
  Target,
  Zap,
  AlertCircle,
  Loader2
} from 'lucide-react';

interface NotificationStats {
  totalSent: number;
  totalDelivered: number;
  totalFailed: number;
  totalPending: number;
  deliveryRate: number;
  failureRate: number;
  averageDeliveryTime: number;
  byCategory: {
    [key: string]: {
      sent: number;
      delivered: number;
      failed: number;
      deliveryRate: number;
    };
  };
  byChannel: {
    [key: string]: {
      sent: number;
      delivered: number;
      failed: number;
      deliveryRate: number;
    };
  };
  byPriority: {
    [key: string]: {
      sent: number;
      delivered: number;
      failed: number;
      deliveryRate: number;
    };
  };
  recentTrends: {
    period: string;
    sent: number;
    delivered: number;
    failed: number;
  }[];
  topFailureReasons: {
    reason: string;
    count: number;
    percentage: number;
  }[];
}

interface ServiceHealth {
  status: 'healthy' | 'degraded' | 'down';
  smsService: {
    status: 'connected' | 'disconnected' | 'error';
    lastCheck: string;
    responseTime: number;
  };
  database: {
    status: 'connected' | 'disconnected' | 'error';
    lastCheck: string;
    responseTime: number;
  };
  pendingNotifications: number;
  failedNotifications: number;
  uptime: number;
}

const NotificationStats: React.FC = () => {
  const [stats, setStats] = useState<NotificationStats | null>(null);
  const [health, setHealth] = useState<ServiceHealth | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [timeRange, setTimeRange] = useState('7d');

  const fetchStats = async () => {
    try {
      setLoading(true);
      setError(null);

      const token = localStorage.getItem('token');
      const [statsResponse, healthResponse] = await Promise.all([
        fetch(`/api/notifications/stats?timeRange=${timeRange}`, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        }),
        fetch('/api/notifications/health', {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        })
      ]);

      if (!statsResponse.ok || !healthResponse.ok) {
        throw new Error('Failed to fetch notification data');
      }

      const [statsData, healthData] = await Promise.all([
        statsResponse.json(),
        healthResponse.json()
      ]);

      setStats(statsData.stats);
      setHealth(healthData.health);

    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchStats();
  }, [timeRange]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy':
      case 'connected': return 'text-green-600 bg-green-100';
      case 'degraded': return 'text-yellow-600 bg-yellow-100';
      case 'down':
      case 'disconnected':
      case 'error': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const formatPercentage = (value: number) => {
    return `${(value * 100).toFixed(1)}%`;
  };

  const formatNumber = (value: number) => {
    if (value >= 1000000) {
      return `${(value / 1000000).toFixed(1)}M`;
    } else if (value >= 1000) {
      return `${(value / 1000).toFixed(1)}K`;
    }
    return value.toString();
  };

  const formatDuration = (milliseconds: number) => {
    if (milliseconds < 1000) {
      return `${milliseconds}ms`;
    } else if (milliseconds < 60000) {
      return `${(milliseconds / 1000).toFixed(1)}s`;
    } else {
      return `${(milliseconds / 60000).toFixed(1)}m`;
    }
  };

  if (loading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <Loader2 className="h-8 w-8 animate-spin" />
          <span className="ml-2">Loading statistics...</span>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardContent className="py-8">
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5" />
                Notification Analytics
              </CardTitle>
              <CardDescription>
                Monitor notification performance and system health
              </CardDescription>
            </div>
            
            <div className="flex items-center gap-4">
              <Select value={timeRange} onValueChange={setTimeRange}>
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="1d">Last 24h</SelectItem>
                  <SelectItem value="7d">Last 7 days</SelectItem>
                  <SelectItem value="30d">Last 30 days</SelectItem>
                  <SelectItem value="90d">Last 90 days</SelectItem>
                </SelectContent>
              </Select>
              
              <Button variant="outline" size="sm" onClick={fetchStats}>
                <RefreshCw className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Service Health */}
      {health && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Activity className="h-5 w-5" />
              Service Health
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div className="text-center">
                <Badge className={getStatusColor(health.status)}>
                  {health.status.toUpperCase()}
                </Badge>
                <p className="text-sm text-gray-600 mt-1">Overall Status</p>
              </div>
              
              <div className="text-center">
                <Badge className={getStatusColor(health.smsService.status)}>
                  <Smartphone className="h-3 w-3 mr-1" />
                  {health.smsService.status}
                </Badge>
                <p className="text-sm text-gray-600 mt-1">
                  SMS Service ({formatDuration(health.smsService.responseTime)})
                </p>
              </div>
              
              <div className="text-center">
                <Badge className={getStatusColor(health.database.status)}>
                  {health.database.status}
                </Badge>
                <p className="text-sm text-gray-600 mt-1">
                  Database ({formatDuration(health.database.responseTime)})
                </p>
              </div>
              
              <div className="text-center">
                <div className="text-lg font-semibold">
                  {formatDuration(health.uptime)}
                </div>
                <p className="text-sm text-gray-600">Uptime</p>
              </div>
            </div>
            
            {(health.pendingNotifications > 0 || health.failedNotifications > 0) && (
              <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                <div className="flex items-center gap-4 text-sm">
                  {health.pendingNotifications > 0 && (
                    <span className="flex items-center gap-1">
                      <Clock className="h-4 w-4 text-yellow-600" />
                      {health.pendingNotifications} pending
                    </span>
                  )}
                  {health.failedNotifications > 0 && (
                    <span className="flex items-center gap-1">
                      <XCircle className="h-4 w-4 text-red-600" />
                      {health.failedNotifications} failed
                    </span>
                  )}
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Overview Stats */}
      {stats && (
        <>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Total Sent</p>
                    <p className="text-2xl font-bold">{formatNumber(stats.totalSent)}</p>
                  </div>
                  <Bell className="h-8 w-8 text-blue-500" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Delivered</p>
                    <p className="text-2xl font-bold text-green-600">{formatNumber(stats.totalDelivered)}</p>
                    <p className="text-xs text-gray-500">{formatPercentage(stats.deliveryRate)}</p>
                  </div>
                  <CheckCircle className="h-8 w-8 text-green-500" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Failed</p>
                    <p className="text-2xl font-bold text-red-600">{formatNumber(stats.totalFailed)}</p>
                    <p className="text-xs text-gray-500">{formatPercentage(stats.failureRate)}</p>
                  </div>
                  <XCircle className="h-8 w-8 text-red-500" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Avg. Delivery</p>
                    <p className="text-2xl font-bold">{formatDuration(stats.averageDeliveryTime)}</p>
                  </div>
                  <Zap className="h-8 w-8 text-yellow-500" />
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Category Performance */}
          <Card>
            <CardHeader>
              <CardTitle>Performance by Category</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {Object.entries(stats.byCategory).map(([category, data]) => (
                  <div key={category} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex items-center gap-3">
                      <span className="font-medium capitalize">{category}</span>
                      <Badge variant="outline">{formatNumber(data.sent)} sent</Badge>
                    </div>
                    
                    <div className="flex items-center gap-4">
                      <div className="text-right">
                        <div className="text-sm font-medium text-green-600">
                          {formatNumber(data.delivered)} delivered
                        </div>
                        <div className="text-xs text-gray-500">
                          {formatPercentage(data.deliveryRate)}
                        </div>
                      </div>
                      
                      {data.failed > 0 && (
                        <div className="text-right">
                          <div className="text-sm font-medium text-red-600">
                            {formatNumber(data.failed)} failed
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Channel Performance */}
          <Card>
            <CardHeader>
              <CardTitle>Performance by Channel</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {Object.entries(stats.byChannel).map(([channel, data]) => (
                  <div key={channel} className="p-4 border rounded-lg">
                    <div className="flex items-center gap-2 mb-3">
                      {channel === 'sms' && <Smartphone className="h-4 w-4" />}
                      {channel === 'email' && <Mail className="h-4 w-4" />}
                      {channel === 'push' && <Bell className="h-4 w-4" />}
                      {channel === 'in-app' && <MessageSquare className="h-4 w-4" />}
                      <span className="font-medium capitalize">{channel}</span>
                    </div>
                    
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-600">Sent:</span>
                        <span className="font-medium">{formatNumber(data.sent)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-600">Delivered:</span>
                        <span className="font-medium text-green-600">{formatNumber(data.delivered)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-600">Success Rate:</span>
                        <span className="font-medium">{formatPercentage(data.deliveryRate)}</span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Failure Analysis */}
          {stats.topFailureReasons.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Target className="h-5 w-5" />
                  Top Failure Reasons
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {stats.topFailureReasons.map((reason, index) => (
                    <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                      <span className="font-medium">{reason.reason}</span>
                      <div className="flex items-center gap-2">
                        <Badge variant="destructive">{reason.count}</Badge>
                        <span className="text-sm text-gray-500">
                          {formatPercentage(reason.percentage / 100)}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </>
      )}
    </div>
  );
};

export default NotificationStats;
