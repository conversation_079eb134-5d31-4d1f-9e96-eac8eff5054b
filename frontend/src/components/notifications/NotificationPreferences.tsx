import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Settings, 
  Bell, 
  BellOff, 
  Smartphone, 
  Mail, 
  MessageSquare,
  Save,
  RefreshCw,
  Clock,
  Volume2,
  VolumeX,
  CheckCircle,
  AlertCircle,
  Loader2
} from 'lucide-react';

interface NotificationPreferences {
  channels: {
    sms: boolean;
    push: boolean;
    email: boolean;
    inApp: boolean;
  };
  categories: {
    payment: boolean;
    exchange: boolean;
    token: boolean;
    system: boolean;
    promotion: boolean;
    reminder: boolean;
    welcome: boolean;
    security: boolean;
  };
  quietHours: {
    enabled: boolean;
    startTime: string;
    endTime: string;
    timezone: string;
  };
  frequency: {
    maxPerDay: number;
    maxPerWeek: number;
    cooldownMinutes: number;
  };
  language: string;
}

const defaultPreferences: NotificationPreferences = {
  channels: {
    sms: true,
    push: true,
    email: false,
    inApp: true,
  },
  categories: {
    payment: true,
    exchange: true,
    token: true,
    system: true,
    promotion: false,
    reminder: true,
    welcome: true,
    security: true,
  },
  quietHours: {
    enabled: true,
    startTime: '22:00',
    endTime: '08:00',
    timezone: 'Africa/Nairobi',
  },
  frequency: {
    maxPerDay: 20,
    maxPerWeek: 100,
    cooldownMinutes: 5,
  },
  language: 'en',
};

const NotificationPreferences: React.FC = () => {
  const [preferences, setPreferences] = useState<NotificationPreferences>(defaultPreferences);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  const fetchPreferences = async () => {
    try {
      setLoading(true);
      setError(null);

      const token = localStorage.getItem('token');
      const response = await fetch('/api/notifications/preferences', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Failed to fetch preferences');
      }

      const data = await response.json();
      setPreferences(data.preferences || defaultPreferences);

    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
      setPreferences(defaultPreferences);
    } finally {
      setLoading(false);
    }
  };

  const savePreferences = async () => {
    try {
      setSaving(true);
      setError(null);
      setSuccess(null);

      const token = localStorage.getItem('token');
      const response = await fetch('/api/notifications/preferences', {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(preferences),
      });

      if (!response.ok) {
        throw new Error('Failed to save preferences');
      }

      setSuccess('Preferences saved successfully!');
      setTimeout(() => setSuccess(null), 3000);

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to save preferences');
    } finally {
      setSaving(false);
    }
  };

  const resetToDefaults = async () => {
    try {
      setSaving(true);
      setError(null);
      setSuccess(null);

      const token = localStorage.getItem('token');
      const response = await fetch('/api/notifications/preferences/reset', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Failed to reset preferences');
      }

      const data = await response.json();
      setPreferences(data.preferences || defaultPreferences);
      setSuccess('Preferences reset to defaults!');
      setTimeout(() => setSuccess(null), 3000);

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to reset preferences');
    } finally {
      setSaving(false);
    }
  };

  const updateChannelPreference = (channel: keyof NotificationPreferences['channels'], enabled: boolean) => {
    setPreferences(prev => ({
      ...prev,
      channels: {
        ...prev.channels,
        [channel]: enabled,
      },
    }));
  };

  const updateCategoryPreference = (category: keyof NotificationPreferences['categories'], enabled: boolean) => {
    setPreferences(prev => ({
      ...prev,
      categories: {
        ...prev.categories,
        [category]: enabled,
      },
    }));
  };

  const updateQuietHours = (field: keyof NotificationPreferences['quietHours'], value: string | boolean) => {
    setPreferences(prev => ({
      ...prev,
      quietHours: {
        ...prev.quietHours,
        [field]: value,
      },
    }));
  };

  const updateFrequency = (field: keyof NotificationPreferences['frequency'], value: number) => {
    setPreferences(prev => ({
      ...prev,
      frequency: {
        ...prev.frequency,
        [field]: value,
      },
    }));
  };

  useEffect(() => {
    fetchPreferences();
  }, []);

  if (loading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <Loader2 className="h-8 w-8 animate-spin" />
          <span className="ml-2">Loading preferences...</span>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Notification Preferences
          </CardTitle>
          <CardDescription>
            Customize how and when you receive notifications from Pedi
          </CardDescription>
        </CardHeader>

        <CardContent className="space-y-6">
          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {success && (
            <Alert>
              <CheckCircle className="h-4 w-4" />
              <AlertDescription>{success}</AlertDescription>
            </Alert>
          )}

          {/* Notification Channels */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold flex items-center gap-2">
              <Bell className="h-5 w-5" />
              Notification Channels
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="flex items-center justify-between p-3 border rounded-lg">
                <div className="flex items-center gap-2">
                  <Smartphone className="h-4 w-4" />
                  <div>
                    <Label className="font-medium">SMS</Label>
                    <p className="text-sm text-gray-500">Text messages to your phone</p>
                  </div>
                </div>
                <Button
                  variant={preferences.channels.sms ? "default" : "outline"}
                  size="sm"
                  onClick={() => updateChannelPreference('sms', !preferences.channels.sms)}
                >
                  {preferences.channels.sms ? <Volume2 className="h-4 w-4" /> : <VolumeX className="h-4 w-4" />}
                </Button>
              </div>

              <div className="flex items-center justify-between p-3 border rounded-lg">
                <div className="flex items-center gap-2">
                  <Bell className="h-4 w-4" />
                  <div>
                    <Label className="font-medium">Push Notifications</Label>
                    <p className="text-sm text-gray-500">Browser notifications</p>
                  </div>
                </div>
                <Button
                  variant={preferences.channels.push ? "default" : "outline"}
                  size="sm"
                  onClick={() => updateChannelPreference('push', !preferences.channels.push)}
                >
                  {preferences.channels.push ? <Volume2 className="h-4 w-4" /> : <VolumeX className="h-4 w-4" />}
                </Button>
              </div>

              <div className="flex items-center justify-between p-3 border rounded-lg">
                <div className="flex items-center gap-2">
                  <Mail className="h-4 w-4" />
                  <div>
                    <Label className="font-medium">Email</Label>
                    <p className="text-sm text-gray-500">Email notifications</p>
                  </div>
                </div>
                <Button
                  variant={preferences.channels.email ? "default" : "outline"}
                  size="sm"
                  onClick={() => updateChannelPreference('email', !preferences.channels.email)}
                >
                  {preferences.channels.email ? <Volume2 className="h-4 w-4" /> : <VolumeX className="h-4 w-4" />}
                </Button>
              </div>

              <div className="flex items-center justify-between p-3 border rounded-lg">
                <div className="flex items-center gap-2">
                  <MessageSquare className="h-4 w-4" />
                  <div>
                    <Label className="font-medium">In-App</Label>
                    <p className="text-sm text-gray-500">Notifications within the app</p>
                  </div>
                </div>
                <Button
                  variant={preferences.channels.inApp ? "default" : "outline"}
                  size="sm"
                  onClick={() => updateChannelPreference('inApp', !preferences.channels.inApp)}
                >
                  {preferences.channels.inApp ? <Volume2 className="h-4 w-4" /> : <VolumeX className="h-4 w-4" />}
                </Button>
              </div>
            </div>
          </div>

          {/* Notification Categories */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Notification Categories</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              {Object.entries(preferences.categories).map(([category, enabled]) => (
                <div key={category} className="flex items-center justify-between p-2 border rounded">
                  <Label className="capitalize font-medium">{category}</Label>
                  <Button
                    variant={enabled ? "default" : "outline"}
                    size="sm"
                    onClick={() => updateCategoryPreference(category as keyof NotificationPreferences['categories'], !enabled)}
                  >
                    {enabled ? <Bell className="h-4 w-4" /> : <BellOff className="h-4 w-4" />}
                  </Button>
                </div>
              ))}
            </div>
          </div>

          {/* Quiet Hours */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold flex items-center gap-2">
              <Clock className="h-5 w-5" />
              Quiet Hours
            </h3>
            
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <Label>Enable Quiet Hours</Label>
                <Button
                  variant={preferences.quietHours.enabled ? "default" : "outline"}
                  size="sm"
                  onClick={() => updateQuietHours('enabled', !preferences.quietHours.enabled)}
                >
                  {preferences.quietHours.enabled ? <Volume2 className="h-4 w-4" /> : <VolumeX className="h-4 w-4" />}
                </Button>
              </div>

              {preferences.quietHours.enabled && (
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label>Start Time</Label>
                    <Select 
                      value={preferences.quietHours.startTime} 
                      onValueChange={(value) => updateQuietHours('startTime', value)}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {Array.from({ length: 24 }, (_, i) => {
                          const hour = i.toString().padStart(2, '0');
                          return (
                            <SelectItem key={hour} value={`${hour}:00`}>
                              {hour}:00
                            </SelectItem>
                          );
                        })}
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label>End Time</Label>
                    <Select 
                      value={preferences.quietHours.endTime} 
                      onValueChange={(value) => updateQuietHours('endTime', value)}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {Array.from({ length: 24 }, (_, i) => {
                          const hour = i.toString().padStart(2, '0');
                          return (
                            <SelectItem key={hour} value={`${hour}:00`}>
                              {hour}:00
                            </SelectItem>
                          );
                        })}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Frequency Limits */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Frequency Limits</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <Label>Max per Day</Label>
                <Select 
                  value={preferences.frequency.maxPerDay.toString()} 
                  onValueChange={(value) => updateFrequency('maxPerDay', parseInt(value))}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="5">5</SelectItem>
                    <SelectItem value="10">10</SelectItem>
                    <SelectItem value="20">20</SelectItem>
                    <SelectItem value="50">50</SelectItem>
                    <SelectItem value="100">Unlimited</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label>Max per Week</Label>
                <Select 
                  value={preferences.frequency.maxPerWeek.toString()} 
                  onValueChange={(value) => updateFrequency('maxPerWeek', parseInt(value))}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="20">20</SelectItem>
                    <SelectItem value="50">50</SelectItem>
                    <SelectItem value="100">100</SelectItem>
                    <SelectItem value="200">200</SelectItem>
                    <SelectItem value="500">Unlimited</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label>Cooldown (minutes)</Label>
                <Select 
                  value={preferences.frequency.cooldownMinutes.toString()} 
                  onValueChange={(value) => updateFrequency('cooldownMinutes', parseInt(value))}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="0">None</SelectItem>
                    <SelectItem value="1">1 minute</SelectItem>
                    <SelectItem value="5">5 minutes</SelectItem>
                    <SelectItem value="15">15 minutes</SelectItem>
                    <SelectItem value="60">1 hour</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-4 pt-4 border-t">
            <Button onClick={savePreferences} disabled={saving}>
              {saving ? (
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
              ) : (
                <Save className="h-4 w-4 mr-2" />
              )}
              Save Preferences
            </Button>

            <Button variant="outline" onClick={resetToDefaults} disabled={saving}>
              <RefreshCw className="h-4 w-4 mr-2" />
              Reset to Defaults
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default NotificationPreferences;
