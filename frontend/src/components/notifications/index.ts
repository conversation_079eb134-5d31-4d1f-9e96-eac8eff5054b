// Notification Components
export { default as NotificationList } from './NotificationList';
export { default as NotificationPreferences } from './NotificationPreferences';
export { default as NotificationBell } from './NotificationBell';
export { default as NotificationStats } from './NotificationStats';
export { default as NotificationProvider, useNotifications } from './NotificationProvider';
export { default as NotificationToast, NotificationToastContainer } from './NotificationToast';

// Re-export types for convenience
export type {
  Notification,
  NotificationListResponse,
  NotificationListProps
} from './NotificationList';

export type {
  NotificationPreferences as NotificationPreferencesType,
} from './NotificationPreferences';

export type {
  NotificationBellProps
} from './NotificationBell';

// Common notification types
export interface BaseNotification {
  _id: string;
  category: 'payment' | 'exchange' | 'token' | 'system' | 'promotion' | 'reminder' | 'welcome' | 'security';
  type: 'sms' | 'push' | 'email' | 'in-app';
  title: string;
  message: string;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  status: 'pending' | 'sent' | 'delivered' | 'failed' | 'read';
  isRead: boolean;
  scheduledFor?: string;
  sentAt?: string;
  deliveredAt?: string;
  relatedEntity?: {
    type: string;
    id: string;
  };
  createdAt: string;
  updatedAt: string;
}

export interface NotificationTemplate {
  _id: string;
  name: string;
  description: string;
  category: string;
  type: string;
  title: string;
  message: string;
  variables: {
    name: string;
    type: string;
    required: boolean;
    description: string;
    defaultValue?: any;
  }[];
  priority: string;
  isActive: boolean;
  usageCount: number;
  maxUsagePerDay?: number;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
}

export interface NotificationApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginatedResponse<T> {
  items: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}

// Notification API service functions
export const notificationApi = {
  // Get user notifications
  getNotifications: async (params?: {
    page?: number;
    limit?: number;
    category?: string;
    status?: string;
    priority?: string;
    unreadOnly?: boolean;
  }) => {
    const token = localStorage.getItem('token');
    const searchParams = new URLSearchParams();
    
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined) {
          searchParams.append(key, value.toString());
        }
      });
    }

    const response = await fetch(`/api/notifications?${searchParams}`, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error('Failed to fetch notifications');
    }

    return response.json();
  },

  // Mark notification as read
  markAsRead: async (notificationId: string) => {
    const token = localStorage.getItem('token');
    const response = await fetch(`/api/notifications/${notificationId}/read`, {
      method: 'PATCH',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error('Failed to mark notification as read');
    }

    return response.json();
  },

  // Delete notification
  deleteNotification: async (notificationId: string) => {
    const token = localStorage.getItem('token');
    const response = await fetch(`/api/notifications/${notificationId}`, {
      method: 'DELETE',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error('Failed to delete notification');
    }

    return response.json();
  },

  // Get notification preferences
  getPreferences: async () => {
    const token = localStorage.getItem('token');
    const response = await fetch('/api/notifications/preferences', {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error('Failed to fetch preferences');
    }

    return response.json();
  },

  // Update notification preferences
  updatePreferences: async (preferences: any) => {
    const token = localStorage.getItem('token');
    const response = await fetch('/api/notifications/preferences', {
      method: 'PUT',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(preferences),
    });

    if (!response.ok) {
      throw new Error('Failed to update preferences');
    }

    return response.json();
  },

  // Reset preferences to defaults
  resetPreferences: async () => {
    const token = localStorage.getItem('token');
    const response = await fetch('/api/notifications/preferences/reset', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error('Failed to reset preferences');
    }

    return response.json();
  },

  // Get notification statistics
  getStats: async (timeRange: string = '7d') => {
    const token = localStorage.getItem('token');
    const response = await fetch(`/api/notifications/stats?timeRange=${timeRange}`, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error('Failed to fetch statistics');
    }

    return response.json();
  },

  // Get service health
  getHealth: async () => {
    const token = localStorage.getItem('token');
    const response = await fetch('/api/notifications/health', {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error('Failed to fetch health status');
    }

    return response.json();
  },

  // Create notification (admin/system use)
  createNotification: async (notification: {
    userId?: string;
    userIds?: string[];
    category: string;
    title: string;
    message: string;
    priority?: string;
    scheduledFor?: string;
    templateId?: string;
    templateVariables?: any;
  }) => {
    const token = localStorage.getItem('token');
    const response = await fetch('/api/notifications', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(notification),
    });

    if (!response.ok) {
      throw new Error('Failed to create notification');
    }

    return response.json();
  },
};

// Utility functions
export const notificationUtils = {
  // Format time ago
  formatTimeAgo: (dateString: string): string => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));

    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`;
    return `${Math.floor(diffInMinutes / 1440)}d ago`;
  },

  // Get category icon name
  getCategoryIcon: (category: string): string => {
    const iconMap: { [key: string]: string } = {
      payment: 'CreditCard',
      exchange: 'MessageSquare',
      token: 'Gift',
      system: 'Settings',
      security: 'Shield',
      promotion: 'Megaphone',
      reminder: 'Calendar',
      welcome: 'UserPlus',
    };
    return iconMap[category] || 'Bell';
  },

  // Get priority color
  getPriorityColor: (priority: string): string => {
    const colorMap: { [key: string]: string } = {
      urgent: 'red',
      high: 'orange',
      medium: 'blue',
      low: 'gray',
    };
    return colorMap[priority] || 'gray';
  },

  // Get status color
  getStatusColor: (status: string): string => {
    const colorMap: { [key: string]: string } = {
      delivered: 'green',
      sent: 'blue',
      pending: 'yellow',
      failed: 'red',
      read: 'gray',
    };
    return colorMap[status] || 'gray';
  },

  // Format notification count
  formatCount: (count: number): string => {
    if (count > 99) return '99+';
    return count.toString();
  },
};
