'use client';

import { useState, useEffect, useCallback } from 'react';
import { useAuth } from '@/hooks/useAuth';
import { useSocket, useChatSocket } from '@/hooks/useSocket';
import { chatApi, ChatConversation, ChatMessage } from '@/services/chatApi';
import ChatList from '@/components/chat/ChatList';
import ChatWindow from '@/components/chat/ChatWindow';



export default function MessagesPage() {
  const { user } = useAuth();
  const [conversations, setConversations] = useState<ChatConversation[]>([]);
  const [selectedChatId, setSelectedChatId] = useState<string | null>(null);
  const [selectedConversation, setSelectedConversation] = useState<ChatConversation | null>(null);
  const [typingUsers, setTypingUsers] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [loadingMessages, setLoadingMessages] = useState(false);
  const [isMobile, setIsMobile] = useState(false);

  // Socket integration
  const { socket, isConnected } = useSocket({
    onConnect: () => {
      console.log('Connected to chat server');
      loadConversations();
    },
    onDisconnect: () => {
      console.log('Disconnected from chat server');
    },
    onError: (error) => {
      console.error('Socket connection error:', error);
    }
  });

  const { sendTypingStart, sendTypingStop } = useChatSocket(selectedChatId || undefined);

  // Load conversations from API
  const loadConversations = useCallback(async () => {
    try {
      setLoading(true);
      const response = await chatApi.getConversations();
      setConversations(response.conversations);
    } catch (error) {
      console.error('Failed to load conversations:', error);
    } finally {
      setLoading(false);
    }
  }, []);

  // Load messages for selected conversation
  const loadMessages = useCallback(async (conversationId: string) => {
    try {
      setLoadingMessages(true);
      const response = await chatApi.getMessages(conversationId);

      setSelectedConversation(prev => {
        if (prev && prev.id === conversationId) {
          return {
            ...prev,
            messages: response.messages
          };
        }
        return prev;
      });
    } catch (error) {
      console.error('Failed to load messages:', error);
    } finally {
      setLoadingMessages(false);
    }
  }, []);

  // Check if mobile view
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // Load conversation when selected
  useEffect(() => {
    if (selectedChatId) {
      const conversation = conversations.find(c => c.id === selectedChatId);
      if (conversation) {
        setSelectedConversation({
          ...conversation,
          messages: []
        });
        loadMessages(selectedChatId);
      }
    } else {
      setSelectedConversation(null);
    }
  }, [selectedChatId, conversations, loadMessages]);

  const handleChatSelect = (chatId: string) => {
    setSelectedChatId(chatId);
  };

  const handleNewChat = () => {
    // TODO: Implement new chat creation
    console.log('Create new chat');
  };

  const handleSendMessage = async (content: string, type: 'text' | 'image' = 'text') => {
    if (!selectedConversation || !user || !selectedChatId) return;

    const tempId = Date.now().toString();
    const newMessage: ChatMessage = {
      id: tempId,
      sender: {
        id: user.id,
        firstName: user.firstName,
        lastName: user.lastName,
        profilePicture: user.profilePicture
      },
      content,
      type,
      timestamp: new Date().toISOString(),
      isRead: false,
      deliveryStatus: 'sending'
    };

    // Optimistically add message to conversation
    setSelectedConversation(prev => {
      if (!prev) return prev;
      return {
        ...prev,
        messages: [...(prev.messages || []), newMessage]
      };
    });

    try {
      // Send message via API
      const sentMessage = await chatApi.sendMessage(selectedChatId, { content, type });

      // Update the temporary message with the real one
      setSelectedConversation(prev => {
        if (!prev) return prev;
        return {
          ...prev,
          messages: prev.messages?.map(msg =>
            msg.id === tempId ? sentMessage : msg
          ) || []
        };
      });

      // Update conversation in list
      setConversations(prev =>
        prev.map(conv =>
          conv.id === selectedChatId
            ? {
                ...conv,
                lastMessage: {
                  content,
                  sender: user.id,
                  timestamp: sentMessage.timestamp
                },
                updatedAt: sentMessage.timestamp
              }
            : conv
        )
      );
    } catch (error) {
      console.error('Failed to send message:', error);

      // Mark message as failed
      setSelectedConversation(prev => {
        if (!prev) return prev;
        return {
          ...prev,
          messages: prev.messages?.map(msg =>
            msg.id === tempId ? { ...msg, deliveryStatus: 'failed' as const } : msg
          ) || []
        };
      });
    }
  };

  const handleTypingStart = () => {
    sendTypingStart();
  };

  const handleTypingStop = () => {
    sendTypingStop();
  };

  const handleBack = () => {
    setSelectedChatId(null);
  };

  // Socket event listeners for real-time updates
  useEffect(() => {
    if (!isConnected || !user) return;

    // Handle new messages
    const handleNewMessage = (data: { chatId: string; message: ChatMessage }) => {
      const { chatId, message } = data;

      // Update selected conversation if it matches
      if (selectedChatId === chatId) {
        setSelectedConversation(prev => {
          if (!prev) return prev;
          return {
            ...prev,
            messages: [...(prev.messages || []), message]
          };
        });
      }

      // Update conversation list
      setConversations(prev =>
        prev.map(conv =>
          conv.id === chatId
            ? {
                ...conv,
                lastMessage: {
                  content: message.content,
                  sender: message.sender.id,
                  timestamp: message.timestamp
                },
                unreadCount: selectedChatId === chatId ? conv.unreadCount : conv.unreadCount + 1,
                updatedAt: message.timestamp
              }
            : conv
        )
      );
    };

    // Handle new conversations
    const handleNewConversation = (conversation: ChatConversation) => {
      setConversations(prev => [conversation, ...prev]);
    };

    // Handle typing indicators
    const handleUserTyping = (data: { chatId: string; userId: string; user: any }) => {
      if (data.chatId === selectedChatId && data.userId !== user.id) {
        setTypingUsers(prev => {
          const exists = prev.find(u => u.userId === data.userId);
          if (!exists) {
            return [...prev, data];
          }
          return prev;
        });
      }
    };

    const handleUserStoppedTyping = (data: { chatId: string; userId: string }) => {
      if (data.chatId === selectedChatId) {
        setTypingUsers(prev => prev.filter(u => u.userId !== data.userId));
      }
    };

    // Handle message read status
    const handleMessagesRead = (data: { readBy: string; messageIds: string[] | 'all' }) => {
      if (selectedConversation && data.readBy !== user.id) {
        setSelectedConversation(prev => {
          if (!prev) return prev;
          return {
            ...prev,
            messages: prev.messages?.map(msg => {
              if (data.messageIds === 'all' || data.messageIds.includes(msg.id)) {
                return { ...msg, isRead: true };
              }
              return msg;
            }) || []
          };
        });
      }
    };

    // Register event listeners
    socket.on('new-message', handleNewMessage);
    socket.on('user:new-conversation', handleNewConversation);
    socket.on('user-typing', handleUserTyping);
    socket.on('user-stopped-typing', handleUserStoppedTyping);
    socket.on('messages-read', handleMessagesRead);

    return () => {
      socket.off('new-message', handleNewMessage);
      socket.off('user:new-conversation', handleNewConversation);
      socket.off('user-typing', handleUserTyping);
      socket.off('user-stopped-typing', handleUserStoppedTyping);
      socket.off('messages-read', handleMessagesRead);
    };
  }, [isConnected, user, selectedChatId, selectedConversation, socket]);

  if (!user) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center">
          <h2 className="text-xl font-semibold mb-2">Please log in</h2>
          <p className="text-gray-600">You need to be logged in to access messages</p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full flex">
      {/* Chat List - Hidden on mobile when chat is selected */}
      <div className={`${
        isMobile && selectedChatId ? 'hidden' : 'block'
      } w-full md:w-1/3 lg:w-1/4 border-r`}>
        <ChatList
          conversations={conversations}
          selectedChatId={selectedChatId || undefined}
          onChatSelect={handleChatSelect}
          onNewChat={handleNewChat}
          currentUserId={user.id}
          loading={loading}
        />
      </div>

      {/* Chat Window - Full width on mobile when selected */}
      <div className={`${
        isMobile && !selectedChatId ? 'hidden' : 'block'
      } flex-1`}>
        <ChatWindow
          conversation={selectedConversation}
          currentUserId={user.id}
          onSendMessage={handleSendMessage}
          onTypingStart={handleTypingStart}
          onTypingStop={handleTypingStop}
          onBack={isMobile ? handleBack : undefined}
          typingUsers={typingUsers}
          loading={loading}
          loadingMessages={loadingMessages}
        />
      </div>
    </div>
  );
}
