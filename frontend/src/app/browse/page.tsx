'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import SearchFilters, { SearchFilters as SearchFiltersType } from '@/components/clothing/SearchFilters';
import ClothingGrid from '@/components/clothing/ClothingGrid';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Plus, AlertCircle } from 'lucide-react';

interface ClothingItem {
  _id: string;
  title: string;
  description: string;
  category: string;
  size: string;
  color: string;
  condition: string;
  exchangeType: string;
  tokenPrice?: number;
  originalPrice?: number;
  images: string[];
  views: number;
  likes: string[];
  likeCount: number;
  tags: string[];
  location: {
    county: string;
    town: string;
  };
  owner: {
    _id: string;
    firstName: string;
    lastName: string;
    profilePicture?: string;
    rating: {
      average: number;
      count: number;
    };
    sustainabilityScore: number;
  };
  createdAt: string;
  isAvailable: boolean;
}

interface PaginationInfo {
  page: number;
  limit: number;
  total: number;
  pages: number;
}

export default function BrowsePage() {
  const [items, setItems] = useState<ClothingItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [pagination, setPagination] = useState<PaginationInfo>({
    page: 1,
    limit: 20,
    total: 0,
    pages: 0,
  });
  const [currentUserId, setCurrentUserId] = useState<string | null>(null);
  const router = useRouter();

  useEffect(() => {
    // Get current user ID from token
    const token = localStorage.getItem('token');
    if (token) {
      try {
        const payload = JSON.parse(atob(token.split('.')[1]));
        setCurrentUserId(payload.id);
      } catch (error) {
        console.error('Error parsing token:', error);
      }
    }

    // Load initial items
    handleSearch({
      search: '',
      category: '',
      size: '',
      condition: '',
      exchangeType: '',
      county: '',
      minPrice: '',
      maxPrice: '',
      sortBy: 'createdAt',
      sortOrder: 'desc',
    });
  }, []);

  const handleSearch = async (filters: SearchFiltersType, page = 1) => {
    setLoading(true);
    setError('');

    try {
      const params = new URLSearchParams();
      
      // Add filters to params
      Object.entries(filters).forEach(([key, value]) => {
        if (value) {
          params.append(key, value);
        }
      });
      
      params.append('page', page.toString());
      params.append('limit', pagination.limit.toString());

      const response = await fetch(`/api/clothing?${params.toString()}`);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Failed to fetch items');
      }

      setItems(data.data.items);
      setPagination(data.data.pagination);
    } catch (err) {
      console.error('Error fetching items:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch items');
    } finally {
      setLoading(false);
    }
  };

  const handleLike = async (itemId: string) => {
    const token = localStorage.getItem('token');
    if (!token) {
      router.push('/auth/login');
      return;
    }

    try {
      const response = await fetch(`/api/clothing/${itemId}/like`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Failed to like item');
      }

      // Update the item in the list
      setItems(prevItems =>
        prevItems.map(item => {
          if (item._id === itemId) {
            const isCurrentlyLiked = currentUserId ? item.likes.includes(currentUserId) : false;
            return {
              ...item,
              likes: isCurrentlyLiked
                ? item.likes.filter(id => id !== currentUserId)
                : [...item.likes, currentUserId!],
              likeCount: isCurrentlyLiked ? item.likeCount - 1 : item.likeCount + 1,
            };
          }
          return item;
        })
      );
    } catch (error) {
      console.error('Error liking item:', error);
      setError('Failed to like item');
    }
  };

  const handlePageChange = (newPage: number) => {
    // Re-run search with new page
    const currentFilters: SearchFiltersType = {
      search: '',
      category: '',
      size: '',
      condition: '',
      exchangeType: '',
      county: '',
      minPrice: '',
      maxPrice: '',
      sortBy: 'createdAt',
      sortOrder: 'desc',
    };
    handleSearch(currentFilters, newPage);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Browse Clothing</h1>
              <p className="text-gray-600 mt-1">
                Discover sustainable fashion from the Pedi community
              </p>
            </div>
            <Button onClick={() => router.push('/list-item')} className="flex items-center gap-2">
              <Plus className="h-4 w-4" />
              List Item
            </Button>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Search and Filters */}
        <div className="mb-8">
          <SearchFilters onSearch={handleSearch} loading={loading} />
        </div>

        {/* Error Alert */}
        {error && (
          <div className="mb-6">
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          </div>
        )}

        {/* Results Summary */}
        {!loading && (
          <div className="mb-6 flex items-center justify-between">
            <p className="text-gray-600">
              {pagination.total > 0 ? (
                <>
                  Showing {((pagination.page - 1) * pagination.limit) + 1} to{' '}
                  {Math.min(pagination.page * pagination.limit, pagination.total)} of{' '}
                  {pagination.total} items
                </>
              ) : (
                'No items found'
              )}
            </p>
          </div>
        )}

        {/* Clothing Grid */}
        <ClothingGrid
          items={items}
          loading={loading}
          onLike={handleLike}
          currentUserId={currentUserId}
        />

        {/* Pagination */}
        {pagination.pages > 1 && (
          <div className="mt-8 flex justify-center">
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                onClick={() => handlePageChange(pagination.page - 1)}
                disabled={pagination.page <= 1}
              >
                Previous
              </Button>
              
              <div className="flex items-center gap-1">
                {Array.from({ length: Math.min(5, pagination.pages) }, (_, i) => {
                  const page = i + 1;
                  return (
                    <Button
                      key={page}
                      variant={page === pagination.page ? "default" : "outline"}
                      size="sm"
                      onClick={() => handlePageChange(page)}
                    >
                      {page}
                    </Button>
                  );
                })}
              </div>

              <Button
                variant="outline"
                onClick={() => handlePageChange(pagination.page + 1)}
                disabled={pagination.page >= pagination.pages}
              >
                Next
              </Button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
