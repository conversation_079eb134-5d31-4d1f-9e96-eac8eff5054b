'use client';

import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Bell, 
  Settings, 
  BarChart3, 
  MessageSquare,
  ArrowLeft,
  ExternalLink
} from 'lucide-react';
import {
  NotificationList,
  NotificationPreferences,
  NotificationStats,
  type BaseNotification
} from '@/components/notifications';

const NotificationsPage: React.FC = () => {
  const [selectedNotification, setSelectedNotification] = useState<BaseNotification | null>(null);
  const [activeTab, setActiveTab] = useState('notifications');

  const handleNotificationClick = (notification: BaseNotification) => {
    setSelectedNotification(notification);
    
    // Handle navigation based on notification type
    if (notification.relatedEntity) {
      switch (notification.relatedEntity.type) {
        case 'payment':
          // Navigate to payment details
          window.location.href = `/payments/${notification.relatedEntity.id}`;
          break;
        case 'exchange':
          // Navigate to exchange details
          window.location.href = `/exchanges/${notification.relatedEntity.id}`;
          break;
        case 'swap':
          // Navigate to swap details
          window.location.href = `/swaps/${notification.relatedEntity.id}`;
          break;
        case 'token':
          // Navigate to token history
          window.location.href = '/tokens';
          break;
        default:
          // Just mark as read, no navigation
          break;
      }
    }
  };

  const handleBackToList = () => {
    setSelectedNotification(null);
  };

  if (selectedNotification) {
    return (
      <div className="container mx-auto px-4 py-8 max-w-4xl">
        <div className="mb-6">
          <Button 
            variant="ghost" 
            onClick={handleBackToList}
            className="mb-4"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Notifications
          </Button>
          
          <Card>
            <CardHeader>
              <div className="flex items-start justify-between">
                <div>
                  <CardTitle className="flex items-center gap-2">
                    <Bell className="h-5 w-5" />
                    {selectedNotification.title}
                  </CardTitle>
                  <CardDescription>
                    {new Date(selectedNotification.createdAt).toLocaleString()}
                  </CardDescription>
                </div>
                
                <div className="flex items-center gap-2">
                  <Badge variant="outline" className="capitalize">
                    {selectedNotification.category}
                  </Badge>
                  <Badge 
                    variant={selectedNotification.priority === 'urgent' ? 'destructive' : 'secondary'}
                    className="capitalize"
                  >
                    {selectedNotification.priority}
                  </Badge>
                </div>
              </div>
            </CardHeader>
            
            <CardContent>
              <div className="space-y-4">
                <div>
                  <h3 className="font-medium mb-2">Message</h3>
                  <p className="text-gray-700 leading-relaxed">
                    {selectedNotification.message}
                  </p>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 pt-4 border-t">
                  <div>
                    <h4 className="font-medium text-sm text-gray-600 mb-1">Status</h4>
                    <Badge variant="outline" className="capitalize">
                      {selectedNotification.status}
                    </Badge>
                  </div>
                  
                  <div>
                    <h4 className="font-medium text-sm text-gray-600 mb-1">Type</h4>
                    <Badge variant="outline" className="capitalize">
                      {selectedNotification.type}
                    </Badge>
                  </div>
                  
                  {selectedNotification.sentAt && (
                    <div>
                      <h4 className="font-medium text-sm text-gray-600 mb-1">Sent At</h4>
                      <p className="text-sm">
                        {new Date(selectedNotification.sentAt).toLocaleString()}
                      </p>
                    </div>
                  )}
                  
                  {selectedNotification.deliveredAt && (
                    <div>
                      <h4 className="font-medium text-sm text-gray-600 mb-1">Delivered At</h4>
                      <p className="text-sm">
                        {new Date(selectedNotification.deliveredAt).toLocaleString()}
                      </p>
                    </div>
                  )}
                </div>
                
                {selectedNotification.relatedEntity && (
                  <div className="pt-4 border-t">
                    <h4 className="font-medium text-sm text-gray-600 mb-2">Related Item</h4>
                    <div className="flex items-center gap-2">
                      <Badge variant="outline" className="capitalize">
                        {selectedNotification.relatedEntity.type}
                      </Badge>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleNotificationClick(selectedNotification)}
                      >
                        <ExternalLink className="h-4 w-4 mr-1" />
                        View Details
                      </Button>
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8 max-w-6xl">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">Notifications</h1>
        <p className="text-gray-600">
          Manage your notification preferences and view your notification history
        </p>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="notifications" className="flex items-center gap-2">
            <Bell className="h-4 w-4" />
            Notifications
          </TabsTrigger>
          <TabsTrigger value="preferences" className="flex items-center gap-2">
            <Settings className="h-4 w-4" />
            Preferences
          </TabsTrigger>
          <TabsTrigger value="analytics" className="flex items-center gap-2">
            <BarChart3 className="h-4 w-4" />
            Analytics
          </TabsTrigger>
        </TabsList>

        <TabsContent value="notifications" className="space-y-6">
          <NotificationList
            onNotificationClick={handleNotificationClick}
            showFilters={true}
            pageSize={20}
            autoRefresh={true}
          />
        </TabsContent>

        <TabsContent value="preferences" className="space-y-6">
          <NotificationPreferences />
        </TabsContent>

        <TabsContent value="analytics" className="space-y-6">
          <NotificationStats />
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default NotificationsPage;
