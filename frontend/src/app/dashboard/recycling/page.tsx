'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, Tabs<PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import DashboardLayout from '@/components/layout/DashboardLayout';
import BulkDonationForm from '@/components/donations/BulkDonationForm';
import { 
  Recycle, 
  Scale, 
  Coins, 
  Leaf,
  Calendar,
  MapPin,
  Package,
  Award,
  Factory,
  TrendingUp,
  TreePine
} from 'lucide-react';

interface RecyclingStats {
  totalWeight: number;
  totalRecycling: number;
  tokensEarned: number;
  co2Saved: number;
  waterSaved: number;
  rank: string;
}

interface RecyclingEntry {
  _id: string;
  weight: number;
  tokensEarned: number;
  center: {
    name: string;
    location: string;
  };
  status: 'pending' | 'collected' | 'processed';
  createdAt: string;
  environmentalImpact: {
    co2Saved: number;
    waterSaved: number;
  };
}

interface RecyclingCenter {
  _id: string;
  name: string;
  description: string;
  location: string;
  totalRecycled: number;
  rewardRate: number;
  specialties: string[];
  image?: string;
}

export default function RecyclingPage() {
  const [stats, setStats] = useState<RecyclingStats>({
    totalWeight: 0,
    totalRecycling: 0,
    tokensEarned: 0,
    co2Saved: 0,
    waterSaved: 0,
    rank: 'Eco-Starter'
  });
  const [recyclingEntries, setRecyclingEntries] = useState<RecyclingEntry[]>([]);
  const [recyclingCenters, setRecyclingCenters] = useState<RecyclingCenter[]>([]);
  const [loading, setLoading] = useState(true);
  const [showRecyclingForm, setShowRecyclingForm] = useState(false);

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    try {
      const token = localStorage.getItem('token');
      
      // Fetch recycling stats
      const statsResponse = await fetch('/api/recycling/stats', {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      if (statsResponse.ok) {
        const statsData = await statsResponse.json();
        setStats(statsData.data);
      }

      // Fetch user recycling entries
      const entriesResponse = await fetch('/api/recycling/my-entries', {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      if (entriesResponse.ok) {
        const entriesData = await entriesResponse.json();
        setRecyclingEntries(entriesData.data || []);
      }

      // Fetch recycling centers
      const centersResponse = await fetch('/api/recycling-centers', {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      if (centersResponse.ok) {
        const centersData = await centersResponse.json();
        setRecyclingCenters(centersData.data || []);
      }
    } catch (error) {
      console.error('Error fetching recycling data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleRecyclingSubmit = async (recyclingData: any) => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch('/api/bulk-recycling', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({
          ...recyclingData,
          type: 'recycling'
        }),
      });

      if (response.ok) {
        alert('Recycling request submitted successfully! We will contact you soon.');
        setShowRecyclingForm(false);
        fetchData(); // Refresh data
      } else {
        const error = await response.json();
        alert(error.message || 'Failed to submit recycling request');
      }
    } catch (error) {
      console.error('Error submitting recycling request:', error);
      alert('Failed to submit recycling request. Please try again.');
    }
  };

  const getRankColor = (rank: string) => {
    switch (rank.toLowerCase()) {
      case 'eco-starter': return 'text-green-600';
      case 'eco-warrior': return 'text-blue-600';
      case 'eco-champion': return 'text-purple-600';
      case 'eco-legend': return 'text-yellow-600';
      default: return 'text-gray-600';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'collected': return 'bg-blue-100 text-blue-800';
      case 'processed': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  if (showRecyclingForm) {
    return (
      <DashboardLayout>
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Schedule Bulk Recycling</h1>
              <p className="text-gray-600">Recycle clothing in bulk and earn tokens while helping the environment</p>
            </div>
            <Button
              variant="outline"
              onClick={() => setShowRecyclingForm(false)}
            >
              Back to Recycling
            </Button>
          </div>

          <BulkDonationForm 
            onSubmit={handleRecyclingSubmit}
            loading={loading}
          />
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="space-y-8">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Recycling Center</h1>
            <p className="text-gray-600">Turn old clothes into environmental impact and earn tokens</p>
          </div>
          <Button
            onClick={() => setShowRecyclingForm(true)}
            className="bg-[#01796F] hover:bg-[#032221]"
          >
            <Recycle className="h-4 w-4 mr-2" />
            Schedule Recycling
          </Button>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
          <Card className="hover:shadow-lg transition-shadow duration-200">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Recycled</CardTitle>
              <Scale className="h-4 w-4 text-[#01796F]" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-[#01796F]">{stats.totalWeight}kg</div>
              <p className="text-xs text-gray-500">Clothing recycled</p>
            </CardContent>
          </Card>

          <Card className="hover:shadow-lg transition-shadow duration-200">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Tokens Earned</CardTitle>
              <Coins className="h-4 w-4 text-yellow-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-yellow-600">{stats.tokensEarned}</div>
              <p className="text-xs text-gray-500">From recycling</p>
            </CardContent>
          </Card>

          <Card className="hover:shadow-lg transition-shadow duration-200">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">CO₂ Saved</CardTitle>
              <Leaf className="h-4 w-4 text-green-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">{stats.co2Saved}kg</div>
              <p className="text-xs text-gray-500">Carbon footprint reduced</p>
            </CardContent>
          </Card>

          <Card className="hover:shadow-lg transition-shadow duration-200">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Water Saved</CardTitle>
              <TreePine className="h-4 w-4 text-blue-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-600">{stats.waterSaved}L</div>
              <p className="text-xs text-gray-500">Water conservation</p>
            </CardContent>
          </Card>

          <Card className="hover:shadow-lg transition-shadow duration-200">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Eco Rank</CardTitle>
              <Award className="h-4 w-4 text-purple-600" />
            </CardHeader>
            <CardContent>
              <div className={`text-lg font-bold ${getRankColor(stats.rank)}`}>{stats.rank}</div>
              <p className="text-xs text-gray-500">Environmental level</p>
            </CardContent>
          </Card>
        </div>

        {/* Environmental Impact Banner */}
        <Card className="bg-gradient-to-r from-green-50 to-blue-50 border-green-200">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-semibold text-green-800 mb-2">Your Environmental Impact</h3>
                <p className="text-green-700">
                  By recycling {stats.totalWeight}kg of clothing, you've saved {stats.co2Saved}kg of CO₂ 
                  and {stats.waterSaved}L of water. Keep up the great work!
                </p>
              </div>
              <div className="text-right">
                <div className="text-3xl">🌱</div>
                <p className="text-sm text-green-600 font-medium">Eco {stats.rank}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Tabs defaultValue="history" className="space-y-6">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="history">My Recycling</TabsTrigger>
            <TabsTrigger value="centers">Recycling Centers</TabsTrigger>
          </TabsList>

          <TabsContent value="history" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Package className="h-5 w-5" />
                  Recycling History
                </CardTitle>
                <CardDescription>
                  Track your recycling impact and environmental contribution
                </CardDescription>
              </CardHeader>
              <CardContent>
                {recyclingEntries.length === 0 ? (
                  <div className="text-center py-8">
                    <Recycle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-600 mb-4">No recycling entries yet</p>
                    <Button
                      onClick={() => setShowRecyclingForm(true)}
                      className="bg-[#01796F] hover:bg-[#032221]"
                    >
                      Start Recycling
                    </Button>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {recyclingEntries.map((entry) => (
                      <div key={entry._id} className="flex items-center justify-between p-4 border rounded-lg">
                        <div className="flex items-center gap-4">
                          <div className="p-2 rounded-full bg-green-100">
                            <Recycle className="h-4 w-4 text-green-600" />
                          </div>
                          <div>
                            <p className="font-medium">{entry.center.name}</p>
                            <div className="flex items-center gap-2 text-sm text-gray-500">
                              <MapPin className="h-3 w-3" />
                              <span>{entry.center.location}</span>
                              <span>•</span>
                              <Scale className="h-3 w-3" />
                              <span>{entry.weight}kg</span>
                            </div>
                            <div className="flex items-center gap-4 text-xs text-green-600 mt-1">
                              <span>CO₂: -{entry.environmentalImpact.co2Saved}kg</span>
                              <span>Water: -{entry.environmentalImpact.waterSaved}L</span>
                            </div>
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="flex items-center gap-2 mb-1">
                            <Coins className="h-4 w-4 text-yellow-600" />
                            <span className="font-medium">{entry.tokensEarned} tokens</span>
                          </div>
                          <Badge className={getStatusColor(entry.status)}>
                            {entry.status}
                          </Badge>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="centers" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {recyclingCenters.map((center) => (
                <Card key={center._id} className="hover:shadow-lg transition-shadow duration-200">
                  <CardHeader>
                    <div className="flex items-center gap-3">
                      <div className="p-2 bg-green-100 rounded-full">
                        <Factory className="h-5 w-5 text-green-600" />
                      </div>
                      <div>
                        <CardTitle className="text-lg">{center.name}</CardTitle>
                        <div className="flex items-center gap-1 text-sm text-gray-500">
                          <MapPin className="h-3 w-3" />
                          <span>{center.location}</span>
                        </div>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <p className="text-sm text-gray-600">{center.description}</p>
                    
                    <div className="flex flex-wrap gap-1">
                      {center.specialties.map((specialty, index) => (
                        <Badge key={index} variant="secondary" className="text-xs">
                          {specialty}
                        </Badge>
                      ))}
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <Coins className="h-4 w-4 text-yellow-600" />
                        <span className="text-sm font-medium">{center.rewardRate} tokens/kg</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <TrendingUp className="h-4 w-4 text-gray-400" />
                        <span className="text-sm text-gray-500">{center.totalRecycled}kg processed</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  );
}
