'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import DashboardLayout from '@/components/layout/DashboardLayout';
import BulkDonationForm from '@/components/donations/BulkDonationForm';
import { 
  Heart, 
  Scale, 
  Coins, 
  TrendingUp,
  Calendar,
  MapPin,
  Package,
  Award,
  Users,
  Building
} from 'lucide-react';

interface DonationStats {
  totalWeight: number;
  totalDonations: number;
  tokensEarned: number;
  impactScore: number;
  rank: string;
}

interface Donation {
  _id: string;
  type: 'charity' | 'recycling';
  weight: number;
  tokensEarned: number;
  partner: {
    name: string;
    location: string;
  };
  status: 'pending' | 'collected' | 'completed';
  createdAt: string;
}

interface CharityPartner {
  _id: string;
  name: string;
  description: string;
  location: string;
  totalDonations: number;
  rewardRate: number;
  image?: string;
}

export default function DonationsPage() {
  const [stats, setStats] = useState<DonationStats>({
    totalWeight: 0,
    totalDonations: 0,
    tokensEarned: 0,
    impactScore: 0,
    rank: 'Bronze'
  });
  const [donations, setDonations] = useState<Donation[]>([]);
  const [charityPartners, setCharityPartners] = useState<CharityPartner[]>([]);
  const [loading, setLoading] = useState(true);
  const [showDonationForm, setShowDonationForm] = useState(false);

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    try {
      const token = localStorage.getItem('token');
      
      // Fetch donation stats
      const statsResponse = await fetch('/api/donations/stats', {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      if (statsResponse.ok) {
        const statsData = await statsResponse.json();
        setStats(statsData.data);
      }

      // Fetch user donations
      const donationsResponse = await fetch('/api/donations/my-donations', {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      if (donationsResponse.ok) {
        const donationsData = await donationsResponse.json();
        setDonations(donationsData.data || []);
      }

      // Fetch charity partners
      const partnersResponse = await fetch('/api/charity-partners', {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      if (partnersResponse.ok) {
        const partnersData = await partnersResponse.json();
        setCharityPartners(partnersData.data || []);
      }
    } catch (error) {
      console.error('Error fetching donation data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleDonationSubmit = async (donationData: any) => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch('/api/bulk-donations', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify(donationData),
      });

      if (response.ok) {
        alert('Donation submitted successfully! We will contact you soon.');
        setShowDonationForm(false);
        fetchData(); // Refresh data
      } else {
        const error = await response.json();
        alert(error.message || 'Failed to submit donation');
      }
    } catch (error) {
      console.error('Error submitting donation:', error);
      alert('Failed to submit donation. Please try again.');
    }
  };

  const getRankColor = (rank: string) => {
    switch (rank.toLowerCase()) {
      case 'bronze': return 'text-amber-600';
      case 'silver': return 'text-gray-500';
      case 'gold': return 'text-yellow-500';
      case 'platinum': return 'text-purple-600';
      default: return 'text-gray-600';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'collected': return 'bg-blue-100 text-blue-800';
      case 'completed': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  if (showDonationForm) {
    return (
      <DashboardLayout>
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Make a Bulk Donation</h1>
              <p className="text-gray-600">Donate clothing in bulk and earn tokens</p>
            </div>
            <Button
              variant="outline"
              onClick={() => setShowDonationForm(false)}
            >
              Back to Donations
            </Button>
          </div>

          <BulkDonationForm 
            onSubmit={handleDonationSubmit}
            loading={loading}
          />
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="space-y-8">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Donations & Recycling</h1>
            <p className="text-gray-600">Make a positive impact and earn tokens</p>
          </div>
          <Button
            onClick={() => setShowDonationForm(true)}
            className="bg-[#01796F] hover:bg-[#032221]"
          >
            <Heart className="h-4 w-4 mr-2" />
            Make Bulk Donation
          </Button>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card className="hover:shadow-lg transition-shadow duration-200">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Weight</CardTitle>
              <Scale className="h-4 w-4 text-[#01796F]" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-[#01796F]">{stats.totalWeight}kg</div>
              <p className="text-xs text-gray-500">Clothing donated</p>
            </CardContent>
          </Card>

          <Card className="hover:shadow-lg transition-shadow duration-200">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Donations</CardTitle>
              <Heart className="h-4 w-4 text-red-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-red-600">{stats.totalDonations}</div>
              <p className="text-xs text-gray-500">Successful donations</p>
            </CardContent>
          </Card>

          <Card className="hover:shadow-lg transition-shadow duration-200">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Tokens Earned</CardTitle>
              <Coins className="h-4 w-4 text-yellow-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-yellow-600">{stats.tokensEarned}</div>
              <p className="text-xs text-gray-500">From donations</p>
            </CardContent>
          </Card>

          <Card className="hover:shadow-lg transition-shadow duration-200">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Impact Rank</CardTitle>
              <Award className="h-4 w-4 text-purple-600" />
            </CardHeader>
            <CardContent>
              <div className={`text-2xl font-bold ${getRankColor(stats.rank)}`}>{stats.rank}</div>
              <p className="text-xs text-gray-500">Donor level</p>
            </CardContent>
          </Card>
        </div>

        <Tabs defaultValue="history" className="space-y-6">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="history">My Donations</TabsTrigger>
            <TabsTrigger value="partners">Charity Partners</TabsTrigger>
          </TabsList>

          <TabsContent value="history" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Package className="h-5 w-5" />
                  Donation History
                </CardTitle>
                <CardDescription>
                  Track your donation impact and rewards
                </CardDescription>
              </CardHeader>
              <CardContent>
                {donations.length === 0 ? (
                  <div className="text-center py-8">
                    <Heart className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-600 mb-4">No donations yet</p>
                    <Button
                      onClick={() => setShowDonationForm(true)}
                      className="bg-[#01796F] hover:bg-[#032221]"
                    >
                      Make Your First Donation
                    </Button>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {donations.map((donation) => (
                      <div key={donation._id} className="flex items-center justify-between p-4 border rounded-lg">
                        <div className="flex items-center gap-4">
                          <div className={`p-2 rounded-full ${
                            donation.type === 'charity' ? 'bg-red-100' : 'bg-green-100'
                          }`}>
                            {donation.type === 'charity' ? (
                              <Heart className="h-4 w-4 text-red-600" />
                            ) : (
                              <Package className="h-4 w-4 text-green-600" />
                            )}
                          </div>
                          <div>
                            <p className="font-medium">{donation.partner.name}</p>
                            <div className="flex items-center gap-2 text-sm text-gray-500">
                              <MapPin className="h-3 w-3" />
                              <span>{donation.partner.location}</span>
                              <span>•</span>
                              <Scale className="h-3 w-3" />
                              <span>{donation.weight}kg</span>
                            </div>
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="flex items-center gap-2 mb-1">
                            <Coins className="h-4 w-4 text-yellow-600" />
                            <span className="font-medium">{donation.tokensEarned} tokens</span>
                          </div>
                          <Badge className={getStatusColor(donation.status)}>
                            {donation.status}
                          </Badge>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="partners" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {charityPartners.map((partner) => (
                <Card key={partner._id} className="hover:shadow-lg transition-shadow duration-200">
                  <CardHeader>
                    <div className="flex items-center gap-3">
                      <div className="p-2 bg-red-100 rounded-full">
                        <Building className="h-5 w-5 text-red-600" />
                      </div>
                      <div>
                        <CardTitle className="text-lg">{partner.name}</CardTitle>
                        <div className="flex items-center gap-1 text-sm text-gray-500">
                          <MapPin className="h-3 w-3" />
                          <span>{partner.location}</span>
                        </div>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <p className="text-sm text-gray-600">{partner.description}</p>
                    
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <Coins className="h-4 w-4 text-yellow-600" />
                        <span className="text-sm font-medium">{partner.rewardRate} tokens/kg</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Users className="h-4 w-4 text-gray-400" />
                        <span className="text-sm text-gray-500">{partner.totalDonations} donations</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  );
}
