'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import DashboardLayout from '@/components/layout/DashboardLayout';
import { 
  Users, 
  Shirt, 
  Heart, 
  Recycle, 
  Coins,
  TrendingUp,
  AlertTriangle,
  CheckCircle,
  Clock,
  DollarSign,
  Package,
  Building,
  BarChart3
} from 'lucide-react';

interface AdminStats {
  users: {
    total: number;
    active: number;
    newThisMonth: number;
    verified: number;
  };
  clothing: {
    total: number;
    available: number;
    swapped: number;
    donated: number;
  };
  donations: {
    total: number;
    totalWeight: number;
    totalValue: number;
    pendingPickups: number;
  };
  recycling: {
    total: number;
    totalWeight: number;
    co2Saved: number;
    pendingProcessing: number;
  };
  tokens: {
    totalIssued: number;
    totalSpent: number;
    totalPurchased: number;
    revenue: number;
  };
  partners: {
    charities: number;
    recyclingCenters: number;
    activePartnerships: number;
  };
}

interface RecentActivity {
  _id: string;
  type: 'user_registration' | 'clothing_listed' | 'donation' | 'recycling' | 'token_purchase';
  description: string;
  user: {
    firstName: string;
    lastName: string;
  };
  createdAt: string;
  status: 'pending' | 'completed' | 'failed';
}

export default function AdminDashboardPage() {
  const [stats, setStats] = useState<AdminStats>({
    users: { total: 0, active: 0, newThisMonth: 0, verified: 0 },
    clothing: { total: 0, available: 0, swapped: 0, donated: 0 },
    donations: { total: 0, totalWeight: 0, totalValue: 0, pendingPickups: 0 },
    recycling: { total: 0, totalWeight: 0, co2Saved: 0, pendingProcessing: 0 },
    tokens: { totalIssued: 0, totalSpent: 0, totalPurchased: 0, revenue: 0 },
    partners: { charities: 0, recyclingCenters: 0, activePartnerships: 0 }
  });
  
  const [recentActivity, setRecentActivity] = useState<RecentActivity[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchAdminData();
  }, []);

  const fetchAdminData = async () => {
    try {
      const token = localStorage.getItem('token');
      
      // Fetch admin statistics
      const statsResponse = await fetch('/api/admin/stats', {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      if (statsResponse.ok) {
        const statsData = await statsResponse.json();
        setStats(statsData.data);
      }

      // Fetch recent activity
      const activityResponse = await fetch('/api/admin/recent-activity', {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      if (activityResponse.ok) {
        const activityData = await activityResponse.json();
        setRecentActivity(activityData.data || []);
      }
    } catch (error) {
      console.error('Error fetching admin data:', error);
    } finally {
      setLoading(false);
    }
  };

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'user_registration': return <Users className="h-4 w-4 text-blue-600" />;
      case 'clothing_listed': return <Shirt className="h-4 w-4 text-green-600" />;
      case 'donation': return <Heart className="h-4 w-4 text-red-600" />;
      case 'recycling': return <Recycle className="h-4 w-4 text-[#01796F]" />;
      case 'token_purchase': return <Coins className="h-4 w-4 text-yellow-600" />;
      default: return <Package className="h-4 w-4 text-gray-600" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-100 text-green-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'failed': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#01796F]"></div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="space-y-8">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Admin Dashboard</h1>
            <p className="text-gray-600">Platform overview and management</p>
          </div>
          <div className="flex gap-3">
            <Button variant="outline">
              <BarChart3 className="h-4 w-4 mr-2" />
              Generate Report
            </Button>
            <Button className="bg-[#032221] hover:bg-[#01796F]">
              <AlertTriangle className="h-4 w-4 mr-2" />
              System Alerts
            </Button>
          </div>
        </div>

        {/* Key Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card className="hover:shadow-lg transition-shadow duration-200">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Users</CardTitle>
              <Users className="h-4 w-4 text-blue-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-600">{stats.users.total.toLocaleString()}</div>
              <p className="text-xs text-gray-500">
                {stats.users.newThisMonth} new this month
              </p>
            </CardContent>
          </Card>

          <Card className="hover:shadow-lg transition-shadow duration-200">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Active Listings</CardTitle>
              <Shirt className="h-4 w-4 text-green-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">{stats.clothing.available.toLocaleString()}</div>
              <p className="text-xs text-gray-500">
                {stats.clothing.total} total items
              </p>
            </CardContent>
          </Card>

          <Card className="hover:shadow-lg transition-shadow duration-200">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Donations</CardTitle>
              <Heart className="h-4 w-4 text-red-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-red-600">{stats.donations.totalWeight}kg</div>
              <p className="text-xs text-gray-500">
                {stats.donations.total} donations
              </p>
            </CardContent>
          </Card>

          <Card className="hover:shadow-lg transition-shadow duration-200">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Token Revenue</CardTitle>
              <DollarSign className="h-4 w-4 text-yellow-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-yellow-600">
                KES {stats.tokens.revenue.toLocaleString()}
              </div>
              <p className="text-xs text-gray-500">
                {stats.tokens.totalPurchased.toLocaleString()} tokens sold
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Secondary Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Recycle className="h-5 w-5 text-[#01796F]" />
                Environmental Impact
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Total Recycled:</span>
                <span className="font-medium">{stats.recycling.totalWeight}kg</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">CO₂ Saved:</span>
                <span className="font-medium text-green-600">{stats.recycling.co2Saved}kg</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Pending Processing:</span>
                <Badge variant="secondary">{stats.recycling.pendingProcessing}</Badge>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Building className="h-5 w-5 text-purple-600" />
                Partnerships
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Charity Partners:</span>
                <span className="font-medium">{stats.partners.charities}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Recycling Centers:</span>
                <span className="font-medium">{stats.partners.recyclingCenters}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Active Partnerships:</span>
                <Badge variant="default">{stats.partners.activePartnerships}</Badge>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Coins className="h-5 w-5 text-yellow-600" />
                Token Economy
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Total Issued:</span>
                <span className="font-medium">{stats.tokens.totalIssued.toLocaleString()}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Total Spent:</span>
                <span className="font-medium">{stats.tokens.totalSpent.toLocaleString()}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Circulation:</span>
                <Badge variant="secondary">
                  {(stats.tokens.totalIssued - stats.tokens.totalSpent).toLocaleString()}
                </Badge>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Recent Activity */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Clock className="h-5 w-5" />
              Recent Activity
            </CardTitle>
            <CardDescription>
              Latest platform activities and user interactions
            </CardDescription>
          </CardHeader>
          <CardContent>
            {recentActivity.length === 0 ? (
              <div className="text-center py-8">
                <Clock className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-600">No recent activity</p>
              </div>
            ) : (
              <div className="space-y-4">
                {recentActivity.slice(0, 10).map((activity) => (
                  <div key={activity._id} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex items-center gap-3">
                      <div className="p-2 rounded-full bg-gray-100">
                        {getActivityIcon(activity.type)}
                      </div>
                      <div>
                        <p className="text-sm font-medium">{activity.description}</p>
                        <p className="text-xs text-gray-500">
                          by {activity.user.firstName} {activity.user.lastName} • {' '}
                          {new Date(activity.createdAt).toLocaleDateString()}
                        </p>
                      </div>
                    </div>
                    <Badge className={getStatusColor(activity.status)}>
                      {activity.status}
                    </Badge>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Quick Actions */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card className="hover:shadow-lg transition-shadow duration-200 cursor-pointer">
            <CardContent className="p-6 text-center">
              <Users className="h-8 w-8 mx-auto mb-3 text-blue-600" />
              <h3 className="font-medium text-gray-900 mb-1">Manage Users</h3>
              <p className="text-sm text-gray-600">View and moderate users</p>
            </CardContent>
          </Card>

          <Card className="hover:shadow-lg transition-shadow duration-200 cursor-pointer">
            <CardContent className="p-6 text-center">
              <Shirt className="h-8 w-8 mx-auto mb-3 text-green-600" />
              <h3 className="font-medium text-gray-900 mb-1">Clothing Items</h3>
              <p className="text-sm text-gray-600">Moderate listings</p>
            </CardContent>
          </Card>

          <Card className="hover:shadow-lg transition-shadow duration-200 cursor-pointer">
            <CardContent className="p-6 text-center">
              <Heart className="h-8 w-8 mx-auto mb-3 text-red-600" />
              <h3 className="font-medium text-gray-900 mb-1">Donations</h3>
              <p className="text-sm text-gray-600">Track charity impact</p>
            </CardContent>
          </Card>

          <Card className="hover:shadow-lg transition-shadow duration-200 cursor-pointer">
            <CardContent className="p-6 text-center">
              <BarChart3 className="h-8 w-8 mx-auto mb-3 text-purple-600" />
              <h3 className="font-medium text-gray-900 mb-1">Analytics</h3>
              <p className="text-sm text-gray-600">Platform insights</p>
            </CardContent>
          </Card>
        </div>
      </div>
    </DashboardLayout>
  );
}
