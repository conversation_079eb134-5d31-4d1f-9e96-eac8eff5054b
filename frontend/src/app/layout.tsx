import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON>, <PERSON><PERSON><PERSON>_Mon<PERSON> } from "next/font/google";
import "./globals.css";

// Using Inter as a high-quality alternative to Axiforma
// Inter provides excellent readability and modern aesthetics
const inter = Inter({
  variable: "--font-inter",
  subsets: ["latin"],
  weight: ["400", "500", "600", "700"],
  display: "swap",
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Pedi - Sustainable Fashion Exchange Platform",
  description: "Kenya's first sustainable clothing exchange platform. Swap, donate, and discover pre-loved fashion while earning Pedi tokens and making a positive impact on our environment.",
  keywords: "sustainable fashion, clothing exchange, Kenya, circular fashion, eco-friendly, pre-loved clothes, fashion swap, environmental impact",
  authors: [{ name: "Pedi Team" }],
  openGraph: {
    title: "Pedi - Sustainable Fashion Exchange Platform",
    description: "Join Kenya's sustainable clothing exchange platform. Swap, donate, and discover pre-loved fashion while making a positive environmental impact.",
    type: "website",
    locale: "en_KE",
  },
  twitter: {
    card: "summary_large_image",
    title: "Pedi - Sustainable Fashion Exchange Platform",
    description: "Kenya's first sustainable clothing exchange platform for eco-conscious fashion lovers.",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${inter.variable} ${geistMono.variable} antialiased font-body overflow-x-hidden`}
      >
        <div>
          {children}
        </div>
      </body>
    </html>
  );
}
