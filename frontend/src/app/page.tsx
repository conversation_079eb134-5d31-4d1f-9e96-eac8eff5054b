'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { LandingPage } from '@/components/landing';

export default function Home() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  useEffect(() => {
    // Check if user is logged in
    const token = localStorage.getItem('token');

    if (token) {
      // User is logged in, redirect to dashboard
      setIsAuthenticated(true);
      router.push('/dashboard');
    } else {
      // User is not logged in, show landing page
      setIsAuthenticated(false);
      setIsLoading(false);
    }
  }, [router]);

  // Show loading spinner while checking authentication
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-brand-pine-green mx-auto mb-4"></div>
          <p className="text-gray-600">Loading Pedi...</p>
        </div>
      </div>
    );
  }

  // Show landing page for non-authenticated users
  if (!isAuthenticated) {
    return <LandingPage />;
  }

  // This should not be reached as authenticated users are redirected
  return null;
}
