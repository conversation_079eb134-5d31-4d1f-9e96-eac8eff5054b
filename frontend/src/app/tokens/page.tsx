'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { TokenBalance } from '@/components/tokens/TokenBalance';
import { TokenHistory } from '@/components/tokens/TokenHistory';
import { EarningOpportunities } from '@/components/tokens/EarningOpportunities';
import { Achievements } from '@/components/tokens/Achievements';
import { Leaderboard } from '@/components/tokens/Leaderboard';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Coins, 
  TrendingUp, 
  Target, 
  History, 
  Trophy,
  Users,
  ArrowRight,
  AlertCircle,
  RefreshCw
} from 'lucide-react';

interface TokenStats {
  currentBalance: number;
  totalEarned: number;
  totalSpent: number;
  totalTransactions: number;
  recentActivity: number;
  sustainabilityScore: number;
}

interface LeaderboardEntry {
  rank: number;
  name: string;
  tokens: number;
  sustainabilityScore: number;
  county: string;
}

export default function TokensPage() {
  const router = useRouter();
  const [stats, setStats] = useState<TokenStats | null>(null);
  const [leaderboard, setLeaderboard] = useState<LeaderboardEntry[]>([]);
  const [refreshTrigger, setRefreshTrigger] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    // Check authentication
    const token = localStorage.getItem('token');
    if (!token) {
      router.push('/auth/login');
      return;
    }

    fetchTokenStats();
    fetchLeaderboard();
  }, [router, refreshTrigger]);

  const fetchTokenStats = async () => {
    try {
      const token = localStorage.getItem('token');
      if (!token) return;

      const response = await fetch('/api/tokens?endpoint=stats', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      const data = await response.json();

      if (response.ok && data.success) {
        setStats(data.data);
      }
    } catch (err) {
      console.error('Error fetching token stats:', err);
    }
  };

  const fetchLeaderboard = async () => {
    try {
      const response = await fetch('/api/tokens?endpoint=leaderboard&limit=10');
      const data = await response.json();

      if (response.ok && data.success) {
        setLeaderboard(data.data.leaderboard || []);
      }
    } catch (err) {
      console.error('Error fetching leaderboard:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = () => {
    setRefreshTrigger(prev => prev + 1);
  };

  const handleOpportunityAction = (action: string) => {
    // Handle different opportunity actions
    switch (action) {
      case 'claim_daily_bonus':
        handleRefresh();
        break;
      case 'list_item':
        router.push('/list-item');
        break;
      case 'refer_friend':
        // Open share dialog or copy referral link
        if (navigator.share) {
          navigator.share({
            title: 'Join Pedi - Sustainable Fashion Exchange',
            text: 'Join me on Pedi, the sustainable fashion platform! Earn tokens by swapping clothes and reducing textile waste.',
            url: window.location.origin
          });
        } else {
          // Fallback: copy to clipboard
          navigator.clipboard.writeText(window.location.origin);
        }
        break;
      case 'write_review':
        router.push('/transactions');
        break;
      default:
        break;
    }
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-48 mb-6" />
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-32 bg-gray-200 rounded-lg" />
            ))}
          </div>
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div className="h-96 bg-gray-200 rounded-lg" />
            <div className="h-96 bg-gray-200 rounded-lg" />
            <div className="h-96 bg-gray-200 rounded-lg" />
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="flex items-center justify-between mb-8">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Token Dashboard</h1>
          <p className="text-gray-600 mt-2">
            Manage your Pedi tokens and track your sustainable fashion journey
          </p>
        </div>
        <Button onClick={handleRefresh} variant="outline">
          <RefreshCw className="h-4 w-4 mr-2" />
          Refresh
        </Button>
      </div>

      {/* Stats Cards */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Current Balance</p>
                  <p className="text-2xl font-bold text-green-600">
                    {stats.currentBalance.toLocaleString()}
                  </p>
                </div>
                <Coins className="h-8 w-8 text-green-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Earned</p>
                  <p className="text-2xl font-bold text-blue-600">
                    {stats.totalEarned.toLocaleString()}
                  </p>
                </div>
                <TrendingUp className="h-8 w-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Transactions</p>
                  <p className="text-2xl font-bold text-purple-600">
                    {stats.totalTransactions}
                  </p>
                </div>
                <History className="h-8 w-8 text-purple-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Sustainability Score</p>
                  <p className="text-2xl font-bold text-orange-600">
                    {stats.sustainabilityScore}
                  </p>
                </div>
                <Target className="h-8 w-8 text-orange-600" />
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Main Content */}
      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="history">History</TabsTrigger>
          <TabsTrigger value="opportunities">Earn More</TabsTrigger>
          <TabsTrigger value="achievements">Achievements</TabsTrigger>
          <TabsTrigger value="leaderboard">Leaderboard</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <TokenBalance 
              onClaimDailyBonus={handleRefresh}
              onRefresh={handleRefresh}
            />
            <EarningOpportunities 
              refreshTrigger={refreshTrigger}
              onOpportunityAction={handleOpportunityAction}
            />
          </div>
        </TabsContent>

        <TabsContent value="history">
          <TokenHistory refreshTrigger={refreshTrigger} />
        </TabsContent>

        <TabsContent value="opportunities">
          <EarningOpportunities 
            refreshTrigger={refreshTrigger}
            onOpportunityAction={handleOpportunityAction}
          />
        </TabsContent>

        <TabsContent value="achievements">
          <Achievements
            refreshTrigger={refreshTrigger}
            onAchievementUnlocked={(achievement) => {
              // Handle achievement unlocked notification
              console.log('Achievement unlocked:', achievement);
              // You could show a toast notification here
            }}
          />
        </TabsContent>

        <TabsContent value="leaderboard">
          <Leaderboard refreshTrigger={refreshTrigger} />
        </TabsContent>
      </Tabs>
    </div>
  );
}
