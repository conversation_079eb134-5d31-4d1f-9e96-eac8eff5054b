@import "tailwindcss";

:root {
  --background: #FFFFFF;
  --foreground: #032221;

  /* Enhanced Sustainable Fashion Color Palette */

  /* Base Colors */
  --color-white: #FFFFFF;              /* White - Base/background color */
  --color-dark-green: #032221;         /* Dark Green - Primary accent color */
  --color-pine-green: #01796F;         /* Pine Green - Secondary accent color */
  
  /* Extended Color Palette */
  --color-light-green: #E8F5F3;        /* Light green for backgrounds */
  --color-sage-green: #A8D5BA;         /* Sage green for accents */
  --color-forest-green: #2D5A27;       /* Forest green for depth */
  --color-mint: #B8E6B8;               /* Mint for highlights */
  --color-cream: #FEFEFE;              /* Cream for soft backgrounds */
  --color-charcoal: #060606;           /* Charcoal for text */
  --color-soft-gray: #F7F9FC;          /* Soft gray for sections */
  --color-warm-gray: #000000;          /* Black for muted text (improved visibility) */
  --color-black: #000000;              /* Pure black for maximum contrast */

  /* Semantic Color Mappings for shadcn/ui */
  --primary: var(--color-dark-green);
  --primary-foreground: var(--color-white);
  --secondary: var(--color-pine-green);
  --secondary-foreground: var(--color-white);
  --accent: var(--color-sage-green);
  --accent-foreground: var(--color-dark-green);
  --muted: var(--color-soft-gray);
  --muted-foreground: var(--color-black);
  --destructive: #E74C3C;
  --destructive-foreground: var(--color-white);
  --border: #E1E8ED;
  --input: #F1F5F9;
  --ring: var(--color-pine-green);
  --card: var(--color-white);
  --card-foreground: var(--color-dark-green);
  --popover: var(--color-white);
  --popover-foreground: var(--color-dark-green);

  /* Enhanced Gradient variations */
  --gradient-primary: linear-gradient(135deg, var(--color-dark-green) 0%, var(--color-pine-green) 100%);
  --gradient-hero: linear-gradient(135deg, var(--color-dark-green) 0%, var(--color-pine-green) 50%, var(--color-forest-green) 100%);
  --gradient-soft: linear-gradient(135deg, var(--color-light-green) 0%, var(--color-sage-green) 100%);
  --gradient-overlay: linear-gradient(135deg, rgba(3, 34, 33, 0.95) 0%, rgba(1, 121, 111, 0.9) 100%);
  --gradient-card: linear-gradient(145deg, var(--color-white) 0%, var(--color-light-green) 100%);
  --gradient-mesh: radial-gradient(circle at 20% 50%, rgba(1, 121, 111, 0.1) 0%, transparent 50%), 
                   radial-gradient(circle at 80% 20%, rgba(3, 34, 33, 0.1) 0%, transparent 50%),
                   radial-gradient(circle at 40% 80%, rgba(168, 213, 186, 0.1) 0%, transparent 50%);
}



@media (prefers-color-scheme: dark) {
  :root {
    --background: var(--color-dark-green);
    --foreground: var(--color-white);
    --card: var(--color-dark-green);
    --card-foreground: var(--color-white);
    --popover: var(--color-dark-green);
    --popover-foreground: var(--color-white);
    --muted: rgba(1, 121, 111, 0.1);
    --muted-foreground: var(--color-white);
    --border: rgba(1, 121, 111, 0.2);
    --input: rgba(1, 121, 111, 0.2);
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-inter), var(--font-sans), Arial, Helvetica, sans-serif;
  font-weight: 400;
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  overflow-x: hidden;
  margin: 0;
  padding: 0;
}

html {
  margin: 0;
  padding: 0;
}

/* Enhanced base styles */
* {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

/* Selection styles */
::selection {
  background: var(--color-sage-green);
  color: var(--color-dark-green);
}

::-moz-selection {
  background: var(--color-sage-green);
  color: var(--color-dark-green);
}

/* Typography Hierarchy */
.font-heading {
  font-family: var(--font-inter), var(--font-sans), Arial, Helvetica, sans-serif;
  font-weight: 600; /* Semi Bold equivalent */
  line-height: 1.2;
  letter-spacing: -0.025em;
}

.font-subheading {
  font-family: var(--font-inter), var(--font-sans), Arial, Helvetica, sans-serif;
  font-weight: 500; /* Medium */
  line-height: 1.4;
  letter-spacing: -0.015em;
}

.font-body {
  font-family: var(--font-inter), var(--font-sans), Arial, Helvetica, sans-serif;
  font-weight: 400; /* Regular */
  line-height: 1.6;
}

/* Enhanced animations and transitions */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

/* Animation classes */
.animate-fade-in-up {
  animation: fadeInUp 0.6s ease-out forwards;
}

.animate-fade-in-down {
  animation: fadeInDown 0.6s ease-out forwards;
}

.animate-fade-in-left {
  animation: fadeInLeft 0.6s ease-out forwards;
}

.animate-fade-in-right {
  animation: fadeInRight 0.6s ease-out forwards;
}

.animate-scale-in {
  animation: scaleIn 0.4s ease-out forwards;
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-pulse-slow {
  animation: pulse 3s ease-in-out infinite;
}

.animate-shimmer {
  animation: shimmer 2s infinite;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  background-size: 200px 100%;
}

/* Modern CSS Effects and Utilities */
.transition-all-smooth {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.transition-colors-smooth {
  transition: color 0.2s ease-in-out, background-color 0.2s ease-in-out, border-color 0.2s ease-in-out;
}

.transition-transform-smooth {
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.hover-lift {
  transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.hover-lift:hover {
  transform: translateY(-2px);
}

.hover-scale {
  transition: transform 0.2s ease-in-out;
}

.hover-scale:hover {
  transform: scale(1.05);
}

.hover-scale-sm:hover {
  transform: scale(1.02);
}

/* Gradient backgrounds */
.gradient-primary {
  background: linear-gradient(135deg, var(--color-dark-green) 0%, var(--color-pine-green) 100%);
}

.gradient-secondary {
  background: linear-gradient(135deg, var(--color-pine-green) 0%, var(--color-dark-green) 100%);
}

.gradient-hero {
  background: linear-gradient(135deg, var(--color-dark-green) 0%, var(--color-pine-green) 50%, var(--color-dark-green) 100%);
}

.gradient-overlay {
  background: linear-gradient(135deg, rgba(3, 34, 33, 0.9) 0%, rgba(1, 121, 111, 0.8) 100%);
}

.gradient-text {
  background: linear-gradient(135deg, var(--color-pine-green) 0%, var(--color-dark-green) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Glass morphism effect */
.glass {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.glass-dark {
  background: rgba(3, 34, 33, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(3, 34, 33, 0.2);
}

/* Enhanced shadows with modern depth */
.shadow-soft {
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
}

.shadow-medium {
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.16), 0 3px 6px rgba(0, 0, 0, 0.23);
}

.shadow-strong {
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.19), 0 6px 6px rgba(0, 0, 0, 0.23);
}

.shadow-colored {
  box-shadow: 0 4px 15px rgba(1, 121, 111, 0.2), 0 2px 4px rgba(1, 121, 111, 0.1);
}

.shadow-glow {
  box-shadow: 0 0 20px rgba(1, 121, 111, 0.3), 0 0 40px rgba(1, 121, 111, 0.1);
}

.shadow-inner {
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.06);
}

.shadow-elevated {
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Focus states */
.focus-ring:focus-visible {
  outline: none;
  box-shadow: 0 0 0 2px var(--color-white), 0 0 0 4px var(--color-pine-green);
}

/* Scrollbar styling */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--color-white);
}

::-webkit-scrollbar-thumb {
  background: var(--color-pine-green);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--color-dark-green);
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* Animation classes */
.animate-fade-in-up {
  animation: fadeInUp 0.6s ease-out forwards;
}

.animate-fade-in-left {
  animation: fadeInLeft 0.6s ease-out forwards;
}

.animate-fade-in-right {
  animation: fadeInRight 0.6s ease-out forwards;
}

.animate-scale-in {
  animation: scaleIn 0.5s ease-out forwards;
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-shimmer {
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

/* Smooth transitions for interactive elements */
.transition-all-smooth {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.hover-lift {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Gradient text effect */
.gradient-text {
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Enhanced utility classes for the new color scheme */
.bg-brand-gradient {
  background: var(--gradient-primary);
}

.bg-brand-hero {
  background: var(--gradient-hero);
}

.bg-brand-soft {
  background: var(--gradient-soft);
}

.bg-brand-card {
  background: var(--gradient-card);
}

.bg-brand-mesh {
  background: var(--gradient-mesh);
}

.text-brand-primary {
  color: var(--color-dark-green);
}

.text-brand-secondary {
  color: var(--color-pine-green);
}

.text-brand-sage {
  color: var(--color-sage-green);
}

.text-brand-charcoal {
  color: var(--color-charcoal);
}

.text-brand-black {
  color: var(--color-black);
}

/* Override any gray text colors to use black for better visibility */
.text-gray-500,
.text-gray-600,
.text-gray-700,
.text-gray-800,
.text-gray-900 {
  color: var(--color-black) !important;
}

/* Ensure muted text is black for better readability */
.text-muted-foreground {
  color: var(--color-black) !important;
}

/* Override warm gray text to black */
.text-brand-warm-gray {
  color: var(--color-black) !important;
}

.border-brand-primary {
  border-color: var(--color-dark-green);
}

.border-brand-secondary {
  border-color: var(--color-pine-green);
}

.border-brand-sage {
  border-color: var(--color-sage-green);
}

/* Modern card styles */
.card-modern {
  background: var(--gradient-card);
  border: 1px solid var(--color-sage-green);
  border-radius: 16px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.card-modern:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.card-glass {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(12px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
}

.card-elevated {
  background: var(--color-white);
  border-radius: 20px;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

/* Button enhancements */
.btn-modern {
  background: var(--gradient-primary);
  border: none;
  border-radius: 12px;
  color: var(--color-white);
  font-weight: 500;
  padding: 12px 24px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4px 6px -1px rgba(1, 121, 111, 0.3);
}

.btn-modern:hover {
  transform: translateY(-1px);
  box-shadow: 0 10px 15px -3px rgba(1, 121, 111, 0.4);
}

.btn-outline-modern {
  background: transparent;
  border: 2px solid var(--color-pine-green);
  border-radius: 12px;
  color: var(--color-pine-green);
  font-weight: 500;
  padding: 10px 22px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.btn-outline-modern:hover {
  background: var(--color-pine-green);
  color: var(--color-white);
  transform: translateY(-1px);
}

/* Input enhancements */
.input-modern {
  background: var(--color-white);
  border: 2px solid var(--color-sage-green);
  border-radius: 12px;
  padding: 12px 16px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-size: 16px;
}

.input-modern:focus {
  border-color: var(--color-pine-green);
  box-shadow: 0 0 0 3px rgba(1, 121, 111, 0.1);
  outline: none;
}

/* Loading states */
.loading-shimmer {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

.loading-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Micro-interactions */
.hover-glow:hover {
  box-shadow: 0 0 20px rgba(1, 121, 111, 0.4);
}

.hover-bounce:hover {
  animation: bounce 0.6s ease-in-out;
}

.click-scale:active {
  transform: scale(0.98);
}

/* Modern spacing utilities */
.space-modern > * + * {
  margin-top: 1.5rem;
}

.space-modern-sm > * + * {
  margin-top: 1rem;
}

.space-modern-lg > * + * {
  margin-top: 2rem;
}

/* Comprehensive text color overrides for better visibility */
/* Override all common text color classes to use black */
p, span, div, h1, h2, h3, h4, h5, h6, label,
.text-sm, .text-xs, .text-base, .text-lg, .text-xl, .text-2xl, .text-3xl,
.font-medium, .font-semibold, .font-bold,
.description, .subtitle, .caption {
  color: var(--color-black) !important;
}

/* Specific overrides for common gray text patterns */
.text-gray-400,
.text-gray-500,
.text-gray-600,
.text-gray-700,
.text-gray-800,
.text-gray-900,
.text-slate-400,
.text-slate-500,
.text-slate-600,
.text-slate-700,
.text-slate-800,
.text-slate-900,
.text-zinc-400,
.text-zinc-500,
.text-zinc-600,
.text-zinc-700,
.text-zinc-800,
.text-zinc-900 {
  color: var(--color-black) !important;
}

/* Override muted text colors */
.text-muted,
.text-muted-foreground,
.muted,
[class*="muted"] {
  color: var(--color-black) !important;
}

/* Override warm gray and other light colors */
.text-brand-warm-gray,
.text-warm-gray,
[class*="warm-gray"] {
  color: var(--color-black) !important;
}

/* Ensure input placeholders are visible but slightly lighter */
input::placeholder,
textarea::placeholder,
select::placeholder {
  color: var(--color-black) !important;
  opacity: 0.7;
}

/* Override any remaining light text colors */
[class*="text-gray"],
[class*="text-slate"],
[class*="text-zinc"],
[class*="text-neutral"] {
  color: var(--color-black) !important;
}

/* Ensure form labels and descriptions are black */
.form-label,
.form-description,
.field-description,
.help-text,
.hint-text {
  color: var(--color-black) !important;
}

/* Override card and component text */
.card-description,
.card-subtitle,
.component-description,
.component-subtitle {
  color: var(--color-black) !important;
}

/* Ensure navigation text is visible */
.nav-description,
.menu-description,
.sidebar-description {
  color: var(--color-black) !important;
}

/* Typography classes */
.font-heading {
  font-family: var(--font-nunito), sans-serif;
  font-weight: 600; /* Semi Bold */
}

.font-subheading {
  font-family: var(--font-nunito), sans-serif;
  font-weight: 500; /* Medium */
}

.font-body {
  font-family: var(--font-nunito), sans-serif;
  font-weight: 400; /* Regular */
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--muted);
}

::-webkit-scrollbar-thumb {
  background: var(--color-pine-green);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--color-dark-green);
}
