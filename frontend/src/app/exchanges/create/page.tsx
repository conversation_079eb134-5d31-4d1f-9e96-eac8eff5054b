'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { 
  ArrowRightLeft, 
  Coins, 
  Heart, 
  Plus,
  X,
  Search,
  MapPin,
  User
} from 'lucide-react';

interface ClothingItem {
  _id: string;
  title: string;
  category: string;
  condition: string;
  images: string[];
  tokenPrice?: number;
}

interface User {
  _id: string;
  name: string;
  profilePicture?: string;
  location: {
    county: string;
    town: string;
  };
}

export default function CreateExchangePage() {
  const router = useRouter();
  const [exchangeType, setExchangeType] = useState<'swap' | 'token_purchase' | 'donation'>('swap');
  const [selectedItems, setSelectedItems] = useState<ClothingItem[]>([]);
  const [targetUser, setTargetUser] = useState<User | null>(null);
  const [userItems, setUserItems] = useState<ClothingItem[]>([]);
  const [searchResults, setSearchResults] = useState<ClothingItem[]>([]);
  const [userSearchResults, setUserSearchResults] = useState<User[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [userSearchTerm, setUserSearchTerm] = useState('');
  const [tokenAmount, setTokenAmount] = useState('');
  const [message, setMessage] = useState('');
  const [deliveryMethod, setDeliveryMethod] = useState<'pickup' | 'delivery' | 'meetup'>('pickup');
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    fetchUserItems();
  }, []);

  useEffect(() => {
    if (searchTerm) {
      searchItems();
    } else {
      setSearchResults([]);
    }
  }, [searchTerm]);

  useEffect(() => {
    if (userSearchTerm) {
      searchUsers();
    } else {
      setUserSearchResults([]);
    }
  }, [userSearchTerm]);

  const fetchUserItems = async () => {
    try {
      const response = await fetch('/api/clothing-items?owner=me&status=available', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setUserItems(data.data || []);
      }
    } catch (error) {
      console.error('Failed to fetch user items:', error);
    }
  };

  const searchItems = async () => {
    try {
      const response = await fetch(`/api/clothing-items/search?q=${encodeURIComponent(searchTerm)}&status=available`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setSearchResults(data.data || []);
      }
    } catch (error) {
      console.error('Failed to search items:', error);
    }
  };

  const searchUsers = async () => {
    try {
      const response = await fetch(`/api/users/search?q=${encodeURIComponent(userSearchTerm)}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setUserSearchResults(data.data || []);
      }
    } catch (error) {
      console.error('Failed to search users:', error);
    }
  };

  const handleItemSelect = (item: ClothingItem) => {
    if (!selectedItems.find(selected => selected._id === item._id)) {
      setSelectedItems([...selectedItems, item]);
    }
    setSearchTerm('');
    setSearchResults([]);
  };

  const handleItemRemove = (itemId: string) => {
    setSelectedItems(selectedItems.filter(item => item._id !== itemId));
  };

  const handleUserSelect = (user: User) => {
    setTargetUser(user);
    setUserSearchTerm('');
    setUserSearchResults([]);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (selectedItems.length === 0) {
      alert('Please select at least one item');
      return;
    }

    if (exchangeType === 'token_purchase' && !tokenAmount) {
      alert('Please enter a token amount');
      return;
    }

    setLoading(true);

    try {
      let endpoint = '';
      let payload: any = {
        deliveryMethod,
        message: message.trim() || undefined
      };

      switch (exchangeType) {
        case 'swap':
          endpoint = '/api/swaps';
          payload = {
            ...payload,
            requestedItems: selectedItems.map(item => item._id),
            targetUserId: targetUser?._id
          };
          break;

        case 'token_purchase':
          endpoint = '/api/token-purchases';
          payload = {
            ...payload,
            itemId: selectedItems[0]._id, // Token purchases are for single items
            offeredAmount: parseInt(tokenAmount)
          };
          break;

        case 'donation':
          endpoint = '/api/donations';
          payload = {
            ...payload,
            items: selectedItems.map(item => item._id),
            charityPartnerId: targetUser?._id // In this case, targetUser would be a charity
          };
          break;
      }

      const response = await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify(payload)
      });

      if (response.ok) {
        router.push('/exchanges');
      } else {
        const error = await response.json();
        alert(error.message || 'Failed to create exchange');
      }
    } catch (error) {
      console.error('Failed to create exchange:', error);
      alert('Failed to create exchange');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container mx-auto px-4 py-8 max-w-4xl">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900">Create Exchange</h1>
        <p className="text-gray-600 mt-2">Start a new clothing exchange, swap, or donation</p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-8">
        {/* Exchange Type Selection */}
        <Card>
          <CardHeader>
            <CardTitle>Exchange Type</CardTitle>
            <CardDescription>Choose the type of exchange you want to create</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div 
                className={`p-4 border-2 rounded-lg cursor-pointer transition-colors ${
                  exchangeType === 'swap' ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-gray-300'
                }`}
                onClick={() => setExchangeType('swap')}
              >
                <div className="flex items-center space-x-3">
                  <ArrowRightLeft className="h-6 w-6 text-blue-500" />
                  <div>
                    <h3 className="font-medium">Swap</h3>
                    <p className="text-sm text-gray-600">Exchange items directly</p>
                  </div>
                </div>
              </div>

              <div 
                className={`p-4 border-2 rounded-lg cursor-pointer transition-colors ${
                  exchangeType === 'token_purchase' ? 'border-yellow-500 bg-yellow-50' : 'border-gray-200 hover:border-gray-300'
                }`}
                onClick={() => setExchangeType('token_purchase')}
              >
                <div className="flex items-center space-x-3">
                  <Coins className="h-6 w-6 text-yellow-500" />
                  <div>
                    <h3 className="font-medium">Token Purchase</h3>
                    <p className="text-sm text-gray-600">Buy with Pedi tokens</p>
                  </div>
                </div>
              </div>

              <div 
                className={`p-4 border-2 rounded-lg cursor-pointer transition-colors ${
                  exchangeType === 'donation' ? 'border-red-500 bg-red-50' : 'border-gray-200 hover:border-gray-300'
                }`}
                onClick={() => setExchangeType('donation')}
              >
                <div className="flex items-center space-x-3">
                  <Heart className="h-6 w-6 text-red-500" />
                  <div>
                    <h3 className="font-medium">Donation</h3>
                    <p className="text-sm text-gray-600">Donate to charity</p>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Item Selection */}
        <Card>
          <CardHeader>
            <CardTitle>
              {exchangeType === 'donation' ? 'Items to Donate' : 
               exchangeType === 'token_purchase' ? 'Item to Purchase' : 'Items to Exchange'}
            </CardTitle>
            <CardDescription>
              {exchangeType === 'donation' ? 'Select items you want to donate' :
               exchangeType === 'token_purchase' ? 'Search for an item to purchase' : 'Select items for the exchange'}
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {exchangeType !== 'token_purchase' ? (
              // For swaps and donations, select from user's items
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {userItems.map((item) => (
                  <div
                    key={item._id}
                    className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                      selectedItems.find(selected => selected._id === item._id)
                        ? 'border-green-500 bg-green-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                    onClick={() => {
                      if (selectedItems.find(selected => selected._id === item._id)) {
                        handleItemRemove(item._id);
                      } else {
                        setSelectedItems([...selectedItems, item]);
                      }
                    }}
                  >
                    <img
                      src={item.images[0] || '/placeholder-image.jpg'}
                      alt={item.title}
                      className="w-full h-32 object-cover rounded-md mb-2"
                    />
                    <h4 className="font-medium text-sm">{item.title}</h4>
                    <p className="text-xs text-gray-600">{item.category} • {item.condition}</p>
                  </div>
                ))}
              </div>
            ) : (
              // For token purchases, search for items
              <div className="space-y-4">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="Search for items to purchase..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>

                {searchResults.length > 0 && (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 max-h-64 overflow-y-auto">
                    {searchResults.map((item) => (
                      <div
                        key={item._id}
                        className="p-4 border rounded-lg cursor-pointer hover:border-gray-300"
                        onClick={() => handleItemSelect(item)}
                      >
                        <img
                          src={item.images[0] || '/placeholder-image.jpg'}
                          alt={item.title}
                          className="w-full h-32 object-cover rounded-md mb-2"
                        />
                        <h4 className="font-medium text-sm">{item.title}</h4>
                        <p className="text-xs text-gray-600">{item.category} • {item.condition}</p>
                        {item.tokenPrice && (
                          <p className="text-xs text-green-600 font-medium">{item.tokenPrice} tokens</p>
                        )}
                      </div>
                    ))}
                  </div>
                )}

                {selectedItems.length > 0 && (
                  <div className="space-y-2">
                    <Label>Selected Item:</Label>
                    <div className="flex items-center space-x-2">
                      <Badge variant="outline">{selectedItems[0].title}</Badge>
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => handleItemRemove(selectedItems[0]._id)}
                      >
                        <X className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>
                )}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Token Amount (for token purchases) */}
        {exchangeType === 'token_purchase' && (
          <Card>
            <CardHeader>
              <CardTitle>Token Amount</CardTitle>
              <CardDescription>Enter the amount of tokens you want to offer</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <Label htmlFor="tokenAmount">Token Amount</Label>
                <Input
                  id="tokenAmount"
                  type="number"
                  min="1"
                  value={tokenAmount}
                  onChange={(e) => setTokenAmount(e.target.value)}
                  placeholder="Enter token amount"
                />
              </div>
            </CardContent>
          </Card>
        )}

        {/* Delivery Method */}
        <Card>
          <CardHeader>
            <CardTitle>Delivery Method</CardTitle>
            <CardDescription>Choose how you want to handle the exchange</CardDescription>
          </CardHeader>
          <CardContent>
            <Select value={deliveryMethod} onValueChange={(value: any) => setDeliveryMethod(value)}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="pickup">Pickup</SelectItem>
                <SelectItem value="delivery">Delivery</SelectItem>
                <SelectItem value="meetup">Meetup</SelectItem>
              </SelectContent>
            </Select>
          </CardContent>
        </Card>

        {/* Message */}
        <Card>
          <CardHeader>
            <CardTitle>Message (Optional)</CardTitle>
            <CardDescription>Add a personal message to your exchange request</CardDescription>
          </CardHeader>
          <CardContent>
            <Textarea
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              placeholder="Add a message..."
              rows={3}
            />
          </CardContent>
        </Card>

        {/* Submit Button */}
        <div className="flex justify-end space-x-4">
          <Button
            type="button"
            variant="outline"
            onClick={() => router.back()}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            disabled={loading || selectedItems.length === 0}
            className="bg-green-600 hover:bg-green-700"
          >
            {loading ? 'Creating...' : 'Create Exchange'}
          </Button>
        </div>
      </form>
    </div>
  );
}
