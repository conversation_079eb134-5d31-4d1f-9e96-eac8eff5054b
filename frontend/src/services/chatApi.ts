import { apiClient } from './api';

export interface ChatParticipant {
  id: string;
  firstName: string;
  lastName: string;
  profilePicture?: string;
  isOnline?: boolean;
  lastSeen?: string;
}

export interface ChatMessage {
  id: string;
  sender: ChatParticipant;
  content: string;
  type: 'text' | 'image' | 'system';
  timestamp: string;
  isRead: boolean;
  deliveryStatus?: 'sending' | 'sent' | 'delivered' | 'failed';
}

export interface ChatConversation {
  id: string;
  participants: ChatParticipant[];
  lastMessage?: {
    content: string;
    sender: string;
    timestamp: string;
  };
  unreadCount: number;
  relatedClothingItem?: {
    id: string;
    title: string;
    images: string[];
  };
  updatedAt: string;
  messages?: ChatMessage[];
}

export interface CreateConversationRequest {
  participantId: string;
  clothingItemId?: string;
  initialMessage?: string;
}

export interface SendMessageRequest {
  content: string;
  type?: 'text' | 'image';
}

export interface GetMessagesParams {
  page?: number;
  limit?: number;
  before?: string;
}

export interface MarkMessagesReadRequest {
  messageIds?: string[];
}

class ChatApiService {
  // Get user's conversations
  async getConversations(params?: {
    page?: number;
    limit?: number;
    search?: string;
  }): Promise<{
    conversations: ChatConversation[];
    pagination: {
      page: number;
      limit: number;
      totalPages: number;
      totalCount: number;
      hasNext: boolean;
      hasPrev: boolean;
    };
  }> {
    const response = await apiClient.get('/chat/conversations', { params });
    return response.data.data;
  }

  // Create a new conversation
  async createConversation(data: CreateConversationRequest): Promise<ChatConversation> {
    const response = await apiClient.post('/chat/conversations', data);
    return response.data.data;
  }

  // Get messages for a conversation
  async getMessages(
    conversationId: string,
    params?: GetMessagesParams
  ): Promise<{
    messages: ChatMessage[];
    pagination: {
      page: number;
      limit: number;
      totalPages: number;
      totalCount: number;
      hasNext: boolean;
      hasPrev: boolean;
    };
  }> {
    const response = await apiClient.get(`/chat/conversations/${conversationId}/messages`, {
      params
    });
    return response.data.data;
  }

  // Send a message
  async sendMessage(
    conversationId: string,
    data: SendMessageRequest
  ): Promise<ChatMessage> {
    const response = await apiClient.post(`/chat/conversations/${conversationId}/messages`, data);
    return response.data.data;
  }

  // Mark messages as read
  async markMessagesAsRead(
    conversationId: string,
    data?: MarkMessagesReadRequest
  ): Promise<void> {
    await apiClient.patch(`/chat/conversations/${conversationId}/messages/read`, data);
  }

  // Delete conversation
  async deleteConversation(conversationId: string): Promise<void> {
    await apiClient.delete(`/chat/conversations/${conversationId}`);
  }

  // Upload image for chat
  async uploadChatImage(file: File): Promise<string> {
    const formData = new FormData();
    formData.append('image', file);

    const response = await apiClient.post('/chat/upload-image', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });

    return response.data.data.imageUrl;
  }
}

export const chatApi = new ChatApiService();
