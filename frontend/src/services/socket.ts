import { io, Socket } from 'socket.io-client';

interface SocketEvents {
  // Connection events
  connect: () => void;
  disconnect: () => void;
  connect_error: (error: Error) => void;
  
  // Chat events
  'new-message': (data: { chatId: string; message: any }) => void;
  'user:new-message': (data: { chatId: string; message: any; from: string }) => void;
  'user:new-conversation': (conversation: any) => void;
  'user:conversation-deleted': (data: { chatId: string; deletedBy: string }) => void;
  'messages-read': (data: { readBy: string; messageIds: string[] | 'all' }) => void;
  
  // Presence events
  'user-online': (data: { userId: string; user: any }) => void;
  'user-offline': (data: { userId: string; lastSeen: string }) => void;
  'online-users-list': (users: any[]) => void;
  
  // Chat room events
  'user-joined-chat': (data: { chatId: string; user: any }) => void;
  'user-left-chat': (data: { chatId: string; userId: string }) => void;
  
  // Typing events
  'user-typing': (data: { chatId: string; userId: string; user: any }) => void;
  'user-stopped-typing': (data: { chatId: string; userId: string }) => void;
  
  // Message delivery
  'message-delivery-confirmed': (data: { chatId: string; messageId: string; deliveredTo: string }) => void;
}

class SocketService {
  private socket: Socket | null = null;
  private token: string | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000;
  private eventListeners: Map<string, Function[]> = new Map();

  constructor() {
    this.setupEventListeners();
  }

  connect(token: string): Promise<void> {
    return new Promise((resolve, reject) => {
      if (this.socket?.connected) {
        resolve();
        return;
      }

      this.token = token;
      const serverUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000';

      this.socket = io(serverUrl, {
        auth: {
          token: token
        },
        transports: ['websocket', 'polling'],
        timeout: 10000,
        forceNew: true
      });

      this.socket.on('connect', () => {
        console.log('Socket connected:', this.socket?.id);
        this.reconnectAttempts = 0;
        resolve();
      });

      this.socket.on('connect_error', (error) => {
        console.error('Socket connection error:', error);
        this.handleReconnect();
        reject(error);
      });

      this.socket.on('disconnect', (reason) => {
        console.log('Socket disconnected:', reason);
        if (reason === 'io server disconnect') {
          // Server disconnected, try to reconnect
          this.handleReconnect();
        }
      });

      // Set up all event listeners
      this.setupSocketEventListeners();
    });
  }

  disconnect(): void {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }
    this.token = null;
    this.reconnectAttempts = 0;
  }

  private handleReconnect(): void {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('Max reconnection attempts reached');
      return;
    }

    this.reconnectAttempts++;
    const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);

    setTimeout(() => {
      if (this.token) {
        console.log(`Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
        this.connect(this.token).catch(console.error);
      }
    }, delay);
  }

  private setupEventListeners(): void {
    // Initialize event listener maps
    Object.keys({} as SocketEvents).forEach(event => {
      this.eventListeners.set(event, []);
    });
  }

  private setupSocketEventListeners(): void {
    if (!this.socket) return;

    // Set up listeners for all socket events
    this.eventListeners.forEach((listeners, event) => {
      this.socket?.on(event, (...args) => {
        listeners.forEach(listener => listener(...args));
      });
    });
  }

  // Event listener management
  on<K extends keyof SocketEvents>(event: K, listener: SocketEvents[K]): void {
    const listeners = this.eventListeners.get(event) || [];
    listeners.push(listener);
    this.eventListeners.set(event, listeners);
  }

  off<K extends keyof SocketEvents>(event: K, listener: SocketEvents[K]): void {
    const listeners = this.eventListeners.get(event) || [];
    const index = listeners.indexOf(listener);
    if (index > -1) {
      listeners.splice(index, 1);
      this.eventListeners.set(event, listeners);
    }
  }

  // Chat operations
  joinChat(chatId: string): void {
    this.socket?.emit('join-chat', chatId);
  }

  leaveChat(chatId: string): void {
    this.socket?.emit('leave-chat', chatId);
  }

  sendTypingStart(chatId: string): void {
    this.socket?.emit('typing-start', { chatId });
  }

  sendTypingStop(chatId: string): void {
    this.socket?.emit('typing-stop', { chatId });
  }

  markMessageDelivered(chatId: string, messageId: string): void {
    this.socket?.emit('message-delivered', { chatId, messageId });
  }

  updatePresence(): void {
    this.socket?.emit('update-presence');
  }

  getOnlineUsers(): void {
    this.socket?.emit('get-online-users');
  }

  // Connection status
  isConnected(): boolean {
    return this.socket?.connected || false;
  }

  getSocketId(): string | undefined {
    return this.socket?.id;
  }
}

// Create singleton instance
const socketService = new SocketService();

export default socketService;
