{"name": "pedi-monorepo", "version": "1.0.0", "description": "Pedi: The Future of Fashion is Circular - Sustainable clothing exchange platform for Kenya", "private": true, "scripts": {"dev": "concurrently \"pnpm run dev:backend\" \"pnpm run dev:frontend\"", "dev:frontend": "pnpm --filter frontend dev", "dev:backend": "pnpm --filter pedi-backend dev", "build": "pnpm run build:shared && pnpm run build:backend && pnpm run build:frontend", "build:frontend": "pnpm --filter frontend build", "build:backend": "pnpm --filter pedi-backend build", "build:shared": "pnpm --filter shared build", "test": "pnpm run test:backend && pnpm run test:frontend", "test:frontend": "pnpm --filter frontend test", "test:backend": "pnpm --filter pedi-backend test", "lint": "pnpm run lint:frontend && pnpm run lint:backend", "lint:frontend": "pnpm --filter frontend lint", "lint:backend": "pnpm --filter pedi-backend lint", "clean": "rm -rf node_modules frontend/node_modules backend/node_modules shared/node_modules", "install:all": "pnpm install"}, "devDependencies": {"concurrently": "^8.2.2", "@types/node": "^20.10.0", "typescript": "^5.3.0"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0", "pnpm": ">=10.0.0"}, "keywords": ["sustainable-fashion", "circular-economy", "clothing-exchange", "kenya", "web2", "nextjs", "nodejs", "mongodb"], "author": "Pedi Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/Ivy-NW/pedi.git"}, "bugs": {"url": "https://github.com/Ivy-NW/pedi/issues"}, "homepage": "https://github.com/Ivy-NW/pedi#readme"}