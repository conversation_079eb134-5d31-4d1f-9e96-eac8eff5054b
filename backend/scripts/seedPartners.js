const mongoose = require('mongoose');
const CharityPartner = require('../models/CharityPartner');
const RecyclingCenter = require('../models/RecyclingCenter');
require('dotenv').config();

const charityPartners = [
  {
    name: 'Hope Children\'s Home',
    description: 'Providing care and education for orphaned and vulnerable children in Nairobi.',
    mission: 'To give every child a chance at a better future through love, care, and education.',
    location: {
      county: 'Nairobi',
      town: 'Nairobi',
      address: 'Kibera, Nairobi'
    },
    contactInfo: {
      phone: '+254712345001',
      email: '<EMAIL>',
      website: 'https://hopechildrenshome.org'
    },
    acceptedItems: [
      'Children\'s Clothing',
      'School Uniforms',
      'Shoes',
      'Blankets',
      'Toys'
    ],
    rewardRate: 12,
    categories: ['children', 'education'],
    registrationNumber: 'CH001/2020',
    isVerified: true,
    isActive: true,
    capacity: {
      maxWeightPerDonation: 50,
      monthlyCapacity: 500
    }
  },
  {
    name: 'Elderly Care Foundation',
    description: 'Supporting elderly citizens with clothing and basic necessities.',
    mission: 'Ensuring dignity and comfort for our elderly community members.',
    location: {
      county: 'Kiambu',
      town: 'Thika',
      address: 'Thika Town, Kiambu County'
    },
    contactInfo: {
      phone: '+254712345002',
      email: '<EMAIL>',
      website: 'https://elderlycare.ke'
    },
    acceptedItems: [
      'Adult Clothing',
      'Warm Clothing',
      'Shoes',
      'Blankets',
      'Walking Aids'
    ],
    rewardRate: 10,
    categories: ['elderly', 'healthcare'],
    registrationNumber: 'EC002/2019',
    isVerified: true,
    isActive: true,
    capacity: {
      maxWeightPerDonation: 75,
      monthlyCapacity: 800
    }
  },
  {
    name: 'Women Empowerment Center',
    description: 'Empowering women through skills training and support programs.',
    mission: 'Creating opportunities for women to achieve economic independence.',
    location: {
      county: 'Mombasa',
      town: 'Mombasa',
      address: 'Old Town, Mombasa'
    },
    contactInfo: {
      phone: '+254712345003',
      email: '<EMAIL>',
      website: 'https://womenempowerment.org'
    },
    acceptedItems: [
      'Professional Clothing',
      'Casual Wear',
      'Shoes',
      'Accessories',
      'Handbags'
    ],
    rewardRate: 15,
    categories: ['women_empowerment', 'education'],
    registrationNumber: 'WE003/2021',
    isVerified: true,
    isActive: true,
    capacity: {
      maxWeightPerDonation: 60,
      monthlyCapacity: 600
    }
  },
  {
    name: 'Disaster Relief Kenya',
    description: 'Providing emergency clothing and supplies during disasters.',
    mission: 'Rapid response to communities affected by natural disasters.',
    location: {
      county: 'Nakuru',
      town: 'Nakuru',
      address: 'Nakuru Town, Nakuru County'
    },
    contactInfo: {
      phone: '+254712345004',
      email: '<EMAIL>',
      website: 'https://disasterrelief.ke'
    },
    acceptedItems: [
      'All Clothing Types',
      'Blankets',
      'Shoes',
      'Rain Gear',
      'Emergency Supplies'
    ],
    rewardRate: 8,
    categories: ['disaster_relief', 'community_development'],
    registrationNumber: 'DR004/2018',
    isVerified: true,
    isActive: true,
    capacity: {
      maxWeightPerDonation: 100,
      monthlyCapacity: 2000
    }
  }
];

const recyclingCenters = [
  {
    name: 'EcoTextile Recycling',
    description: 'Advanced textile recycling facility specializing in cotton and polyester.',
    location: {
      county: 'Nairobi',
      town: 'Industrial Area',
      address: 'Industrial Area, Nairobi'
    },
    contactInfo: {
      phone: '+254712345101',
      email: '<EMAIL>',
      website: 'https://ecotextile.ke'
    },
    specialties: ['cotton', 'polyester', 'mixed_materials'],
    acceptedItems: [
      'Cotton Clothing',
      'Polyester Items',
      'Mixed Fabric Clothing',
      'Bed Sheets',
      'Towels'
    ],
    rewardRate: 8,
    processingMethods: ['mechanical_recycling', 'fiber_recovery'],
    licenseNumber: 'RC001/2020',
    isVerified: true,
    isActive: true,
    capacity: {
      maxWeightPerDelivery: 100,
      monthlyCapacity: 2000
    },
    environmentalImpact: {
      co2ReductionRate: 3.2,
      waterSavingRate: 2800,
      energySavingRate: 0.6
    }
  },
  {
    name: 'Green Fiber Solutions',
    description: 'Sustainable textile processing with focus on natural fibers.',
    location: {
      county: 'Kiambu',
      town: 'Ruiru',
      address: 'Ruiru Industrial Park, Kiambu'
    },
    contactInfo: {
      phone: '+254712345102',
      email: '<EMAIL>',
      website: 'https://greenfiber.co.ke'
    },
    specialties: ['cotton', 'wool', 'silk', 'all_textiles'],
    acceptedItems: [
      'Natural Fiber Clothing',
      'Wool Items',
      'Silk Garments',
      'Cotton Products',
      'Linen Items'
    ],
    rewardRate: 10,
    processingMethods: ['mechanical_recycling', 'upcycling', 'fiber_recovery'],
    licenseNumber: 'RC002/2019',
    isVerified: true,
    isActive: true,
    capacity: {
      maxWeightPerDelivery: 80,
      monthlyCapacity: 1500
    },
    environmentalImpact: {
      co2ReductionRate: 3.5,
      waterSavingRate: 3000,
      energySavingRate: 0.7
    }
  },
  {
    name: 'Urban Textile Recovery',
    description: 'City-based textile recovery and processing center.',
    location: {
      county: 'Mombasa',
      town: 'Changamwe',
      address: 'Changamwe Industrial Area, Mombasa'
    },
    contactInfo: {
      phone: '+254712345103',
      email: '<EMAIL>',
      website: 'https://urbantextile.ke'
    },
    specialties: ['synthetic_fabrics', 'denim', 'mixed_materials'],
    acceptedItems: [
      'Synthetic Clothing',
      'Denim Items',
      'Sportswear',
      'Mixed Fabric Items',
      'Accessories'
    ],
    rewardRate: 7,
    processingMethods: ['chemical_recycling', 'downcycling', 'energy_recovery'],
    licenseNumber: 'RC003/2021',
    isVerified: true,
    isActive: true,
    capacity: {
      maxWeightPerDelivery: 120,
      monthlyCapacity: 2500
    },
    environmentalImpact: {
      co2ReductionRate: 2.8,
      waterSavingRate: 2500,
      energySavingRate: 0.5
    }
  }
];

const seedPartners = async () => {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('Connected to MongoDB');

    // Clear existing data
    await CharityPartner.deleteMany({});
    await RecyclingCenter.deleteMany({});
    console.log('Cleared existing partner data');

    // Insert charity partners
    const createdCharities = await CharityPartner.insertMany(charityPartners);
    console.log(`Created ${createdCharities.length} charity partners`);

    // Insert recycling centers
    const createdCenters = await RecyclingCenter.insertMany(recyclingCenters);
    console.log(`Created ${createdCenters.length} recycling centers`);

    console.log('Partner seeding completed successfully!');
    
  } catch (error) {
    console.error('Error seeding partners:', error);
  } finally {
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
  }
};

// Run the script
seedPartners();

module.exports = seedPartners;
