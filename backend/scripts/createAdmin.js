const mongoose = require('mongoose');
const User = require('../src/models/User');
require('dotenv').config();

const createAdminUser = async () => {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('Connected to MongoDB');

    // Check if admin already exists
    const existingAdmin = await User.findOne({ 
      $or: [
        { email: process.env.ADMIN_EMAIL },
        { phoneNumber: process.env.ADMIN_PHONE }
      ]
    });

    if (existingAdmin) {
      console.log('Admin user already exists:', existingAdmin.email || existingAdmin.phoneNumber);
      
      // Update existing admin with latest credentials
      existingAdmin.role = 'admin';
      existingAdmin.isVerified = true;
      existingAdmin.isActive = true;
      existingAdmin.firstName = process.env.ADMIN_FIRST_NAME || 'Pedi';
      existingAdmin.lastName = process.env.ADMIN_LAST_NAME || 'Administrator';
      existingAdmin.email = process.env.ADMIN_EMAIL;
      existingAdmin.phoneNumber = process.env.ADMIN_PHONE;
      existingAdmin.pediTokens = 10000; // Give admin plenty of tokens
      
      await existingAdmin.save();
      console.log('Admin user updated successfully');
    } else {
      // Create new admin user
      const adminUser = new User({
        phoneNumber: process.env.ADMIN_PHONE || '+254700000000',
        email: process.env.ADMIN_EMAIL || '<EMAIL>',
        firstName: process.env.ADMIN_FIRST_NAME || 'Pedi',
        lastName: process.env.ADMIN_LAST_NAME || 'Administrator',
        location: {
          county: 'Nairobi',
          town: 'Nairobi'
        },
        isVerified: true,
        isActive: true,
        role: 'admin',
        pediTokens: 10000,
        totalTokensEarned: 10000,
        sustainabilityScore: 1000,
        profileCompletionPercentage: 100,
        preferences: {
          sizes: ['S', 'M', 'L'],
          categories: ['Tops', 'Bottoms', 'Dresses', 'Outerwear'],
          brands: [],
          priceRange: { min: 0, max: 50000 }
        }
      });

      await adminUser.save();
      console.log('Admin user created successfully:', adminUser.email);
    }

    // Check if super admin exists and create if needed
    if (process.env.SUPER_ADMIN_EMAIL && process.env.SUPER_ADMIN_PHONE) {
      const existingSuperAdmin = await User.findOne({ 
        $or: [
          { email: process.env.SUPER_ADMIN_EMAIL },
          { phoneNumber: process.env.SUPER_ADMIN_PHONE }
        ]
      });

      if (!existingSuperAdmin) {
        const superAdminUser = new User({
          phoneNumber: process.env.SUPER_ADMIN_PHONE,
          email: process.env.SUPER_ADMIN_EMAIL,
          firstName: 'Super',
          lastName: 'Administrator',
          location: {
            county: 'Nairobi',
            town: 'Nairobi'
          },
          isVerified: true,
          isActive: true,
          role: 'admin',
          pediTokens: 50000,
          totalTokensEarned: 50000,
          sustainabilityScore: 1000,
          profileCompletionPercentage: 100,
          preferences: {
            sizes: ['S', 'M', 'L', 'XL'],
            categories: ['Tops', 'Bottoms', 'Dresses', 'Outerwear', 'Shoes', 'Accessories'],
            brands: [],
            priceRange: { min: 0, max: 100000 }
          }
        });

        await superAdminUser.save();
        console.log('Super admin user created successfully:', superAdminUser.email);
      } else {
        console.log('Super admin user already exists:', existingSuperAdmin.email);
      }
    }

    console.log('Admin setup completed successfully!');
    
  } catch (error) {
    console.error('Error creating admin user:', error);
  } finally {
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
  }
};

// Run the script
createAdminUser();

module.exports = createAdminUser;
