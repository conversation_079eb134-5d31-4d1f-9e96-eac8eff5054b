{"name": "pedi-backend-setup", "version": "1.0.0", "description": "Setup scripts for Pedi backend enhanced features", "scripts": {"setup:admin": "node scripts/createAdmin.js", "setup:partners": "node scripts/seedPartners.js", "setup:all": "npm run setup:admin && npm run setup:partners", "dev:enhanced": "npm run setup:all && npm run dev", "start:fresh": "npm run setup:all && npm start"}, "dependencies": {"mongoose": "^7.0.0", "dotenv": "^16.0.0"}}