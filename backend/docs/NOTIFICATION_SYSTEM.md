# Pedi Notification System

## Overview

The Pedi notification system provides comprehensive SMS notification functionality using Africa's Talking API. It supports various notification types including payment confirmations, exchange updates, token earnings, system alerts, and promotional messages.

## Architecture

### Core Components

1. **Notification Service** (`/services/notification.ts`)
   - Main notification management service
   - Handles creation, sending, delivery tracking, and statistics

2. **Notification Integration Service** (`/services/notificationIntegration.ts`)
   - Platform event integration helpers
   - Provides convenient methods for common notification scenarios

3. **SMS Service** (`/services/sms.ts`)
   - Enhanced Africa's Talking integration
   - Bulk messaging and delivery tracking

4. **Database Models**
   - `Notification`: Individual notification records
   - `NotificationTemplate`: Reusable message templates
   - `NotificationPreferences`: User notification preferences

### Database Models

#### Notification Model
```typescript
{
  userId: ObjectId,           // Recipient user
  category: string,           // payment, exchange, token, etc.
  type: string,              // sms, push, email, in-app
  title: string,             // Notification title
  message: string,           // Notification content
  priority: string,          // low, medium, high, urgent
  status: string,            // pending, sent, delivered, failed
  scheduledFor: Date,        // When to send (optional)
  sentAt: Date,              // When actually sent
  deliveredAt: Date,         // When delivery confirmed
  templateId: ObjectId,      // Template used (optional)
  templateVariables: Object, // Variables for template
  relatedEntity: {           // Related platform entity
    type: string,
    id: string
  },
  deliveryAttempts: Number,  // Retry count
  maxAttempts: Number,       // Max retry limit
  metadata: Object           // Additional data
}
```

#### NotificationTemplate Model
```typescript
{
  name: string,              // Unique template name
  description: string,       // Template description
  category: string,          // Notification category
  type: string,             // Channel type
  title: string,            // Template title
  message: string,          // Template with {{variables}}
  variables: [{             // Required/optional variables
    name: string,
    type: string,
    required: boolean,
    description: string,
    defaultValue: any
  }],
  priority: string,         // Default priority
  isActive: boolean,        // Template enabled
  usageCount: number,       // Times used
  maxUsagePerDay: number,   // Daily limit
  createdBy: ObjectId       // Creator
}
```

#### NotificationPreferences Model
```typescript
{
  userId: ObjectId,         // User ID
  channels: {               // Channel preferences
    sms: boolean,
    push: boolean,
    email: boolean,
    inApp: boolean
  },
  categories: {             // Category preferences
    payment: boolean,
    exchange: boolean,
    token: boolean,
    system: boolean,
    promotion: boolean,
    reminder: boolean,
    welcome: boolean,
    security: boolean
  },
  quietHours: {             // Do not disturb
    enabled: boolean,
    startTime: string,      // "22:00"
    endTime: string,        // "08:00"
    timezone: string
  },
  frequency: {              // Frequency limits
    maxPerDay: number,
    maxPerWeek: number,
    cooldownMinutes: number
  },
  language: string          // Preferred language
}
```

## API Endpoints

### Notification Management
- `POST /api/notifications` - Create notification
- `GET /api/notifications` - Get user notifications
- `GET /api/notifications/:id` - Get notification details
- `PATCH /api/notifications/:id/read` - Mark as read
- `DELETE /api/notifications/:id` - Delete notification

### User Preferences
- `GET /api/notifications/preferences` - Get user preferences
- `PUT /api/notifications/preferences` - Update preferences
- `POST /api/notifications/preferences/reset` - Reset to defaults

### Templates (Admin)
- `GET /api/notifications/templates` - List templates
- `POST /api/notifications/templates` - Create template
- `PUT /api/notifications/templates/:id` - Update template
- `DELETE /api/notifications/templates/:id` - Delete template

### Statistics & Monitoring
- `GET /api/notifications/stats` - Get notification statistics
- `GET /api/notifications/health` - Service health check

### Webhooks
- `POST /api/notifications/webhook/delivery` - Africa's Talking delivery reports

## Integration Points

### Payment System
- Payment confirmations (success/failure)
- Integrated in `PaymentService.processCallback()`

### Authentication
- Welcome messages for new users
- Integrated in `auth.ts` verification endpoint

### Token System
- Token earning notifications
- Integrated in `TokenService.earnTokens()`

### Exchange System
- Exchange request notifications
- Exchange status updates (accepted/completed)
- Integrated in `ExchangeService` and `SwapService`

## Notification Categories

### Payment Notifications
- Payment confirmations
- Payment failures
- Refund notifications

### Exchange Notifications
- New exchange requests
- Exchange accepted/declined
- Exchange completed
- Pickup/delivery reminders

### Token Notifications
- Tokens earned
- Token balance updates
- Achievement unlocks

### System Notifications
- Maintenance alerts
- Security notifications
- App updates

### Promotional Notifications
- Special offers
- Feature announcements
- Seasonal campaigns

### Reminder Notifications
- Pickup reminders
- Verification reminders
- Inactive user re-engagement

## Usage Examples

### Basic Notification
```typescript
import { notificationService } from '@/services/notification';

await notificationService.createNotification({
  userId: 'user123',
  category: 'payment',
  title: 'Payment Confirmed',
  message: 'Your payment of KES 500 has been confirmed.',
  priority: 'high'
});
```

### Using Integration Helpers
```typescript
import { notificationIntegrationService } from '@/services/notificationIntegration';

// Payment confirmation
await notificationIntegrationService.sendPaymentConfirmation(payment);

// Welcome message
await notificationIntegrationService.sendWelcomeNotification(userId, firstName);

// Token earned
await notificationIntegrationService.sendTokenEarned(userId, amount, reason);
```

### Bulk Notifications
```typescript
await notificationIntegrationService.sendPromotionalNotification(
  userIds,
  'Weekend Special',
  'Get 20% extra tokens on all exchanges this weekend!'
);
```

## Configuration

### Environment Variables
```bash
# Africa's Talking Configuration
AFRICAS_TALKING_USERNAME=your_username
AFRICAS_TALKING_API_KEY=your_api_key
AFRICAS_TALKING_SENDER_ID=your_sender_id

# Notification Settings
NOTIFICATION_MAX_RETRIES=3
NOTIFICATION_RETRY_DELAY=300000  # 5 minutes
NOTIFICATION_BATCH_SIZE=100
```

### Default Settings
- Max retries: 3 attempts
- Retry delay: 5 minutes (exponential backoff)
- Batch size: 100 notifications per batch
- Default quiet hours: 22:00 - 08:00 EAT

## Testing

### Run Notification Tests
```bash
# Test notification integration
npm run test:notifications

# Or run the test script directly
npx ts-node src/scripts/testNotificationIntegration.ts
```

### Seed Notification Templates
```bash
npx ts-node src/scripts/seedNotificationTemplates.ts
```

## Monitoring & Analytics

### Service Health
The system provides health monitoring endpoints:
- SMS service connectivity
- Database connectivity
- Pending notification count
- Failed notification rate

### Statistics
Track notification performance:
- Delivery rates by category
- User engagement metrics
- Template usage statistics
- Error rates and patterns

## Best Practices

1. **Use Templates**: Create reusable templates for common notifications
2. **Respect Preferences**: Always check user preferences before sending
3. **Handle Failures**: Implement proper error handling and retry logic
4. **Monitor Performance**: Track delivery rates and user engagement
5. **Test Thoroughly**: Use test scripts to verify integration points

## Troubleshooting

### Common Issues
1. **SMS Not Sending**: Check Africa's Talking credentials and balance
2. **High Failure Rate**: Verify phone number formatting (+254 format)
3. **Delivery Delays**: Check network connectivity and API limits
4. **Template Errors**: Validate template variables and syntax

### Debug Mode
Enable detailed logging by setting `LOG_LEVEL=debug` in environment variables.

## Future Enhancements

- Push notification support (Firebase)
- Email notification integration
- In-app notification system
- Advanced analytics dashboard
- A/B testing for notification content
- Multi-language template support
