const express = require('express');
const router = express.Router();
const BulkDonation = require('../models/BulkDonation');
const User = require('../src/models/User').default;
const CharityPartner = require('../src/models/CharityPartner').default;
const RecyclingCenter = require('../models/RecyclingCenter');
const { authenticate: auth } = require('../src/middleware/auth');
const { body, validationResult } = require('express-validator');

// Create bulk donation
router.post('/', [
  auth,
  body('type').isIn(['charity', 'recycling']).withMessage('Type must be charity or recycling'),
  body('weight').isFloat({ min: 0.1, max: 100 }).withMessage('Weight must be between 0.1 and 100 kg'),
  body('condition').isIn(['excellent', 'good', 'fair', 'poor']).withMessage('Invalid condition'),
  body('partnerId').isMongoId().withMessage('Invalid partner ID'),
  body('clothingTypes').isArray({ min: 1 }).withMessage('At least one clothing type required'),
  body('deliveryMethod').isIn(['pickup', 'drop_off']).withMessage('Invalid delivery method'),
  body('contactInfo.phone').isMobilePhone().withMessage('Valid phone number required'),
  body('contactInfo.address').isLength({ min: 10 }).withMessage('Address must be at least 10 characters')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation errors',
        errors: errors.array()
      });
    }

    const {
      type,
      weight,
      description,
      clothingTypes,
      condition,
      partnerId,
      deliveryMethod,
      contactInfo,
      isAnonymous
    } = req.body;

    // Verify partner exists
    const PartnerModel = type === 'charity' ? CharityPartner : RecyclingCenter;
    const partner = await PartnerModel.findById(partnerId);
    if (!partner) {
      return res.status(404).json({
        success: false,
        message: `${type === 'charity' ? 'Charity partner' : 'Recycling center'} not found`
      });
    }

    // Calculate tokens based on weight, condition, and partner rate
    const conditionMultipliers = {
      excellent: 1.2,
      good: 1.0,
      fair: 0.8,
      poor: 0.5
    };

    const baseTokens = weight * partner.rewardRate;
    const tokensEarned = Math.round(baseTokens * conditionMultipliers[condition]);

    // Create bulk donation
    const bulkDonation = new BulkDonation({
      user: req.user.id,
      type,
      weight,
      description,
      clothingTypes,
      condition,
      partner: partnerId,
      deliveryMethod,
      contactInfo,
      isAnonymous,
      tokensEarned,
      status: 'pending',
      estimatedPickupDate: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000) // 3 days from now
    });

    await bulkDonation.save();

    // Update user's pending tokens (will be awarded when donation is completed)
    await User.findByIdAndUpdate(req.user.id, {
      $inc: { pendingTokens: tokensEarned }
    });

    // Populate partner info for response
    await bulkDonation.populate('partner', 'name location');

    res.status(201).json({
      success: true,
      message: 'Bulk donation submitted successfully',
      data: bulkDonation
    });

  } catch (error) {
    console.error('Error creating bulk donation:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// Get user's donations
router.get('/my-donations', auth, async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    const donations = await BulkDonation.find({ user: req.user.id })
      .populate('partner', 'name location type')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit);

    const total = await BulkDonation.countDocuments({ user: req.user.id });

    res.json({
      success: true,
      data: donations,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });

  } catch (error) {
    console.error('Error fetching user donations:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// Get donation statistics for user
router.get('/stats', auth, async (req, res) => {
  try {
    const userId = req.user.id;

    // Get donation stats
    const donationStats = await BulkDonation.aggregate([
      { $match: { user: userId } },
      {
        $group: {
          _id: null,
          totalWeight: { $sum: '$weight' },
          totalDonations: { $sum: 1 },
          tokensEarned: { $sum: '$tokensEarned' },
          charityDonations: {
            $sum: { $cond: [{ $eq: ['$type', 'charity'] }, 1, 0] }
          },
          recyclingDonations: {
            $sum: { $cond: [{ $eq: ['$type', 'recycling'] }, 1, 0] }
          }
        }
      }
    ]);

    const stats = donationStats[0] || {
      totalWeight: 0,
      totalDonations: 0,
      tokensEarned: 0,
      charityDonations: 0,
      recyclingDonations: 0
    };

    // Calculate impact score and rank
    const impactScore = Math.round(stats.totalWeight * 10 + stats.tokensEarned * 0.5);
    let rank = 'Bronze';
    if (impactScore >= 500) rank = 'Platinum';
    else if (impactScore >= 200) rank = 'Gold';
    else if (impactScore >= 50) rank = 'Silver';

    res.json({
      success: true,
      data: {
        ...stats,
        impactScore,
        rank
      }
    });

  } catch (error) {
    console.error('Error fetching donation stats:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// Get single donation details
router.get('/:id', auth, async (req, res) => {
  try {
    const donation = await BulkDonation.findOne({
      _id: req.params.id,
      user: req.user.id
    }).populate('partner', 'name location type description');

    if (!donation) {
      return res.status(404).json({
        success: false,
        message: 'Donation not found'
      });
    }

    res.json({
      success: true,
      data: donation
    });

  } catch (error) {
    console.error('Error fetching donation:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// Cancel donation (only if status is pending)
router.patch('/:id/cancel', auth, async (req, res) => {
  try {
    const donation = await BulkDonation.findOne({
      _id: req.params.id,
      user: req.user.id,
      status: 'pending'
    });

    if (!donation) {
      return res.status(404).json({
        success: false,
        message: 'Donation not found or cannot be cancelled'
      });
    }

    donation.status = 'cancelled';
    donation.cancelledAt = new Date();
    await donation.save();

    // Remove pending tokens from user
    await User.findByIdAndUpdate(req.user.id, {
      $inc: { pendingTokens: -donation.tokensEarned }
    });

    res.json({
      success: true,
      message: 'Donation cancelled successfully',
      data: donation
    });

  } catch (error) {
    console.error('Error cancelling donation:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// Admin routes for managing donations
router.get('/admin/all', auth, async (req, res) => {
  try {
    // Check if user is admin
    if (req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: 'Access denied'
      });
    }

    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const skip = (page - 1) * limit;
    const status = req.query.status;
    const type = req.query.type;

    let filter = {};
    if (status) filter.status = status;
    if (type) filter.type = type;

    const donations = await BulkDonation.find(filter)
      .populate('user', 'firstName lastName phoneNumber')
      .populate('partner', 'name location')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit);

    const total = await BulkDonation.countDocuments(filter);

    res.json({
      success: true,
      data: donations,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });

  } catch (error) {
    console.error('Error fetching admin donations:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// Admin update donation status
router.patch('/admin/:id/status', auth, async (req, res) => {
  try {
    // Check if user is admin
    if (req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: 'Access denied'
      });
    }

    const { status, notes } = req.body;
    const validStatuses = ['pending', 'collected', 'completed', 'cancelled'];
    
    if (!validStatuses.includes(status)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid status'
      });
    }

    const donation = await BulkDonation.findById(req.params.id);
    if (!donation) {
      return res.status(404).json({
        success: false,
        message: 'Donation not found'
      });
    }

    const oldStatus = donation.status;
    donation.status = status;
    if (notes) donation.adminNotes = notes;

    // If donation is completed, award tokens to user
    if (status === 'completed' && oldStatus !== 'completed') {
      await User.findByIdAndUpdate(donation.user, {
        $inc: { 
          pediTokens: donation.tokensEarned,
          pendingTokens: -donation.tokensEarned,
          totalDonations: 1
        }
      });
      donation.completedAt = new Date();
    }

    await donation.save();

    res.json({
      success: true,
      message: 'Donation status updated successfully',
      data: donation
    });

  } catch (error) {
    console.error('Error updating donation status:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

module.exports = router;
