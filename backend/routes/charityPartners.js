const express = require('express');
const router = express.Router();
const CharityPartner = require('../src/models/CharityPartner').default;
const { authenticate: auth } = require('../src/middleware/auth');
const { body, validationResult } = require('express-validator');

// Get all active charity partners
router.get('/', auth, async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;
    const county = req.query.county;
    const town = req.query.town;
    const category = req.query.category;

    let filter = { isActive: true, isVerified: true };
    
    if (county) filter['location.county'] = county;
    if (town) filter['location.town'] = town;
    if (category) filter.categories = category;

    const charities = await CharityPartner.find(filter)
      .select('-adminNotes -documents -bankDetails')
      .sort({ 'rating.average': -1, 'statistics.totalDonations': -1 })
      .skip(skip)
      .limit(limit);

    const total = await CharityPartner.countDocuments(filter);

    res.json({
      success: true,
      data: charities,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });

  } catch (error) {
    console.error('Error fetching charity partners:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// Get charity partner by ID
router.get('/:id', auth, async (req, res) => {
  try {
    const charity = await CharityPartner.findOne({
      _id: req.params.id,
      isActive: true,
      isVerified: true
    }).select('-adminNotes -documents -bankDetails');

    if (!charity) {
      return res.status(404).json({
        success: false,
        message: 'Charity partner not found'
      });
    }

    res.json({
      success: true,
      data: charity
    });

  } catch (error) {
    console.error('Error fetching charity partner:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// Get nearby charity partners
router.get('/nearby/:county/:town', auth, async (req, res) => {
  try {
    const { county, town } = req.params;
    const limit = parseInt(req.query.limit) || 5;

    const nearbyCharities = await CharityPartner.findNearby(county, town, limit);

    res.json({
      success: true,
      data: nearbyCharities
    });

  } catch (error) {
    console.error('Error fetching nearby charities:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// Get top performing charity partners
router.get('/featured/top-performers', auth, async (req, res) => {
  try {
    const limit = parseInt(req.query.limit) || 5;
    const topCharities = await CharityPartner.getTopPerformers(limit);

    res.json({
      success: true,
      data: topCharities
    });

  } catch (error) {
    console.error('Error fetching top charities:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// Admin routes for managing charity partners
router.post('/admin', [
  auth,
  body('name').isLength({ min: 2, max: 100 }).withMessage('Name must be 2-100 characters'),
  body('description').isLength({ min: 10, max: 500 }).withMessage('Description must be 10-500 characters'),
  body('location.county').notEmpty().withMessage('County is required'),
  body('location.town').notEmpty().withMessage('Town is required'),
  body('location.address').notEmpty().withMessage('Address is required'),
  body('contactInfo.phone').isMobilePhone().withMessage('Valid phone number required'),
  body('contactInfo.email').isEmail().withMessage('Valid email required'),
  body('acceptedItems').isArray({ min: 1 }).withMessage('At least one accepted item required'),
  body('rewardRate').isFloat({ min: 1, max: 50 }).withMessage('Reward rate must be 1-50 tokens per kg'),
  body('registrationNumber').notEmpty().withMessage('Registration number is required')
], async (req, res) => {
  try {
    // Check if user is admin
    if (req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: 'Access denied'
      });
    }

    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation errors',
        errors: errors.array()
      });
    }

    // Check if registration number already exists
    const existingCharity = await CharityPartner.findOne({
      registrationNumber: req.body.registrationNumber
    });

    if (existingCharity) {
      return res.status(400).json({
        success: false,
        message: 'Registration number already exists'
      });
    }

    const charity = new CharityPartner({
      ...req.body,
      createdBy: req.user.id,
      isVerified: false,
      isActive: true
    });

    await charity.save();

    res.status(201).json({
      success: true,
      message: 'Charity partner created successfully',
      data: charity
    });

  } catch (error) {
    console.error('Error creating charity partner:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// Admin get all charity partners (including inactive)
router.get('/admin/all', auth, async (req, res) => {
  try {
    if (req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: 'Access denied'
      });
    }

    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const skip = (page - 1) * limit;
    const status = req.query.status; // active, inactive, verified, unverified

    let filter = {};
    if (status === 'active') filter.isActive = true;
    if (status === 'inactive') filter.isActive = false;
    if (status === 'verified') filter.isVerified = true;
    if (status === 'unverified') filter.isVerified = false;

    const charities = await CharityPartner.find(filter)
      .populate('createdBy', 'firstName lastName')
      .populate('lastUpdatedBy', 'firstName lastName')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit);

    const total = await CharityPartner.countDocuments(filter);

    res.json({
      success: true,
      data: charities,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });

  } catch (error) {
    console.error('Error fetching admin charities:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// Admin update charity partner
router.patch('/admin/:id', auth, async (req, res) => {
  try {
    if (req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: 'Access denied'
      });
    }

    const charity = await CharityPartner.findById(req.params.id);
    if (!charity) {
      return res.status(404).json({
        success: false,
        message: 'Charity partner not found'
      });
    }

    // Update fields
    Object.keys(req.body).forEach(key => {
      if (key !== '_id' && key !== 'createdBy' && key !== 'createdAt') {
        charity[key] = req.body[key];
      }
    });

    charity.lastUpdatedBy = req.user.id;
    await charity.save();

    res.json({
      success: true,
      message: 'Charity partner updated successfully',
      data: charity
    });

  } catch (error) {
    console.error('Error updating charity partner:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// Admin verify charity partner
router.patch('/admin/:id/verify', auth, async (req, res) => {
  try {
    if (req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: 'Access denied'
      });
    }

    const { isVerified, adminNotes } = req.body;

    const charity = await CharityPartner.findById(req.params.id);
    if (!charity) {
      return res.status(404).json({
        success: false,
        message: 'Charity partner not found'
      });
    }

    charity.isVerified = isVerified;
    if (adminNotes) charity.adminNotes = adminNotes;
    charity.lastUpdatedBy = req.user.id;

    await charity.save();

    res.json({
      success: true,
      message: `Charity partner ${isVerified ? 'verified' : 'unverified'} successfully`,
      data: charity
    });

  } catch (error) {
    console.error('Error verifying charity partner:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// Admin delete charity partner
router.delete('/admin/:id', auth, async (req, res) => {
  try {
    if (req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: 'Access denied'
      });
    }

    const charity = await CharityPartner.findById(req.params.id);
    if (!charity) {
      return res.status(404).json({
        success: false,
        message: 'Charity partner not found'
      });
    }

    // Soft delete by setting isActive to false
    charity.isActive = false;
    charity.lastUpdatedBy = req.user.id;
    await charity.save();

    res.json({
      success: true,
      message: 'Charity partner deactivated successfully'
    });

  } catch (error) {
    console.error('Error deleting charity partner:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

module.exports = router;
