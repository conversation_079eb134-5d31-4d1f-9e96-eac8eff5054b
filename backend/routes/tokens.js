const express = require('express');
const router = express.Router();
const TokenPurchase = require('../models/TokenPurchase');
const BulkDonation = require('../models/BulkDonation');
const User = require('../src/models/User').default;
const { authenticate: auth } = require('../src/middleware/auth');

// Get user's token transaction history
router.get('/history', auth, async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;
    const type = req.query.type; // 'all', 'earned', 'purchased', 'spent'

    const transactions = [];

    // Get token purchases
    if (!type || type === 'all' || type === 'purchased') {
      const purchases = await TokenPurchase.find({ 
        user: req.user.id,
        status: 'completed'
      })
        .sort({ completedAt: -1 })
        .limit(type === 'purchased' ? limit : Math.ceil(limit / 3));

      purchases.forEach(purchase => {
        transactions.push({
          _id: purchase._id,
          type: 'purchase',
          amount: purchase.totalTokens,
          description: `Token purchase - ${purchase.packageId} package`,
          createdAt: purchase.completedAt,
          status: 'completed',
          metadata: {
            packageId: purchase.packageId,
            paymentMethod: purchase.paymentMethod,
            amountPaid: purchase.amountKES
          }
        });
      });
    }

    // Get tokens earned from donations/recycling
    if (!type || type === 'all' || type === 'earned') {
      const donations = await BulkDonation.find({ 
        user: req.user.id,
        status: 'completed'
      })
        .populate('partner', 'name')
        .sort({ completedAt: -1 })
        .limit(type === 'earned' ? limit : Math.ceil(limit / 3));

      donations.forEach(donation => {
        transactions.push({
          _id: donation._id,
          type: 'earned',
          amount: donation.tokensEarned,
          description: `Tokens earned from ${donation.type} - ${donation.partner.name}`,
          createdAt: donation.completedAt,
          status: 'completed',
          metadata: {
            donationType: donation.type,
            weight: donation.weight,
            partnerName: donation.partner.name
          }
        });
      });
    }

    // Get tokens spent (this would come from clothing purchases/exchanges)
    // For now, we'll create mock spent transactions based on user's totalTokensSpent
    if (!type || type === 'all' || type === 'spent') {
      const user = await User.findById(req.user.id);
      if (user.totalTokensSpent > 0) {
        // This is a simplified approach - in a real app, you'd have a separate transactions table
        const spentTransactions = Math.min(3, Math.ceil(user.totalTokensSpent / 50));
        for (let i = 0; i < spentTransactions; i++) {
          transactions.push({
            _id: `spent_${i}_${req.user.id}`,
            type: 'spent',
            amount: Math.floor(user.totalTokensSpent / spentTransactions),
            description: 'Tokens spent on clothing exchange',
            createdAt: new Date(Date.now() - i * 7 * 24 * 60 * 60 * 1000), // Spread over weeks
            status: 'completed',
            metadata: {
              exchangeType: 'clothing_purchase'
            }
          });
        }
      }
    }

    // Sort by date and apply pagination
    transactions.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
    const paginatedTransactions = transactions.slice(skip, skip + limit);

    res.json({
      success: true,
      data: paginatedTransactions,
      pagination: {
        page,
        limit,
        total: transactions.length,
        pages: Math.ceil(transactions.length / limit)
      }
    });

  } catch (error) {
    console.error('Error fetching token history:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// Get user's token balance and summary
router.get('/balance', auth, async (req, res) => {
  try {
    const user = await User.findById(req.user.id);
    
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Get recent token activity summary
    const recentPurchases = await TokenPurchase.countDocuments({
      user: req.user.id,
      status: 'completed',
      completedAt: { $gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) }
    });

    const recentEarnings = await BulkDonation.aggregate([
      {
        $match: {
          user: req.user.id,
          status: 'completed',
          completedAt: { $gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) }
        }
      },
      {
        $group: {
          _id: null,
          totalEarned: { $sum: '$tokensEarned' },
          count: { $sum: 1 }
        }
      }
    ]);

    const monthlyEarnings = recentEarnings[0] || { totalEarned: 0, count: 0 };

    res.json({
      success: true,
      data: {
        currentBalance: user.pediTokens,
        pendingTokens: user.pendingTokens,
        totalEarned: user.totalTokensEarned,
        totalPurchased: user.totalTokensPurchased,
        totalSpent: user.totalTokensSpent,
        monthlyActivity: {
          purchases: recentPurchases,
          earned: monthlyEarnings.totalEarned,
          earningTransactions: monthlyEarnings.count
        }
      }
    });

  } catch (error) {
    console.error('Error fetching token balance:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// Get token earning opportunities
router.get('/opportunities', auth, async (req, res) => {
  try {
    const user = await User.findById(req.user.id);
    
    // Calculate potential earnings from different activities
    const opportunities = [
      {
        type: 'charity_donation',
        title: 'Donate to Charity',
        description: 'Earn 10-15 tokens per kg donated to charity partners',
        potentialTokens: '10-15 per kg',
        icon: 'heart',
        action: '/dashboard/donations'
      },
      {
        type: 'recycling',
        title: 'Recycle Clothing',
        description: 'Earn 7-10 tokens per kg recycled at partner centers',
        potentialTokens: '7-10 per kg',
        icon: 'recycle',
        action: '/dashboard/recycling'
      },
      {
        type: 'clothing_listing',
        title: 'List Quality Items',
        description: 'Earn bonus tokens for high-quality, popular listings',
        potentialTokens: '5-20 per item',
        icon: 'shirt',
        action: '/list-item'
      },
      {
        type: 'referral',
        title: 'Refer Friends',
        description: 'Earn tokens when friends join and make their first exchange',
        potentialTokens: '50 per referral',
        icon: 'users',
        action: '/referrals'
      }
    ];

    // Add personalized recommendations based on user activity
    const recommendations = [];
    
    if (user.totalDonations === 0) {
      recommendations.push({
        type: 'first_donation',
        title: 'Make Your First Donation',
        description: 'Get started with a 20% bonus on your first donation',
        bonus: '20% extra tokens'
      });
    }

    if (user.totalListings < 3) {
      recommendations.push({
        type: 'list_more_items',
        title: 'List More Items',
        description: 'Users with 5+ listings earn 50% more tokens on average',
        target: '5 listings'
      });
    }

    res.json({
      success: true,
      data: {
        opportunities,
        recommendations,
        userStats: {
          totalDonations: user.totalDonations,
          totalListings: user.totalListings,
          sustainabilityScore: user.sustainabilityScore
        }
      }
    });

  } catch (error) {
    console.error('Error fetching token opportunities:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// Get token leaderboard (anonymized)
router.get('/leaderboard', auth, async (req, res) => {
  try {
    const timeframe = req.query.timeframe || 'all'; // 'all', 'month', 'week'
    let dateFilter = {};

    if (timeframe === 'month') {
      dateFilter = { createdAt: { $gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) } };
    } else if (timeframe === 'week') {
      dateFilter = { createdAt: { $gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) } };
    }

    // Get top earners from donations/recycling
    const topEarners = await BulkDonation.aggregate([
      { 
        $match: { 
          status: 'completed',
          ...dateFilter
        } 
      },
      {
        $group: {
          _id: '$user',
          totalEarned: { $sum: '$tokensEarned' },
          totalWeight: { $sum: '$weight' },
          donationCount: { $sum: 1 }
        }
      },
      { $sort: { totalEarned: -1 } },
      { $limit: 10 },
      {
        $lookup: {
          from: 'users',
          localField: '_id',
          foreignField: '_id',
          as: 'user'
        }
      },
      { $unwind: '$user' },
      {
        $project: {
          _id: 0,
          rank: { $literal: 0 }, // Will be set in the map function
          initials: {
            $concat: [
              { $substr: ['$user.firstName', 0, 1] },
              { $substr: ['$user.lastName', 0, 1] }
            ]
          },
          county: '$user.location.county',
          totalEarned: 1,
          totalWeight: 1,
          donationCount: 1,
          isCurrentUser: { $eq: ['$_id', req.user.id] }
        }
      }
    ]);

    // Add rank numbers
    topEarners.forEach((earner, index) => {
      earner.rank = index + 1;
    });

    // Get current user's position if not in top 10
    const currentUserStats = await BulkDonation.aggregate([
      { 
        $match: { 
          user: req.user.id,
          status: 'completed',
          ...dateFilter
        } 
      },
      {
        $group: {
          _id: null,
          totalEarned: { $sum: '$tokensEarned' },
          totalWeight: { $sum: '$weight' },
          donationCount: { $sum: 1 }
        }
      }
    ]);

    const userStats = currentUserStats[0] || {
      totalEarned: 0,
      totalWeight: 0,
      donationCount: 0
    };

    res.json({
      success: true,
      data: {
        leaderboard: topEarners,
        currentUser: {
          ...userStats,
          rank: topEarners.findIndex(e => e.isCurrentUser) + 1 || null
        },
        timeframe
      }
    });

  } catch (error) {
    console.error('Error fetching token leaderboard:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

module.exports = router;
