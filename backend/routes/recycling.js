const express = require('express');
const router = express.Router();
const BulkDonation = require('../models/BulkDonation');
const { authenticate: auth } = require('../src/middleware/auth');

// Get user's recycling statistics
router.get('/stats', auth, async (req, res) => {
  try {
    const userId = req.user.id;

    // Get recycling stats
    const recyclingStats = await BulkDonation.aggregate([
      { 
        $match: { 
          user: userId, 
          type: 'recycling',
          status: 'completed'
        } 
      },
      {
        $group: {
          _id: null,
          totalWeight: { $sum: '$weight' },
          totalRecycling: { $sum: 1 },
          tokensEarned: { $sum: '$tokensEarned' },
          co2Saved: { $sum: '$environmentalImpact.co2Saved' },
          waterSaved: { $sum: '$environmentalImpact.waterSaved' }
        }
      }
    ]);

    const stats = recyclingStats[0] || {
      totalWeight: 0,
      totalRecycling: 0,
      tokensEarned: 0,
      co2Saved: 0,
      waterSaved: 0
    };

    // Calculate eco rank based on environmental impact
    let rank = 'Eco-Starter';
    const impactScore = stats.co2Saved + (stats.waterSaved / 1000); // Simplified scoring
    
    if (impactScore >= 1000) rank = 'Eco-Legend';
    else if (impactScore >= 500) rank = 'Eco-Champion';
    else if (impactScore >= 100) rank = 'Eco-Warrior';

    res.json({
      success: true,
      data: {
        ...stats,
        rank
      }
    });

  } catch (error) {
    console.error('Error fetching recycling stats:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// Get user's recycling entries
router.get('/my-entries', auth, async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    const entries = await BulkDonation.find({ 
      user: req.user.id,
      type: 'recycling'
    })
      .populate('partner', 'name location')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit);

    const total = await BulkDonation.countDocuments({ 
      user: req.user.id,
      type: 'recycling'
    });

    // Transform data for frontend
    const transformedEntries = entries.map(entry => ({
      _id: entry._id,
      weight: entry.weight,
      tokensEarned: entry.tokensEarned,
      center: {
        name: entry.partner.name,
        location: entry.partner.location
      },
      status: entry.status,
      createdAt: entry.createdAt,
      environmentalImpact: entry.environmentalImpact
    }));

    res.json({
      success: true,
      data: transformedEntries,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });

  } catch (error) {
    console.error('Error fetching recycling entries:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// Get recycling trends and analytics
router.get('/analytics', auth, async (req, res) => {
  try {
    const userId = req.user.id;
    const months = parseInt(req.query.months) || 12;

    // Get monthly recycling trends
    const monthlyTrends = await BulkDonation.aggregate([
      { 
        $match: { 
          user: userId,
          type: 'recycling',
          status: 'completed',
          createdAt: { 
            $gte: new Date(Date.now() - months * 30 * 24 * 60 * 60 * 1000) 
          }
        } 
      },
      {
        $group: {
          _id: {
            year: { $year: '$createdAt' },
            month: { $month: '$createdAt' }
          },
          weight: { $sum: '$weight' },
          tokens: { $sum: '$tokensEarned' },
          co2Saved: { $sum: '$environmentalImpact.co2Saved' },
          waterSaved: { $sum: '$environmentalImpact.waterSaved' },
          count: { $sum: 1 }
        }
      },
      { $sort: { '_id.year': 1, '_id.month': 1 } }
    ]);

    // Get recycling by center
    const centerBreakdown = await BulkDonation.aggregate([
      { 
        $match: { 
          user: userId,
          type: 'recycling',
          status: 'completed'
        } 
      },
      {
        $lookup: {
          from: 'recyclingcenters',
          localField: 'partner',
          foreignField: '_id',
          as: 'center'
        }
      },
      { $unwind: '$center' },
      {
        $group: {
          _id: '$center._id',
          centerName: { $first: '$center.name' },
          totalWeight: { $sum: '$weight' },
          totalTokens: { $sum: '$tokensEarned' },
          totalCO2: { $sum: '$environmentalImpact.co2Saved' },
          count: { $sum: 1 }
        }
      },
      { $sort: { totalWeight: -1 } }
    ]);

    res.json({
      success: true,
      data: {
        monthlyTrends,
        centerBreakdown
      }
    });

  } catch (error) {
    console.error('Error fetching recycling analytics:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// Get global recycling impact
router.get('/global-impact', auth, async (req, res) => {
  try {
    const globalStats = await BulkDonation.aggregate([
      { 
        $match: { 
          type: 'recycling',
          status: 'completed'
        } 
      },
      {
        $group: {
          _id: null,
          totalWeight: { $sum: '$weight' },
          totalEntries: { $sum: 1 },
          totalCO2Saved: { $sum: '$environmentalImpact.co2Saved' },
          totalWaterSaved: { $sum: '$environmentalImpact.waterSaved' },
          totalTokensAwarded: { $sum: '$tokensEarned' }
        }
      }
    ]);

    const stats = globalStats[0] || {
      totalWeight: 0,
      totalEntries: 0,
      totalCO2Saved: 0,
      totalWaterSaved: 0,
      totalTokensAwarded: 0
    };

    // Get top recyclers (anonymized)
    const topRecyclers = await BulkDonation.aggregate([
      { 
        $match: { 
          type: 'recycling',
          status: 'completed'
        } 
      },
      {
        $group: {
          _id: '$user',
          totalWeight: { $sum: '$weight' },
          totalCO2: { $sum: '$environmentalImpact.co2Saved' }
        }
      },
      { $sort: { totalWeight: -1 } },
      { $limit: 10 },
      {
        $project: {
          _id: 0,
          rank: { $add: [{ $indexOfArray: [[], '$_id'] }, 1] },
          totalWeight: 1,
          totalCO2: 1
        }
      }
    ]);

    res.json({
      success: true,
      data: {
        globalStats: stats,
        topRecyclers
      }
    });

  } catch (error) {
    console.error('Error fetching global recycling impact:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// Admin recycling analytics
router.get('/admin/analytics', auth, async (req, res) => {
  try {
    if (req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: 'Access denied'
      });
    }

    const timeframe = req.query.timeframe || '30'; // days
    const startDate = new Date(Date.now() - parseInt(timeframe) * 24 * 60 * 60 * 1000);

    // Overall recycling stats
    const overallStats = await BulkDonation.aggregate([
      { 
        $match: { 
          type: 'recycling',
          createdAt: { $gte: startDate }
        } 
      },
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 },
          totalWeight: { $sum: '$weight' },
          totalTokens: { $sum: '$tokensEarned' },
          totalCO2: { $sum: '$environmentalImpact.co2Saved' }
        }
      }
    ]);

    // Daily trends
    const dailyTrends = await BulkDonation.aggregate([
      { 
        $match: { 
          type: 'recycling',
          createdAt: { $gte: startDate }
        } 
      },
      {
        $group: {
          _id: {
            year: { $year: '$createdAt' },
            month: { $month: '$createdAt' },
            day: { $dayOfMonth: '$createdAt' }
          },
          count: { $sum: 1 },
          weight: { $sum: '$weight' },
          co2Saved: { $sum: '$environmentalImpact.co2Saved' }
        }
      },
      { $sort: { '_id.year': 1, '_id.month': 1, '_id.day': 1 } }
    ]);

    // Top performing centers
    const topCenters = await BulkDonation.aggregate([
      { 
        $match: { 
          type: 'recycling',
          status: 'completed',
          createdAt: { $gte: startDate }
        } 
      },
      {
        $lookup: {
          from: 'recyclingcenters',
          localField: 'partner',
          foreignField: '_id',
          as: 'center'
        }
      },
      { $unwind: '$center' },
      {
        $group: {
          _id: '$center._id',
          centerName: { $first: '$center.name' },
          location: { $first: '$center.location' },
          totalWeight: { $sum: '$weight' },
          totalEntries: { $sum: 1 },
          totalCO2: { $sum: '$environmentalImpact.co2Saved' },
          averageWeight: { $avg: '$weight' }
        }
      },
      { $sort: { totalWeight: -1 } },
      { $limit: 10 }
    ]);

    res.json({
      success: true,
      data: {
        overallStats,
        dailyTrends,
        topCenters,
        timeframe: `${timeframe} days`
      }
    });

  } catch (error) {
    console.error('Error fetching admin recycling analytics:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

module.exports = router;
