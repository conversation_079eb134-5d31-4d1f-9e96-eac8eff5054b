const mongoose = require('mongoose');

const tokenPurchaseSchema = new mongoose.Schema({
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  packageId: {
    type: String,
    required: true,
    enum: ['starter', 'popular', 'value', 'premium', 'custom']
  },
  tokens: {
    type: Number,
    required: true,
    min: 1
  },
  bonusTokens: {
    type: Number,
    default: 0,
    min: 0
  },
  totalTokens: {
    type: Number,
    required: true,
    min: 1
  },
  amountKES: {
    type: Number,
    required: true,
    min: 0
  },
  amountUSD: {
    type: Number,
    required: true,
    min: 0
  },
  actualAmount: {
    type: Number // Actual amount paid (from payment gateway)
  },
  paymentMethod: {
    type: String,
    enum: ['mpesa', 'card'],
    required: true
  },
  phoneNumber: {
    type: String // For M-Pesa payments
  },
  paymentReference: {
    type: String,
    unique: true,
    sparse: true
  },
  mpesaReceiptNumber: {
    type: String // M-Pesa receipt number
  },
  cardTransactionId: {
    type: String // Card payment transaction ID
  },
  status: {
    type: String,
    enum: ['pending', 'processing', 'completed', 'failed', 'cancelled', 'refunded'],
    default: 'pending'
  },
  failureReason: {
    type: String
  },
  completedAt: {
    type: Date
  },
  cancelledAt: {
    type: Date
  },
  refundedAt: {
    type: Date
  },
  refundAmount: {
    type: Number
  },
  refundReason: {
    type: String
  },
  metadata: {
    userAgent: String,
    ipAddress: String,
    deviceInfo: String
  },
  adminNotes: {
    type: String,
    maxlength: 500
  }
}, {
  timestamps: true
});

// Indexes for efficient queries
tokenPurchaseSchema.index({ user: 1, createdAt: -1 });
tokenPurchaseSchema.index({ status: 1, createdAt: -1 });
tokenPurchaseSchema.index({ paymentReference: 1 });
tokenPurchaseSchema.index({ paymentMethod: 1 });

// Virtual for purchase reference number
tokenPurchaseSchema.virtual('referenceNumber').get(function() {
  return `TKN-${this._id.toString().slice(-8).toUpperCase()}`;
});

// Pre-save middleware to calculate total tokens
tokenPurchaseSchema.pre('save', function(next) {
  if (this.isModified('tokens') || this.isModified('bonusTokens')) {
    this.totalTokens = this.tokens + this.bonusTokens;
  }
  next();
});

// Method to process refund
tokenPurchaseSchema.methods.processRefund = async function(amount, reason) {
  if (this.status !== 'completed') {
    throw new Error('Can only refund completed purchases');
  }
  
  this.status = 'refunded';
  this.refundedAt = new Date();
  this.refundAmount = amount || this.actualAmount || this.amountKES;
  this.refundReason = reason;
  
  await this.save();
  
  // Remove tokens from user account
  const User = mongoose.model('User');
  await User.findByIdAndUpdate(this.user, {
    $inc: { 
      pediTokens: -this.totalTokens,
      totalTokensPurchased: -this.totalTokens
    }
  });
  
  return this;
};

// Static method to get purchase statistics
tokenPurchaseSchema.statics.getStats = async function(startDate, endDate) {
  const matchStage = {
    status: 'completed'
  };
  
  if (startDate && endDate) {
    matchStage.completedAt = {
      $gte: new Date(startDate),
      $lte: new Date(endDate)
    };
  }
  
  const stats = await this.aggregate([
    { $match: matchStage },
    {
      $group: {
        _id: null,
        totalPurchases: { $sum: 1 },
        totalRevenue: { $sum: '$amountKES' },
        totalTokensSold: { $sum: '$totalTokens' },
        averagePurchaseAmount: { $avg: '$amountKES' },
        mpesaPurchases: {
          $sum: { $cond: [{ $eq: ['$paymentMethod', 'mpesa'] }, 1, 0] }
        },
        cardPurchases: {
          $sum: { $cond: [{ $eq: ['$paymentMethod', 'card'] }, 1, 0] }
        }
      }
    }
  ]);
  
  return stats[0] || {
    totalPurchases: 0,
    totalRevenue: 0,
    totalTokensSold: 0,
    averagePurchaseAmount: 0,
    mpesaPurchases: 0,
    cardPurchases: 0
  };
};

// Static method to get monthly trends
tokenPurchaseSchema.statics.getMonthlyTrends = async function(months = 12) {
  const startDate = new Date();
  startDate.setMonth(startDate.getMonth() - months);
  
  return await this.aggregate([
    { 
      $match: { 
        completedAt: { $gte: startDate },
        status: 'completed'
      } 
    },
    {
      $group: {
        _id: {
          year: { $year: '$completedAt' },
          month: { $month: '$completedAt' }
        },
        purchases: { $sum: 1 },
        revenue: { $sum: '$amountKES' },
        tokens: { $sum: '$totalTokens' }
      }
    },
    { $sort: { '_id.year': 1, '_id.month': 1 } }
  ]);
};

// Static method to get package popularity
tokenPurchaseSchema.statics.getPackageStats = async function() {
  return await this.aggregate([
    { $match: { status: 'completed' } },
    {
      $group: {
        _id: '$packageId',
        count: { $sum: 1 },
        totalRevenue: { $sum: '$amountKES' },
        totalTokens: { $sum: '$totalTokens' }
      }
    },
    { $sort: { count: -1 } }
  ]);
};

// Static method to get user purchase summary
tokenPurchaseSchema.statics.getUserSummary = async function(userId) {
  const result = await this.aggregate([
    { $match: { user: mongoose.Types.ObjectId(userId), status: 'completed' } },
    {
      $group: {
        _id: null,
        totalPurchases: { $sum: 1 },
        totalSpent: { $sum: '$amountKES' },
        totalTokensPurchased: { $sum: '$totalTokens' },
        firstPurchase: { $min: '$completedAt' },
        lastPurchase: { $max: '$completedAt' },
        favoritePaymentMethod: { $first: '$paymentMethod' }
      }
    }
  ]);
  
  return result[0] || {
    totalPurchases: 0,
    totalSpent: 0,
    totalTokensPurchased: 0,
    firstPurchase: null,
    lastPurchase: null,
    favoritePaymentMethod: null
  };
};

module.exports = mongoose.model('TokenPurchase', tokenPurchaseSchema);
