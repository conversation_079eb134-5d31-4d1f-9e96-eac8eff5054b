const mongoose = require('mongoose');

const recyclingCenterSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    trim: true,
    maxlength: 100
  },
  description: {
    type: String,
    required: true,
    maxlength: 500
  },
  location: {
    county: {
      type: String,
      required: true
    },
    town: {
      type: String,
      required: true
    },
    address: {
      type: String,
      required: true
    },
    coordinates: {
      latitude: Number,
      longitude: Number
    }
  },
  contactInfo: {
    phone: {
      type: String,
      required: true
    },
    email: {
      type: String,
      required: true
    },
    website: {
      type: String
    }
  },
  specialties: [{
    type: String,
    enum: [
      'cotton',
      'polyester',
      'wool',
      'silk',
      'denim',
      'leather',
      'synthetic_fabrics',
      'mixed_materials',
      'shoes',
      'accessories',
      'zippers_buttons',
      'all_textiles'
    ]
  }],
  acceptedItems: [{
    type: String,
    required: true
  }],
  rewardRate: {
    type: Number,
    required: true,
    min: 1,
    max: 30,
    default: 8 // tokens per kg (usually lower than charity donations)
  },
  processingMethods: [{
    type: String,
    enum: [
      'mechanical_recycling',
      'chemical_recycling',
      'upcycling',
      'downcycling',
      'fiber_recovery',
      'energy_recovery'
    ]
  }],
  operatingHours: {
    monday: { open: String, close: String },
    tuesday: { open: String, close: String },
    wednesday: { open: String, close: String },
    thursday: { open: String, close: String },
    friday: { open: String, close: String },
    saturday: { open: String, close: String },
    sunday: { open: String, close: String }
  },
  images: [{
    type: String // URLs to facility images
  }],
  logo: {
    type: String // URL to company logo
  },
  licenseNumber: {
    type: String,
    required: true,
    unique: true
  },
  certifications: [{
    name: String,
    issuedBy: String,
    validUntil: Date,
    certificateUrl: String
  }],
  isVerified: {
    type: Boolean,
    default: false
  },
  isActive: {
    type: Boolean,
    default: true
  },
  capacity: {
    maxWeightPerDelivery: {
      type: Number,
      default: 100 // kg
    },
    monthlyCapacity: {
      type: Number,
      default: 2000 // kg per month
    },
    currentLoad: {
      type: Number,
      default: 0
    }
  },
  statistics: {
    totalRecycled: {
      type: Number,
      default: 0
    },
    totalWeight: {
      type: Number,
      default: 0
    },
    monthlyRecycled: {
      type: Number,
      default: 0
    },
    co2Saved: {
      type: Number,
      default: 0
    },
    waterSaved: {
      type: Number,
      default: 0
    },
    lastRecyclingDate: {
      type: Date
    }
  },
  environmentalImpact: {
    co2ReductionRate: {
      type: Number,
      default: 3 // kg CO2 saved per kg recycled
    },
    waterSavingRate: {
      type: Number,
      default: 2700 // liters saved per kg recycled
    },
    energySavingRate: {
      type: Number,
      default: 0.5 // kWh saved per kg recycled
    }
  },
  pricing: {
    pickupFee: {
      type: Number,
      default: 0
    },
    processingFee: {
      type: Number,
      default: 0
    },
    minimumWeight: {
      type: Number,
      default: 1 // kg
    }
  },
  rating: {
    average: {
      type: Number,
      default: 0,
      min: 0,
      max: 5
    },
    count: {
      type: Number,
      default: 0
    }
  },
  bankDetails: {
    accountName: String,
    accountNumber: String,
    bankName: String,
    branchCode: String
  },
  documents: [{
    type: {
      type: String,
      enum: ['business_license', 'environmental_permit', 'waste_management_license', 'other']
    },
    url: String,
    uploadedAt: {
      type: Date,
      default: Date.now
    }
  }],
  adminNotes: {
    type: String,
    maxlength: 1000
  },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  lastUpdatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true
});

// Indexes for efficient queries
recyclingCenterSchema.index({ 'location.county': 1, 'location.town': 1 });
recyclingCenterSchema.index({ isActive: 1, isVerified: 1 });
recyclingCenterSchema.index({ specialties: 1 });
recyclingCenterSchema.index({ 'rating.average': -1 });

// Virtual for full location
recyclingCenterSchema.virtual('fullLocation').get(function() {
  return `${this.location.town}, ${this.location.county}`;
});

// Method to update statistics and environmental impact
recyclingCenterSchema.methods.updateStats = async function(recyclingWeight) {
  this.statistics.totalRecycled += 1;
  this.statistics.totalWeight += recyclingWeight;
  this.statistics.lastRecyclingDate = new Date();
  
  // Calculate environmental impact
  const co2Saved = recyclingWeight * this.environmentalImpact.co2ReductionRate;
  const waterSaved = recyclingWeight * this.environmentalImpact.waterSavingRate;
  
  this.statistics.co2Saved += co2Saved;
  this.statistics.waterSaved += waterSaved;
  
  // Update monthly recycling (reset if new month)
  const now = new Date();
  const lastUpdate = this.updatedAt || this.createdAt;
  
  if (now.getMonth() !== lastUpdate.getMonth() || now.getFullYear() !== lastUpdate.getFullYear()) {
    this.statistics.monthlyRecycled = recyclingWeight;
  } else {
    this.statistics.monthlyRecycled += recyclingWeight;
  }
  
  await this.save();
};

// Method to check if center can accept recycling
recyclingCenterSchema.methods.canAcceptRecycling = function(weight, clothingTypes) {
  if (!this.isActive || !this.isVerified) {
    return { canAccept: false, reason: 'Recycling center is not active or verified' };
  }
  
  if (weight < this.pricing.minimumWeight) {
    return { canAccept: false, reason: `Minimum weight is ${this.pricing.minimumWeight}kg` };
  }
  
  if (weight > this.capacity.maxWeightPerDelivery) {
    return { canAccept: false, reason: 'Delivery exceeds maximum weight limit' };
  }
  
  if (this.statistics.monthlyRecycled + weight > this.capacity.monthlyCapacity) {
    return { canAccept: false, reason: 'Monthly capacity exceeded' };
  }
  
  // Check if center accepts the clothing types
  const acceptsAllTypes = clothingTypes.every(type => 
    this.acceptedItems.some(accepted => 
      accepted.toLowerCase().includes(type.toLowerCase()) || 
      type.toLowerCase().includes(accepted.toLowerCase())
    ) || this.specialties.includes('all_textiles')
  );
  
  if (!acceptsAllTypes) {
    return { canAccept: false, reason: 'Some clothing types are not accepted' };
  }
  
  return { canAccept: true };
};

// Method to calculate environmental impact for a given weight
recyclingCenterSchema.methods.calculateImpact = function(weight) {
  return {
    co2Saved: weight * this.environmentalImpact.co2ReductionRate,
    waterSaved: weight * this.environmentalImpact.waterSavingRate,
    energySaved: weight * this.environmentalImpact.energySavingRate
  };
};

// Static method to find nearby recycling centers
recyclingCenterSchema.statics.findNearby = async function(county, town, limit = 10) {
  return await this.find({
    isActive: true,
    isVerified: true,
    $or: [
      { 'location.county': county, 'location.town': town },
      { 'location.county': county }
    ]
  })
  .sort({ 'rating.average': -1, 'statistics.totalRecycled': -1 })
  .limit(limit);
};

// Static method to get centers by specialty
recyclingCenterSchema.statics.findBySpecialty = async function(specialty, limit = 10) {
  return await this.find({
    isActive: true,
    isVerified: true,
    $or: [
      { specialties: specialty },
      { specialties: 'all_textiles' }
    ]
  })
  .sort({ 'rating.average': -1 })
  .limit(limit);
};

// Static method to get top performing centers
recyclingCenterSchema.statics.getTopPerformers = async function(limit = 5) {
  return await this.find({
    isActive: true,
    isVerified: true
  })
  .sort({ 
    'statistics.co2Saved': -1,
    'rating.average': -1, 
    'statistics.totalRecycled': -1
  })
  .limit(limit);
};

module.exports = mongoose.model('RecyclingCenter', recyclingCenterSchema);
