const mongoose = require('mongoose');

const bulkDonationSchema = new mongoose.Schema({
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  type: {
    type: String,
    enum: ['charity', 'recycling'],
    required: true
  },
  weight: {
    type: Number,
    required: true,
    min: 0.1,
    max: 100
  },
  description: {
    type: String,
    maxlength: 500
  },
  clothingTypes: [{
    type: String,
    required: true
  }],
  condition: {
    type: String,
    enum: ['excellent', 'good', 'fair', 'poor'],
    required: true
  },
  partner: {
    type: mongoose.Schema.Types.ObjectId,
    required: true,
    refPath: 'partnerModel'
  },
  partnerModel: {
    type: String,
    required: true,
    enum: ['CharityPartner', 'RecyclingCenter']
  },
  deliveryMethod: {
    type: String,
    enum: ['pickup', 'drop_off'],
    required: true
  },
  contactInfo: {
    phone: {
      type: String,
      required: true
    },
    address: {
      type: String,
      required: true
    },
    preferredTime: {
      type: String
    }
  },
  isAnonymous: {
    type: Boolean,
    default: false
  },
  tokensEarned: {
    type: Number,
    required: true,
    min: 0
  },
  status: {
    type: String,
    enum: ['pending', 'collected', 'completed', 'cancelled'],
    default: 'pending'
  },
  estimatedPickupDate: {
    type: Date
  },
  actualPickupDate: {
    type: Date
  },
  completedAt: {
    type: Date
  },
  cancelledAt: {
    type: Date
  },
  adminNotes: {
    type: String,
    maxlength: 1000
  },
  images: [{
    type: String // URLs to images of the donated items
  }],
  environmentalImpact: {
    co2Saved: {
      type: Number,
      default: 0
    },
    waterSaved: {
      type: Number,
      default: 0
    }
  },
  feedback: {
    rating: {
      type: Number,
      min: 1,
      max: 5
    },
    comment: {
      type: String,
      maxlength: 500
    },
    submittedAt: {
      type: Date
    }
  }
}, {
  timestamps: true
});

// Pre-save middleware to set partnerModel based on type
bulkDonationSchema.pre('save', function(next) {
  if (this.type === 'charity') {
    this.partnerModel = 'CharityPartner';
  } else if (this.type === 'recycling') {
    this.partnerModel = 'RecyclingCenter';
  }
  
  // Calculate environmental impact for recycling
  if (this.type === 'recycling' && this.weight) {
    // Rough estimates: 1kg of recycled clothing saves ~3kg CO2 and ~2700L water
    this.environmentalImpact.co2Saved = Math.round(this.weight * 3);
    this.environmentalImpact.waterSaved = Math.round(this.weight * 2700);
  }
  
  next();
});

// Index for efficient queries
bulkDonationSchema.index({ user: 1, createdAt: -1 });
bulkDonationSchema.index({ status: 1, createdAt: -1 });
bulkDonationSchema.index({ type: 1, status: 1 });
bulkDonationSchema.index({ partner: 1 });

// Virtual for donation reference number
bulkDonationSchema.virtual('referenceNumber').get(function() {
  return `${this.type.toUpperCase()}-${this._id.toString().slice(-8).toUpperCase()}`;
});

// Method to calculate total impact for user
bulkDonationSchema.statics.getUserImpact = async function(userId) {
  const result = await this.aggregate([
    { $match: { user: mongoose.Types.ObjectId(userId), status: 'completed' } },
    {
      $group: {
        _id: null,
        totalWeight: { $sum: '$weight' },
        totalTokens: { $sum: '$tokensEarned' },
        totalDonations: { $sum: 1 },
        totalCO2Saved: { $sum: '$environmentalImpact.co2Saved' },
        totalWaterSaved: { $sum: '$environmentalImpact.waterSaved' },
        charityDonations: {
          $sum: { $cond: [{ $eq: ['$type', 'charity'] }, 1, 0] }
        },
        recyclingDonations: {
          $sum: { $cond: [{ $eq: ['$type', 'recycling'] }, 1, 0] }
        }
      }
    }
  ]);
  
  return result[0] || {
    totalWeight: 0,
    totalTokens: 0,
    totalDonations: 0,
    totalCO2Saved: 0,
    totalWaterSaved: 0,
    charityDonations: 0,
    recyclingDonations: 0
  };
};

// Method to get partner statistics
bulkDonationSchema.statics.getPartnerStats = async function(partnerId, partnerType) {
  const partnerModel = partnerType === 'charity' ? 'CharityPartner' : 'RecyclingCenter';
  
  const result = await this.aggregate([
    { 
      $match: { 
        partner: mongoose.Types.ObjectId(partnerId),
        partnerModel: partnerModel,
        status: 'completed'
      } 
    },
    {
      $group: {
        _id: null,
        totalWeight: { $sum: '$weight' },
        totalDonations: { $sum: 1 },
        totalTokensAwarded: { $sum: '$tokensEarned' },
        averageWeight: { $avg: '$weight' },
        totalCO2Saved: { $sum: '$environmentalImpact.co2Saved' },
        totalWaterSaved: { $sum: '$environmentalImpact.waterSaved' }
      }
    }
  ]);
  
  return result[0] || {
    totalWeight: 0,
    totalDonations: 0,
    totalTokensAwarded: 0,
    averageWeight: 0,
    totalCO2Saved: 0,
    totalWaterSaved: 0
  };
};

// Method to get monthly donation trends
bulkDonationSchema.statics.getMonthlyTrends = async function(months = 12) {
  const startDate = new Date();
  startDate.setMonth(startDate.getMonth() - months);
  
  return await this.aggregate([
    { $match: { createdAt: { $gte: startDate }, status: 'completed' } },
    {
      $group: {
        _id: {
          year: { $year: '$createdAt' },
          month: { $month: '$createdAt' },
          type: '$type'
        },
        count: { $sum: 1 },
        totalWeight: { $sum: '$weight' },
        totalTokens: { $sum: '$tokensEarned' }
      }
    },
    { $sort: { '_id.year': 1, '_id.month': 1 } }
  ]);
};

module.exports = mongoose.model('BulkDonation', bulkDonationSchema);
