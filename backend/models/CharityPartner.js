const mongoose = require('mongoose');

const charityPartnerSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    trim: true,
    maxlength: 100
  },
  description: {
    type: String,
    required: true,
    maxlength: 500
  },
  mission: {
    type: String,
    maxlength: 1000
  },
  location: {
    county: {
      type: String,
      required: true
    },
    town: {
      type: String,
      required: true
    },
    address: {
      type: String,
      required: true
    },
    coordinates: {
      latitude: Number,
      longitude: Number
    }
  },
  contactInfo: {
    phone: {
      type: String,
      required: true
    },
    email: {
      type: String,
      required: true
    },
    website: {
      type: String
    },
    socialMedia: {
      facebook: String,
      twitter: String,
      instagram: String
    }
  },
  acceptedItems: [{
    type: String,
    required: true
  }],
  rewardRate: {
    type: Number,
    required: true,
    min: 1,
    max: 50,
    default: 10 // tokens per kg
  },
  operatingHours: {
    monday: { open: String, close: String },
    tuesday: { open: String, close: String },
    wednesday: { open: String, close: String },
    thursday: { open: String, close: String },
    friday: { open: String, close: String },
    saturday: { open: String, close: String },
    sunday: { open: String, close: String }
  },
  images: [{
    type: String // URLs to charity images
  }],
  logo: {
    type: String // URL to charity logo
  },
  registrationNumber: {
    type: String,
    required: true,
    unique: true
  },
  isVerified: {
    type: Boolean,
    default: false
  },
  isActive: {
    type: Boolean,
    default: true
  },
  categories: [{
    type: String,
    enum: [
      'children',
      'elderly',
      'homeless',
      'disaster_relief',
      'education',
      'healthcare',
      'community_development',
      'women_empowerment',
      'disability_support',
      'environmental'
    ]
  }],
  capacity: {
    maxWeightPerDonation: {
      type: Number,
      default: 100 // kg
    },
    monthlyCapacity: {
      type: Number,
      default: 1000 // kg per month
    }
  },
  statistics: {
    totalDonations: {
      type: Number,
      default: 0
    },
    totalWeight: {
      type: Number,
      default: 0
    },
    totalBeneficiaries: {
      type: Number,
      default: 0
    },
    monthlyDonations: {
      type: Number,
      default: 0
    },
    lastDonationDate: {
      type: Date
    }
  },
  rating: {
    average: {
      type: Number,
      default: 0,
      min: 0,
      max: 5
    },
    count: {
      type: Number,
      default: 0
    }
  },
  bankDetails: {
    accountName: String,
    accountNumber: String,
    bankName: String,
    branchCode: String
  },
  documents: [{
    type: {
      type: String,
      enum: ['registration_certificate', 'tax_exemption', 'audit_report', 'other']
    },
    url: String,
    uploadedAt: {
      type: Date,
      default: Date.now
    }
  }],
  adminNotes: {
    type: String,
    maxlength: 1000
  },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  lastUpdatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true
});

// Indexes for efficient queries
charityPartnerSchema.index({ 'location.county': 1, 'location.town': 1 });
charityPartnerSchema.index({ isActive: 1, isVerified: 1 });
charityPartnerSchema.index({ categories: 1 });
charityPartnerSchema.index({ 'rating.average': -1 });

// Virtual for full location
charityPartnerSchema.virtual('fullLocation').get(function() {
  return `${this.location.town}, ${this.location.county}`;
});

// Method to update statistics
charityPartnerSchema.methods.updateStats = async function(donationWeight) {
  this.statistics.totalDonations += 1;
  this.statistics.totalWeight += donationWeight;
  this.statistics.lastDonationDate = new Date();
  
  // Update monthly donations (reset if new month)
  const now = new Date();
  const lastUpdate = this.updatedAt || this.createdAt;
  
  if (now.getMonth() !== lastUpdate.getMonth() || now.getFullYear() !== lastUpdate.getFullYear()) {
    this.statistics.monthlyDonations = donationWeight;
  } else {
    this.statistics.monthlyDonations += donationWeight;
  }
  
  await this.save();
};

// Method to check if charity can accept donation
charityPartnerSchema.methods.canAcceptDonation = function(weight, clothingTypes) {
  if (!this.isActive || !this.isVerified) {
    return { canAccept: false, reason: 'Charity is not active or verified' };
  }
  
  if (weight > this.capacity.maxWeightPerDonation) {
    return { canAccept: false, reason: 'Donation exceeds maximum weight limit' };
  }
  
  if (this.statistics.monthlyDonations + weight > this.capacity.monthlyCapacity) {
    return { canAccept: false, reason: 'Monthly capacity exceeded' };
  }
  
  // Check if charity accepts the clothing types
  const acceptsAllTypes = clothingTypes.every(type => 
    this.acceptedItems.some(accepted => 
      accepted.toLowerCase().includes(type.toLowerCase()) || 
      type.toLowerCase().includes(accepted.toLowerCase())
    )
  );
  
  if (!acceptsAllTypes) {
    return { canAccept: false, reason: 'Some clothing types are not accepted' };
  }
  
  return { canAccept: true };
};

// Static method to find nearby charities
charityPartnerSchema.statics.findNearby = async function(county, town, limit = 10) {
  return await this.find({
    isActive: true,
    isVerified: true,
    $or: [
      { 'location.county': county, 'location.town': town },
      { 'location.county': county }
    ]
  })
  .sort({ 'rating.average': -1, 'statistics.totalDonations': -1 })
  .limit(limit);
};

// Static method to get top performing charities
charityPartnerSchema.statics.getTopPerformers = async function(limit = 5) {
  return await this.find({
    isActive: true,
    isVerified: true
  })
  .sort({ 
    'rating.average': -1, 
    'statistics.totalDonations': -1,
    'statistics.totalWeight': -1
  })
  .limit(limit);
};

module.exports = mongoose.model('CharityPartner', charityPartnerSchema);
