/**
 * Official list of Kenyan counties
 * This is the authoritative source for county names used throughout the backend
 * to ensure consistency and avoid duplicates.
 */
export const KENYAN_COUNTIES = [
  'Baringo',
  'Bomet',
  'Bungoma',
  'Busia',
  'Elgeyo Marakwet',
  'Em<PERSON>',
  '<PERSON><PERSON><PERSON>',
  'Homa Bay',
  'Isiolo',
  'Ka<PERSON><PERSON>',
  'Kakamega',
  'Kericho',
  'Kiam<PERSON>',
  '<PERSON><PERSON><PERSON>',
  'Kirinya<PERSON>',
  'Ki<PERSON><PERSON>',
  '<PERSON><PERSON><PERSON>',
  'Kitui',
  'Kwale',
  'Laikipia',
  'Lamu',
  'Machakos',
  'Maku<PERSON>',
  'Mandera',
  'Marsabit',
  '<PERSON><PERSON>',
  'Migori',
  'Mombasa',
  'Murang\'a',
  'Nairobi',
  'Na<PERSON><PERSON>',
  'Nandi',
  'Narok',
  'Nyamira',
  'Nyandarua',
  'Nyeri',
  'Samburu',
  'Siaya',
  'Taita Taveta',
  'Tana River',
  'Tharaka Nithi',
  'Trans Nzoia',
  'Turkana',
  '<PERSON><PERSON><PERSON>',
  '<PERSON><PERSON><PERSON>',
  '<PERSON><PERSON><PERSON>',
  'West Pokot'
] as const;

/**
 * Type for Kenyan county names
 */
export type KenyanCounty = typeof KENYAN_COUNTIES[number];

/**
 * Helper function to check if a string is a valid Kenyan county
 */
export const isValidKenyanCounty = (county: string): county is KenyanCounty => {
  return KENYAN_COUNTIES.includes(county as KenyanCounty);
};

/**
 * Get counties sorted alphabetically (already sorted in the main array)
 */
export const getSortedCounties = (): readonly string[] => {
  return KENYAN_COUNTIES;
};
