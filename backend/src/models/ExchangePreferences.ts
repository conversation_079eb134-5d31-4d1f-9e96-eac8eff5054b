import mongoose, { Document, Schema } from 'mongoose';

export interface IExchangePreferences extends Document {
  _id: mongoose.Types.ObjectId;
  user: mongoose.Types.ObjectId;
  
  // General exchange settings
  autoAcceptSwaps: boolean;
  autoAcceptTokenPurchases: boolean;
  autoAcceptDonations: boolean;
  
  // Minimum requirements
  minimumTokenOffer: number;
  minimumRatingRequired: number;
  
  // Preferred exchange types
  preferredExchangeTypes: ('swap' | 'token_purchase' | 'donation')[];
  
  // Delivery preferences
  preferredDeliveryMethods: ('pickup' | 'delivery' | 'meetup')[];
  maxDeliveryDistance: number; // in kilometers
  deliveryFeeWillingness: number; // maximum willing to pay for delivery
  
  // Availability
  availableDays: ('monday' | 'tuesday' | 'wednesday' | 'thursday' | 'friday' | 'saturday' | 'sunday')[];
  availableTimeSlots: {
    start: string; // HH:MM format
    end: string;   // HH:MM format
  }[];
  
  // Quality standards
  minimumConditionAccepted: 'poor' | 'fair' | 'good' | 'very_good' | 'excellent';
  requirePhotos: boolean;
  requireDetailedDescription: boolean;
  
  // Communication preferences
  allowDirectMessages: boolean;
  notifyOnNewOffers: boolean;
  notifyOnCounterOffers: boolean;
  notifyOnOfferExpiry: boolean;
  
  // Charity preferences (for donations)
  preferredCharityCategories: string[];
  requireCharityReceipt: boolean;
  
  // Blocked users
  blockedUsers: mongoose.Types.ObjectId[];
  
  // Exchange history limits
  maxActiveOffers: number;
  maxActiveTransactions: number;
  
  // Special requirements
  requireMeetupInPublicPlace: boolean;
  requireIdentityVerification: boolean;
  
  createdAt: Date;
  updatedAt: Date;
}

const exchangePreferencesSchema = new Schema<IExchangePreferences>({
  user: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    unique: true,
  },
  autoAcceptSwaps: {
    type: Boolean,
    default: false,
  },
  autoAcceptTokenPurchases: {
    type: Boolean,
    default: false,
  },
  autoAcceptDonations: {
    type: Boolean,
    default: true,
  },
  minimumTokenOffer: {
    type: Number,
    default: 10,
    min: 0,
  },
  minimumRatingRequired: {
    type: Number,
    default: 3.0,
    min: 1.0,
    max: 5.0,
  },
  preferredExchangeTypes: [{
    type: String,
    enum: ['swap', 'token_purchase', 'donation'],
  }],
  preferredDeliveryMethods: [{
    type: String,
    enum: ['pickup', 'delivery', 'meetup'],
  }],
  maxDeliveryDistance: {
    type: Number,
    default: 50, // 50km
    min: 1,
    max: 500,
  },
  deliveryFeeWillingness: {
    type: Number,
    default: 200, // 200 KES
    min: 0,
  },
  availableDays: [{
    type: String,
    enum: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'],
  }],
  availableTimeSlots: [{
    start: {
      type: String,
      required: true,
      match: /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, // HH:MM format
    },
    end: {
      type: String,
      required: true,
      match: /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, // HH:MM format
    },
  }],
  minimumConditionAccepted: {
    type: String,
    enum: ['poor', 'fair', 'good', 'very_good', 'excellent'],
    default: 'good',
  },
  requirePhotos: {
    type: Boolean,
    default: true,
  },
  requireDetailedDescription: {
    type: Boolean,
    default: true,
  },
  allowDirectMessages: {
    type: Boolean,
    default: true,
  },
  notifyOnNewOffers: {
    type: Boolean,
    default: true,
  },
  notifyOnCounterOffers: {
    type: Boolean,
    default: true,
  },
  notifyOnOfferExpiry: {
    type: Boolean,
    default: true,
  },
  preferredCharityCategories: [{
    type: String,
    trim: true,
  }],
  requireCharityReceipt: {
    type: Boolean,
    default: false,
  },
  blockedUsers: [{
    type: Schema.Types.ObjectId,
    ref: 'User',
  }],
  maxActiveOffers: {
    type: Number,
    default: 10,
    min: 1,
    max: 50,
  },
  maxActiveTransactions: {
    type: Number,
    default: 5,
    min: 1,
    max: 20,
  },
  requireMeetupInPublicPlace: {
    type: Boolean,
    default: true,
  },
  requireIdentityVerification: {
    type: Boolean,
    default: false,
  },
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true },
});

// Indexes
exchangePreferencesSchema.index({ user: 1 });
exchangePreferencesSchema.index({ preferredExchangeTypes: 1 });
exchangePreferencesSchema.index({ minimumRatingRequired: 1 });
exchangePreferencesSchema.index({ maxDeliveryDistance: 1 });

// Virtual for checking if user is available now
exchangePreferencesSchema.virtual('isAvailableNow').get(function() {
  const now = new Date();
  const currentDay = now.toLocaleDateString('en-US', { weekday: 'lowercase' });
  const currentTime = now.toTimeString().slice(0, 5); // HH:MM format
  
  // Check if today is in available days
  if (!this.availableDays.includes(currentDay as any)) {
    return false;
  }
  
  // Check if current time is in any available time slot
  return this.availableTimeSlots.some(slot => {
    return currentTime >= slot.start && currentTime <= slot.end;
  });
});

// Method to check if user accepts a specific exchange type
exchangePreferencesSchema.methods.acceptsExchangeType = function(type: string) {
  return this.preferredExchangeTypes.includes(type);
};

// Method to check if user accepts a specific delivery method
exchangePreferencesSchema.methods.acceptsDeliveryMethod = function(method: string) {
  return this.preferredDeliveryMethods.includes(method);
};

// Method to check if user is blocked
exchangePreferencesSchema.methods.isUserBlocked = function(userId: mongoose.Types.ObjectId) {
  return this.blockedUsers.some(blockedId => blockedId.equals(userId));
};

// Method to check if user meets minimum rating requirement
exchangePreferencesSchema.methods.meetsRatingRequirement = function(userRating: number) {
  return userRating >= this.minimumRatingRequired;
};

// Static method to get default preferences
exchangePreferencesSchema.statics.getDefaultPreferences = function(userId: mongoose.Types.ObjectId) {
  return {
    user: userId,
    autoAcceptSwaps: false,
    autoAcceptTokenPurchases: false,
    autoAcceptDonations: true,
    minimumTokenOffer: 10,
    minimumRatingRequired: 3.0,
    preferredExchangeTypes: ['swap', 'token_purchase', 'donation'],
    preferredDeliveryMethods: ['pickup', 'meetup'],
    maxDeliveryDistance: 50,
    deliveryFeeWillingness: 200,
    availableDays: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'],
    availableTimeSlots: [
      { start: '09:00', end: '17:00' }
    ],
    minimumConditionAccepted: 'good',
    requirePhotos: true,
    requireDetailedDescription: true,
    allowDirectMessages: true,
    notifyOnNewOffers: true,
    notifyOnCounterOffers: true,
    notifyOnOfferExpiry: true,
    preferredCharityCategories: [],
    requireCharityReceipt: false,
    blockedUsers: [],
    maxActiveOffers: 10,
    maxActiveTransactions: 5,
    requireMeetupInPublicPlace: true,
    requireIdentityVerification: false,
  };
};

export default mongoose.model<IExchangePreferences>('ExchangePreferences', exchangePreferencesSchema);
