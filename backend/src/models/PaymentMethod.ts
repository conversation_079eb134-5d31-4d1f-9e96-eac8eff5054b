import mongoose, { Document, Schema } from 'mongoose';

export interface IPaymentMethod extends Document {
  _id: mongoose.Types.ObjectId;
  
  // User reference
  user: mongoose.Types.ObjectId;
  
  // Payment method details
  type: 'mpesa' | 'card' | 'bank_account';
  provider: 'safaricom' | 'visa' | 'mastercard' | 'equity' | 'kcb' | 'coop' | 'other';
  
  // Method identification
  name: string; // User-friendly name like "My M-Pesa" or "Work Card"
  isDefault: boolean;
  isActive: boolean;
  
  // M-Pesa specific details
  mpesaDetails?: {
    phoneNumber: string;
    accountName?: string;
    isVerified: boolean;
    lastVerifiedAt?: Date;
  };
  
  // Card specific details (for future implementation)
  cardDetails?: {
    lastFourDigits: string;
    expiryMonth: number;
    expiryYear: number;
    cardholderName: string;
    brand: 'visa' | 'mastercard' | 'amex';
    isVerified: boolean;
    lastVerifiedAt?: Date;
  };
  
  // Bank account details (for future implementation)
  bankDetails?: {
    bankName: string;
    accountNumber: string; // Encrypted
    accountName: string;
    branchCode?: string;
    isVerified: boolean;
    lastVerifiedAt?: Date;
  };
  
  // Usage statistics
  stats: {
    totalTransactions: number;
    totalAmount: number;
    lastUsedAt?: Date;
    successRate: number; // Percentage of successful transactions
  };
  
  // Security and verification
  verificationStatus: 'pending' | 'verified' | 'failed' | 'expired';
  verificationAttempts: number;
  lastVerificationAttempt?: Date;
  
  // Metadata
  metadata?: {
    [key: string]: any;
  };
  
  createdAt: Date;
  updatedAt: Date;
}

const paymentMethodSchema = new Schema<IPaymentMethod>({
  user: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true,
  },
  type: {
    type: String,
    required: true,
    enum: ['mpesa', 'card', 'bank_account'],
    index: true,
  },
  provider: {
    type: String,
    required: true,
    enum: ['safaricom', 'visa', 'mastercard', 'equity', 'kcb', 'coop', 'other'],
  },
  name: {
    type: String,
    required: true,
    trim: true,
    maxlength: 50,
  },
  isDefault: {
    type: Boolean,
    default: false,
    index: true,
  },
  isActive: {
    type: Boolean,
    default: true,
    index: true,
  },
  mpesaDetails: {
    phoneNumber: {
      type: String,
      match: [/^254[17]\d{8}$/, 'Please enter a valid Kenyan phone number'],
      sparse: true,
      index: true,
    },
    accountName: {
      type: String,
      trim: true,
    },
    isVerified: {
      type: Boolean,
      default: false,
    },
    lastVerifiedAt: Date,
  },
  cardDetails: {
    lastFourDigits: {
      type: String,
      match: [/^\d{4}$/, 'Last four digits must be exactly 4 numbers'],
    },
    expiryMonth: {
      type: Number,
      min: 1,
      max: 12,
    },
    expiryYear: {
      type: Number,
      min: new Date().getFullYear(),
    },
    cardholderName: {
      type: String,
      trim: true,
      uppercase: true,
    },
    brand: {
      type: String,
      enum: ['visa', 'mastercard', 'amex'],
    },
    isVerified: {
      type: Boolean,
      default: false,
    },
    lastVerifiedAt: Date,
  },
  bankDetails: {
    bankName: {
      type: String,
      trim: true,
    },
    accountNumber: {
      type: String,
      // This should be encrypted in production
    },
    accountName: {
      type: String,
      trim: true,
    },
    branchCode: {
      type: String,
      trim: true,
    },
    isVerified: {
      type: Boolean,
      default: false,
    },
    lastVerifiedAt: Date,
  },
  stats: {
    totalTransactions: {
      type: Number,
      default: 0,
      min: 0,
    },
    totalAmount: {
      type: Number,
      default: 0,
      min: 0,
    },
    lastUsedAt: Date,
    successRate: {
      type: Number,
      default: 0,
      min: 0,
      max: 100,
    },
  },
  verificationStatus: {
    type: String,
    required: true,
    enum: ['pending', 'verified', 'failed', 'expired'],
    default: 'pending',
    index: true,
  },
  verificationAttempts: {
    type: Number,
    default: 0,
    min: 0,
  },
  lastVerificationAttempt: Date,
  metadata: {
    type: Schema.Types.Mixed,
  },
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true },
});

// Compound indexes
paymentMethodSchema.index({ user: 1, type: 1 });
paymentMethodSchema.index({ user: 1, isDefault: 1 });
paymentMethodSchema.index({ user: 1, isActive: 1 });
paymentMethodSchema.index({ type: 1, provider: 1 });

// Virtual for display name
paymentMethodSchema.virtual('displayName').get(function() {
  if (this.type === 'mpesa' && this.mpesaDetails?.phoneNumber) {
    const phone = this.mpesaDetails.phoneNumber;
    return `${this.name} (${phone.slice(0, 6)}***${phone.slice(-2)})`;
  }
  if (this.type === 'card' && this.cardDetails?.lastFourDigits) {
    return `${this.name} (****${this.cardDetails.lastFourDigits})`;
  }
  return this.name;
});

// Virtual for is expired (for cards)
paymentMethodSchema.virtual('isExpired').get(function() {
  if (this.type === 'card' && this.cardDetails) {
    const now = new Date();
    const expiry = new Date(this.cardDetails.expiryYear, this.cardDetails.expiryMonth - 1);
    return now > expiry;
  }
  return false;
});

// Pre-save middleware to ensure only one default payment method per user
paymentMethodSchema.pre('save', async function(next) {
  if (this.isDefault && this.isModified('isDefault')) {
    // Remove default flag from other payment methods for this user
    await this.constructor.updateMany(
      { user: this.user, _id: { $ne: this._id } },
      { $set: { isDefault: false } }
    );
  }
  next();
});

// Static method to get user's default payment method
paymentMethodSchema.statics.getDefaultForUser = function(userId: mongoose.Types.ObjectId) {
  return this.findOne({ user: userId, isDefault: true, isActive: true });
};

// Instance method to update usage stats
paymentMethodSchema.methods.updateUsageStats = function(amount: number, success: boolean) {
  this.stats.totalTransactions += 1;
  if (success) {
    this.stats.totalAmount += amount;
  }
  this.stats.lastUsedAt = new Date();
  
  // Calculate success rate
  const successfulTransactions = Math.round(this.stats.totalTransactions * (this.stats.successRate / 100));
  const newSuccessfulTransactions = success ? successfulTransactions + 1 : successfulTransactions;
  this.stats.successRate = Math.round((newSuccessfulTransactions / this.stats.totalTransactions) * 100);
  
  return this.save();
};

export default mongoose.model<IPaymentMethod>('PaymentMethod', paymentMethodSchema);
