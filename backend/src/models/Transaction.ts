import mongoose, { Document, Schema } from 'mongoose';

export interface ITransaction extends Document {
  _id: mongoose.Types.ObjectId;
  type: 'swap' | 'token_purchase' | 'donation';
  status: 'pending' | 'accepted' | 'completed' | 'cancelled' | 'disputed';
  
  // Participants
  initiator: mongoose.Types.ObjectId;
  recipient?: mongoose.Types.ObjectId; // Not applicable for donations
  
  // Items involved
  initiatorItems: mongoose.Types.ObjectId[];
  recipientItems?: mongoose.Types.ObjectId[]; // Not applicable for token purchases or donations
  
  // Token information
  tokenAmount?: number;
  
  // Delivery information
  deliveryMethod: 'pickup' | 'delivery' | 'meetup';
  deliveryAddress?: {
    county: string;
    town: string;
    specificLocation: string;
    contactPhone: string;
  };
  meetupLocation?: {
    name: string;
    address: string;
    coordinates?: {
      latitude: number;
      longitude: number;
    };
    scheduledTime: Date;
  };
  
  // Payment information (for platform fees)
  platformFee?: {
    amount: number;
    currency: string;
    mpesaTransactionId?: string;
    status: 'pending' | 'completed' | 'failed';
  };
  
  // Charity information (for donations)
  charityPartner?: {
    name: string;
    id: mongoose.Types.ObjectId;
    category: string;
  };
  
  // Communication
  chatId?: mongoose.Types.ObjectId;
  
  // Tracking
  timeline: {
    status: string;
    timestamp: Date;
    note?: string;
    updatedBy: mongoose.Types.ObjectId;
  }[];
  
  // Ratings and feedback
  ratings?: {
    initiatorRating?: {
      score: number;
      comment?: string;
      ratedAt: Date;
    };
    recipientRating?: {
      score: number;
      comment?: string;
      ratedAt: Date;
    };
  };
  
  // Dispute information
  dispute?: {
    reason: string;
    description: string;
    reportedBy: mongoose.Types.ObjectId;
    reportedAt: Date;
    status: 'open' | 'investigating' | 'resolved' | 'closed';
    resolution?: string;
    resolvedBy?: mongoose.Types.ObjectId;
    resolvedAt?: Date;
  };
  
  createdAt: Date;
  updatedAt: Date;
}

const transactionSchema = new Schema<ITransaction>({
  type: {
    type: String,
    required: true,
    enum: ['swap', 'token_purchase', 'donation'],
  },
  status: {
    type: String,
    required: true,
    enum: ['pending', 'accepted', 'completed', 'cancelled', 'disputed'],
    default: 'pending',
  },
  initiator: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  recipient: {
    type: Schema.Types.ObjectId,
    ref: 'User',
  },
  initiatorItems: [{
    type: Schema.Types.ObjectId,
    ref: 'ClothingItem',
    required: true,
  }],
  recipientItems: [{
    type: Schema.Types.ObjectId,
    ref: 'ClothingItem',
  }],
  tokenAmount: {
    type: Number,
    min: 0,
  },
  deliveryMethod: {
    type: String,
    required: true,
    enum: ['pickup', 'delivery', 'meetup'],
  },
  deliveryAddress: {
    county: String,
    town: String,
    specificLocation: String,
    contactPhone: String,
  },
  meetupLocation: {
    name: String,
    address: String,
    coordinates: {
      latitude: Number,
      longitude: Number,
    },
    scheduledTime: Date,
  },
  platformFee: {
    amount: {
      type: Number,
      min: 0,
    },
    currency: {
      type: String,
      default: 'KES',
    },
    mpesaTransactionId: String,
    status: {
      type: String,
      enum: ['pending', 'completed', 'failed'],
      default: 'pending',
    },
  },
  charityPartner: {
    name: String,
    id: {
      type: Schema.Types.ObjectId,
      ref: 'CharityPartner',
    },
    category: String,
  },
  chatId: {
    type: Schema.Types.ObjectId,
    ref: 'Chat',
  },
  timeline: [{
    status: {
      type: String,
      required: true,
    },
    timestamp: {
      type: Date,
      required: true,
      default: Date.now,
    },
    note: String,
    updatedBy: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true,
    },
  }],
  ratings: {
    initiatorRating: {
      score: {
        type: Number,
        min: 1,
        max: 5,
      },
      comment: String,
      ratedAt: Date,
    },
    recipientRating: {
      score: {
        type: Number,
        min: 1,
        max: 5,
      },
      comment: String,
      ratedAt: Date,
    },
  },
  dispute: {
    reason: String,
    description: String,
    reportedBy: {
      type: Schema.Types.ObjectId,
      ref: 'User',
    },
    reportedAt: Date,
    status: {
      type: String,
      enum: ['open', 'investigating', 'resolved', 'closed'],
    },
    resolution: String,
    resolvedBy: {
      type: Schema.Types.ObjectId,
      ref: 'User',
    },
    resolvedAt: Date,
  },
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true },
});

// Indexes
transactionSchema.index({ initiator: 1 });
transactionSchema.index({ recipient: 1 });
transactionSchema.index({ type: 1 });
transactionSchema.index({ status: 1 });
transactionSchema.index({ createdAt: -1 });
transactionSchema.index({ 'platformFee.status': 1 });
transactionSchema.index({ 'dispute.status': 1 });

// Virtual for transaction duration
transactionSchema.virtual('duration').get(function() {
  if (this.status === 'completed') {
    const completedEntry = this.timeline.find(entry => entry.status === 'completed');
    if (completedEntry) {
      return completedEntry.timestamp.getTime() - this.createdAt.getTime();
    }
  }
  return null;
});

// Pre-save middleware to add timeline entry
transactionSchema.pre('save', function(next) {
  if (this.isModified('status') && !this.isNew) {
    this.timeline.push({
      status: this.status,
      timestamp: new Date(),
      updatedBy: this.initiator, // This should be set by the controller
    });
  }
  next();
});

export default mongoose.model<ITransaction>('Transaction', transactionSchema);
