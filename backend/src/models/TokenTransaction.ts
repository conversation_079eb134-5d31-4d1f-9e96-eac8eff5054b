import mongoose, { Document, Schema } from 'mongoose';

export interface ITokenTransaction extends Document {
  _id: mongoose.Types.ObjectId;
  user: mongoose.Types.ObjectId;
  type: 'earn' | 'spend' | 'bonus' | 'refund' | 'penalty';
  amount: number;
  balance: number; // Balance after this transaction
  source: {
    type: 'listing' | 'donation' | 'swap' | 'purchase' | 'referral' | 'welcome' | 'daily_login' | 'review' | 'admin';
    referenceId?: mongoose.Types.ObjectId; // Reference to related transaction, listing, etc.
    description: string;
  };
  metadata?: {
    [key: string]: any;
  };
  createdAt: Date;
}

const tokenTransactionSchema = new Schema<ITokenTransaction>({
  user: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  type: {
    type: String,
    required: true,
    enum: ['earn', 'spend', 'bonus', 'refund', 'penalty'],
  },
  amount: {
    type: Number,
    required: true,
  },
  balance: {
    type: Number,
    required: true,
    min: 0,
  },
  source: {
    type: {
      type: String,
      required: true,
      enum: ['listing', 'donation', 'swap', 'purchase', 'referral', 'welcome', 'daily_login', 'review', 'admin'],
    },
    referenceId: {
      type: Schema.Types.ObjectId,
    },
    description: {
      type: String,
      required: true,
      trim: true,
    },
  },
  metadata: {
    type: Schema.Types.Mixed,
  },
}, {
  timestamps: { createdAt: true, updatedAt: false },
  toJSON: { virtuals: true },
  toObject: { virtuals: true },
});

// Indexes
tokenTransactionSchema.index({ user: 1, createdAt: -1 });
tokenTransactionSchema.index({ type: 1 });
tokenTransactionSchema.index({ 'source.type': 1 });
tokenTransactionSchema.index({ 'source.referenceId': 1 });

export default mongoose.model<ITokenTransaction>('TokenTransaction', tokenTransactionSchema);
