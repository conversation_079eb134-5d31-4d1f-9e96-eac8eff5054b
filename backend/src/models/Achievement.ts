import mongoose, { Document, Schema } from 'mongoose';

export interface IAchievement extends Document {
  _id: mongoose.Types.ObjectId;
  name: string;
  description: string;
  icon: string;
  category: 'tokens' | 'activity' | 'sustainability' | 'social' | 'time';
  type: 'bronze' | 'silver' | 'gold' | 'platinum' | 'special';
  requirements: {
    condition: string;
    value: number;
    operator: 'gte' | 'lte' | 'eq' | 'gt' | 'lt';
  };
  rewards: {
    tokens: number;
    badge?: string;
    title?: string;
  };
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

const achievementSchema = new Schema<IAchievement>({
  name: {
    type: String,
    required: true,
    unique: true,
    trim: true
  },
  description: {
    type: String,
    required: true,
    trim: true
  },
  icon: {
    type: String,
    required: true,
    trim: true
  },
  category: {
    type: String,
    enum: ['tokens', 'activity', 'sustainability', 'social', 'time'],
    required: true
  },
  type: {
    type: String,
    enum: ['bronze', 'silver', 'gold', 'platinum', 'special'],
    required: true
  },
  requirements: {
    condition: {
      type: String,
      required: true,
      trim: true
    },
    value: {
      type: Number,
      required: true,
      min: 0
    },
    operator: {
      type: String,
      enum: ['gte', 'lte', 'eq', 'gt', 'lt'],
      required: true
    }
  },
  rewards: {
    tokens: {
      type: Number,
      required: true,
      min: 0
    },
    badge: {
      type: String,
      trim: true
    },
    title: {
      type: String,
      trim: true
    }
  },
  isActive: {
    type: Boolean,
    default: true
  }
}, {
  timestamps: true
});

// Indexes
achievementSchema.index({ category: 1, type: 1 });
achievementSchema.index({ isActive: 1 });

export default mongoose.model<IAchievement>('Achievement', achievementSchema);
