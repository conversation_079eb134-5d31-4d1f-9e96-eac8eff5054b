import mongoose, { Document, Schema } from 'mongoose';

export interface ICharityPartner extends Document {
  _id: mongoose.Types.ObjectId;
  name: string;
  description: string;
  category: string;
  contactInfo: {
    email: string;
    phone: string;
    website?: string;
    address: {
      county: string;
      town: string;
      specificLocation: string;
    };
  };
  logo?: string;
  isActive: boolean;
  acceptedItemTypes: string[];
  requirements: {
    condition: string[];
    categories: string[];
    notes?: string;
  };
  stats: {
    totalDonationsReceived: number;
    totalItemsReceived: number;
    totalBeneficiaries: number;
    averageRating: number;
    totalRatings: number;
    responseTime: number; // in hours
  };
  verificationStatus: 'pending' | 'verified' | 'suspended';
  verifiedBy?: mongoose.Types.ObjectId;
  verificationDate?: Date;

  // Token rewards for donations
  tokenRewards: {
    baseReward: number;
    qualityMultiplier: {
      poor: number;
      fair: number;
      good: number;
      very_good: number;
      excellent: number;
    };
  };

  // Operational settings
  settings: {
    autoAcceptDonations: boolean;
    requireQualityCheck: boolean;
    providesPickup: boolean;
    maxPickupDistance: number; // in kilometers
    issuesReceipts: boolean;
  };
  createdAt: Date;
  updatedAt: Date;
}

const charityPartnerSchema = new Schema<ICharityPartner>({
  name: {
    type: String,
    required: true,
    trim: true,
    maxlength: 100,
  },
  description: {
    type: String,
    required: true,
    trim: true,
    maxlength: 500,
  },
  category: {
    type: String,
    required: true,
    enum: [
      'Children & Youth',
      'Women Empowerment',
      'Education',
      'Healthcare',
      'Environment',
      'Community Development',
      'Disaster Relief',
      'Elderly Care',
      'Disability Support',
      'General Welfare'
    ],
  },
  contactInfo: {
    email: {
      type: String,
      required: true,
      lowercase: true,
      match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, 'Please enter a valid email'],
    },
    phone: {
      type: String,
      required: true,
      match: [/^(\+254|0)[17]\d{8}$/, 'Please enter a valid Kenyan phone number'],
    },
    website: {
      type: String,
      trim: true,
    },
    address: {
      county: {
        type: String,
        required: true,
      },
      town: {
        type: String,
        required: true,
      },
      specificLocation: {
        type: String,
        required: true,
      },
    },
  },
  logo: {
    type: String,
  },
  isActive: {
    type: Boolean,
    default: true,
  },
  acceptedItemTypes: [{
    type: String,
    enum: [
      'Tops', 'Bottoms', 'Dresses', 'Outerwear', 'Shoes', 'Accessories',
      'Formal', 'Casual', 'Sportswear', 'Traditional', 'Children Clothing',
      'Baby Items', 'School Uniforms'
    ],
  }],
  requirements: {
    condition: [{
      type: String,
      enum: ['New', 'Like New', 'Good', 'Fair'],
    }],
    categories: [{
      type: String,
      enum: [
        'Tops', 'Bottoms', 'Dresses', 'Outerwear', 'Shoes', 'Accessories',
        'Formal', 'Casual', 'Sportswear', 'Traditional', 'Children Clothing'
      ],
    }],
    notes: {
      type: String,
      trim: true,
    },
  },
  stats: {
    totalDonationsReceived: {
      type: Number,
      default: 0,
      min: 0,
    },
    totalItemsReceived: {
      type: Number,
      default: 0,
      min: 0,
    },
    totalBeneficiaries: {
      type: Number,
      default: 0,
      min: 0,
    },
    averageRating: {
      type: Number,
      default: 0,
      min: 0,
      max: 5,
    },
    totalRatings: {
      type: Number,
      default: 0,
      min: 0,
    },
    responseTime: {
      type: Number,
      default: 24,
      min: 0,
    },
  },
  tokenRewards: {
    baseReward: {
      type: Number,
      default: 50,
      min: 0,
    },
    qualityMultiplier: {
      poor: {
        type: Number,
        default: 0.5,
        min: 0,
      },
      fair: {
        type: Number,
        default: 0.7,
        min: 0,
      },
      good: {
        type: Number,
        default: 1.0,
        min: 0,
      },
      very_good: {
        type: Number,
        default: 1.3,
        min: 0,
      },
      excellent: {
        type: Number,
        default: 1.5,
        min: 0,
      },
    },
  },
  settings: {
    autoAcceptDonations: {
      type: Boolean,
      default: true,
    },
    requireQualityCheck: {
      type: Boolean,
      default: false,
    },
    providesPickup: {
      type: Boolean,
      default: true,
    },
    maxPickupDistance: {
      type: Number,
      default: 30,
      min: 0,
    },
    issuesReceipts: {
      type: Boolean,
      default: true,
    },
  },
  verificationStatus: {
    type: String,
    enum: ['pending', 'verified', 'suspended'],
    default: 'pending',
  },
  verifiedBy: {
    type: Schema.Types.ObjectId,
    ref: 'User',
  },
  verificationDate: {
    type: Date,
  },
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true },
});

// Indexes
charityPartnerSchema.index({ name: 1 });
charityPartnerSchema.index({ category: 1 });
charityPartnerSchema.index({ isActive: 1 });
charityPartnerSchema.index({ verificationStatus: 1 });
charityPartnerSchema.index({ 'contactInfo.address.county': 1 });
charityPartnerSchema.index({ acceptedItemTypes: 1 });

// Method to calculate token reward for donation
charityPartnerSchema.methods.calculateTokenReward = function(itemCondition: string) {
  let reward = this.tokenRewards.baseReward;

  // Apply quality multiplier
  const qualityMultiplier = this.tokenRewards.qualityMultiplier[itemCondition as keyof typeof this.tokenRewards.qualityMultiplier];
  if (qualityMultiplier) {
    reward *= qualityMultiplier;
  }

  return Math.round(reward);
};

// Method to check if item type is accepted
charityPartnerSchema.methods.acceptsItemType = function(itemType: string) {
  return this.acceptedItemTypes.includes(itemType);
};

export default mongoose.model<ICharityPartner>('CharityPartner', charityPartnerSchema);
