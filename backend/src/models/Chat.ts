import mongoose, { Document, Schema } from 'mongoose';

export interface IMessage {
  sender: mongoose.Types.ObjectId;
  content: string;
  type: 'text' | 'image' | 'system';
  timestamp: Date;
  isRead: boolean;
}

export interface IChat extends Document {
  _id: mongoose.Types.ObjectId;
  participants: mongoose.Types.ObjectId[];
  relatedTransaction?: mongoose.Types.ObjectId;
  relatedClothingItem?: mongoose.Types.ObjectId;
  messages: IMessage[];
  lastMessage?: {
    content: string;
    sender: mongoose.Types.ObjectId;
    timestamp: Date;
  };
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

const messageSchema = new Schema<IMessage>({
  sender: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  content: {
    type: String,
    required: true,
    trim: true,
    maxlength: 1000,
  },
  type: {
    type: String,
    enum: ['text', 'image', 'system'],
    default: 'text',
  },
  timestamp: {
    type: Date,
    default: Date.now,
  },
  isRead: {
    type: Boolean,
    default: false,
  },
});

const chatSchema = new Schema<IChat>({
  participants: [{
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  }],
  relatedTransaction: {
    type: Schema.Types.ObjectId,
    ref: 'Transaction',
  },
  relatedClothingItem: {
    type: Schema.Types.ObjectId,
    ref: 'ClothingItem',
  },
  messages: [messageSchema],
  lastMessage: {
    content: String,
    sender: {
      type: Schema.Types.ObjectId,
      ref: 'User',
    },
    timestamp: Date,
  },
  isActive: {
    type: Boolean,
    default: true,
  },
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true },
});

// Indexes
chatSchema.index({ participants: 1 });
chatSchema.index({ relatedTransaction: 1 });
chatSchema.index({ relatedClothingItem: 1 });
chatSchema.index({ 'lastMessage.timestamp': -1 });
chatSchema.index({ isActive: 1 });

// Virtual for unread message count per user
chatSchema.virtual('unreadCount').get(function() {
  // This would need to be calculated per user in the application logic
  return 0;
});

export default mongoose.model<IChat>('Chat', chatSchema);
