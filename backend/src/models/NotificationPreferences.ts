import mongoose, { Document, Schema } from 'mongoose';

export interface INotificationPreferences extends Document {
  _id: mongoose.Types.ObjectId;
  user: mongoose.Types.ObjectId;
  
  // Global preferences
  isEnabled: boolean;
  
  // Channel preferences
  channels: {
    sms: {
      enabled: boolean;
      phoneNumber?: string;
    };
    push: {
      enabled: boolean;
    };
    email: {
      enabled: boolean;
      emailAddress?: string;
    };
    inApp: {
      enabled: boolean;
    };
  };
  
  // Category preferences
  categories: {
    payment: {
      enabled: boolean;
      channels: ('sms' | 'push' | 'email' | 'in_app')[];
      priority: 'low' | 'medium' | 'high' | 'urgent';
    };
    exchange: {
      enabled: boolean;
      channels: ('sms' | 'push' | 'email' | 'in_app')[];
      priority: 'low' | 'medium' | 'high' | 'urgent';
    };
    system: {
      enabled: boolean;
      channels: ('sms' | 'push' | 'email' | 'in_app')[];
      priority: 'low' | 'medium' | 'high' | 'urgent';
    };
    promotion: {
      enabled: boolean;
      channels: ('sms' | 'push' | 'email' | 'in_app')[];
      priority: 'low' | 'medium' | 'high' | 'urgent';
    };
    reminder: {
      enabled: boolean;
      channels: ('sms' | 'push' | 'email' | 'in_app')[];
      priority: 'low' | 'medium' | 'high' | 'urgent';
    };
    welcome: {
      enabled: boolean;
      channels: ('sms' | 'push' | 'email' | 'in_app')[];
      priority: 'low' | 'medium' | 'high' | 'urgent';
    };
    token: {
      enabled: boolean;
      channels: ('sms' | 'push' | 'email' | 'in_app')[];
      priority: 'low' | 'medium' | 'high' | 'urgent';
    };
    security: {
      enabled: boolean;
      channels: ('sms' | 'push' | 'email' | 'in_app')[];
      priority: 'low' | 'medium' | 'high' | 'urgent';
    };
  };
  
  // Timing preferences
  quietHours: {
    enabled: boolean;
    startTime: string; // HH:MM format
    endTime: string; // HH:MM format
    timezone: string;
  };
  
  // Frequency limits
  frequency: {
    maxPerDay: number;
    maxPerWeek: number;
    cooldownMinutes: number; // Minimum time between notifications
  };
  
  // Language and localization
  language: string;
  locale: string;
  
  // Audit
  createdAt: Date;
  updatedAt: Date;
  lastModifiedBy?: mongoose.Types.ObjectId;
}

const categoryPreferenceSchema = new Schema({
  enabled: {
    type: Boolean,
    default: true,
  },
  channels: [{
    type: String,
    enum: ['sms', 'push', 'email', 'in_app'],
  }],
  priority: {
    type: String,
    enum: ['low', 'medium', 'high', 'urgent'],
    default: 'medium',
  },
}, { _id: false });

const notificationPreferencesSchema = new Schema<INotificationPreferences>({
  user: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    unique: true,
    index: true,
  },
  
  // Global preferences
  isEnabled: {
    type: Boolean,
    default: true,
  },
  
  // Channel preferences
  channels: {
    sms: {
      enabled: {
        type: Boolean,
        default: true,
      },
      phoneNumber: {
        type: String,
        validate: {
          validator: function(v: string) {
            return !v || /^(\+254|254)[17]\d{8}$/.test(v);
          },
          message: 'Invalid phone number format',
        },
      },
    },
    push: {
      enabled: {
        type: Boolean,
        default: true,
      },
    },
    email: {
      enabled: {
        type: Boolean,
        default: false,
      },
      emailAddress: {
        type: String,
        validate: {
          validator: function(v: string) {
            return !v || /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(v);
          },
          message: 'Invalid email address format',
        },
      },
    },
    inApp: {
      enabled: {
        type: Boolean,
        default: true,
      },
    },
  },
  
  // Category preferences with defaults
  categories: {
    payment: {
      ...categoryPreferenceSchema.obj,
      enabled: { type: Boolean, default: true },
      channels: { type: [String], default: ['sms', 'in_app'] },
      priority: { type: String, default: 'high' },
    },
    exchange: {
      ...categoryPreferenceSchema.obj,
      enabled: { type: Boolean, default: true },
      channels: { type: [String], default: ['sms', 'push', 'in_app'] },
      priority: { type: String, default: 'medium' },
    },
    system: {
      ...categoryPreferenceSchema.obj,
      enabled: { type: Boolean, default: true },
      channels: { type: [String], default: ['sms', 'push', 'in_app'] },
      priority: { type: String, default: 'high' },
    },
    promotion: {
      ...categoryPreferenceSchema.obj,
      enabled: { type: Boolean, default: false },
      channels: { type: [String], default: ['push', 'in_app'] },
      priority: { type: String, default: 'low' },
    },
    reminder: {
      ...categoryPreferenceSchema.obj,
      enabled: { type: Boolean, default: true },
      channels: { type: [String], default: ['sms', 'push'] },
      priority: { type: String, default: 'medium' },
    },
    welcome: {
      ...categoryPreferenceSchema.obj,
      enabled: { type: Boolean, default: true },
      channels: { type: [String], default: ['sms', 'in_app'] },
      priority: { type: String, default: 'high' },
    },
    token: {
      ...categoryPreferenceSchema.obj,
      enabled: { type: Boolean, default: true },
      channels: { type: [String], default: ['sms', 'push', 'in_app'] },
      priority: { type: String, default: 'medium' },
    },
    security: {
      ...categoryPreferenceSchema.obj,
      enabled: { type: Boolean, default: true },
      channels: { type: [String], default: ['sms', 'push', 'in_app'] },
      priority: { type: String, default: 'urgent' },
    },
  },
  
  // Timing preferences
  quietHours: {
    enabled: {
      type: Boolean,
      default: false,
    },
    startTime: {
      type: String,
      default: '22:00',
      validate: {
        validator: function(v: string) {
          return /^([01]\d|2[0-3]):([0-5]\d)$/.test(v);
        },
        message: 'Invalid time format. Use HH:MM',
      },
    },
    endTime: {
      type: String,
      default: '07:00',
      validate: {
        validator: function(v: string) {
          return /^([01]\d|2[0-3]):([0-5]\d)$/.test(v);
        },
        message: 'Invalid time format. Use HH:MM',
      },
    },
    timezone: {
      type: String,
      default: 'Africa/Nairobi',
    },
  },
  
  // Frequency limits
  frequency: {
    maxPerDay: {
      type: Number,
      default: 10,
      min: 1,
      max: 50,
    },
    maxPerWeek: {
      type: Number,
      default: 50,
      min: 1,
      max: 200,
    },
    cooldownMinutes: {
      type: Number,
      default: 5,
      min: 1,
      max: 1440, // 24 hours
    },
  },
  
  // Language and localization
  language: {
    type: String,
    default: 'en',
    enum: ['en', 'sw'], // English and Swahili
  },
  locale: {
    type: String,
    default: 'en-KE',
  },
  
  // Audit
  lastModifiedBy: {
    type: Schema.Types.ObjectId,
    ref: 'User',
  },
}, {
  timestamps: true,
});

// Indexes
notificationPreferencesSchema.index({ user: 1 }, { unique: true });

// Instance methods
notificationPreferencesSchema.methods.canReceiveNotification = function(
  category: string, 
  channel: string, 
  priority: string = 'medium'
) {
  // Check global enabled
  if (!this.isEnabled) return false;
  
  // Check channel enabled
  if (!this.channels[channel]?.enabled) return false;
  
  // Check category enabled
  const categoryPrefs = this.categories[category];
  if (!categoryPrefs?.enabled) return false;
  
  // Check if channel is allowed for this category
  if (!categoryPrefs.channels.includes(channel)) return false;
  
  return true;
};

notificationPreferencesSchema.methods.isInQuietHours = function(date: Date = new Date()) {
  if (!this.quietHours.enabled) return false;
  
  // Convert to user's timezone
  const userTime = new Intl.DateTimeFormat('en-GB', {
    timeZone: this.quietHours.timezone,
    hour12: false,
    hour: '2-digit',
    minute: '2-digit',
  }).format(date);
  
  const [currentHour, currentMinute] = userTime.split(':').map(Number);
  const currentTimeMinutes = currentHour * 60 + currentMinute;
  
  const [startHour, startMinute] = this.quietHours.startTime.split(':').map(Number);
  const startTimeMinutes = startHour * 60 + startMinute;
  
  const [endHour, endMinute] = this.quietHours.endTime.split(':').map(Number);
  const endTimeMinutes = endHour * 60 + endMinute;
  
  // Handle overnight quiet hours (e.g., 22:00 to 07:00)
  if (startTimeMinutes > endTimeMinutes) {
    return currentTimeMinutes >= startTimeMinutes || currentTimeMinutes <= endTimeMinutes;
  } else {
    return currentTimeMinutes >= startTimeMinutes && currentTimeMinutes <= endTimeMinutes;
  }
};

notificationPreferencesSchema.methods.checkFrequencyLimits = async function() {
  const now = new Date();
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
  const thisWeek = new Date(today.getTime() - (today.getDay() * 24 * 60 * 60 * 1000));
  const cooldownTime = new Date(now.getTime() - (this.frequency.cooldownMinutes * 60 * 1000));
  
  // Import here to avoid circular dependency
  const Notification = mongoose.model('Notification');
  
  // Check daily limit
  const todayCount = await Notification.countDocuments({
    user: this.user,
    createdAt: { $gte: today },
    status: { $in: ['sent', 'delivered'] },
  });
  
  if (todayCount >= this.frequency.maxPerDay) {
    return { 
      canSend: false, 
      reason: `Daily limit of ${this.frequency.maxPerDay} notifications reached` 
    };
  }
  
  // Check weekly limit
  const weekCount = await Notification.countDocuments({
    user: this.user,
    createdAt: { $gte: thisWeek },
    status: { $in: ['sent', 'delivered'] },
  });
  
  if (weekCount >= this.frequency.maxPerWeek) {
    return { 
      canSend: false, 
      reason: `Weekly limit of ${this.frequency.maxPerWeek} notifications reached` 
    };
  }
  
  // Check cooldown
  const recentCount = await Notification.countDocuments({
    user: this.user,
    createdAt: { $gte: cooldownTime },
    status: { $in: ['sent', 'delivered'] },
  });
  
  if (recentCount > 0) {
    return { 
      canSend: false, 
      reason: `Cooldown period of ${this.frequency.cooldownMinutes} minutes not elapsed` 
    };
  }
  
  return { canSend: true };
};

// Static methods
notificationPreferencesSchema.statics.getOrCreateForUser = async function(userId: string) {
  let preferences = await this.findOne({ user: userId });
  
  if (!preferences) {
    preferences = new this({ user: userId });
    await preferences.save();
  }
  
  return preferences;
};

notificationPreferencesSchema.statics.updateChannelInfo = async function(
  userId: string, 
  channel: string, 
  info: Record<string, any>
) {
  const updatePath = `channels.${channel}`;
  
  return this.findOneAndUpdate(
    { user: userId },
    { $set: { [updatePath]: { ...info } } },
    { new: true, upsert: true }
  );
};

const NotificationPreferences = mongoose.model<INotificationPreferences>('NotificationPreferences', notificationPreferencesSchema);

export default NotificationPreferences;
