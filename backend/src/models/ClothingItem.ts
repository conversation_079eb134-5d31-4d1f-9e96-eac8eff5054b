import mongoose, { Document, Schema } from 'mongoose';

export interface IClothingItem extends Document {
  _id: mongoose.Types.ObjectId;
  owner: mongoose.Types.ObjectId;
  title: string;
  description: string;
  category: string;
  subcategory?: string;
  brand?: string;
  size: string;
  color: string;
  condition: 'New' | 'Like New' | 'Good' | 'Fair' | 'Poor';
  images: string[];
  originalPrice?: number;
  tokenPrice?: number;
  isAvailable: boolean;
  exchangeType: 'swap' | 'token' | 'donation' | 'all';
  preferredSwapCategories?: string[];
  location: {
    county: string;
    town: string;
  };
  tags: string[];
  views: number;
  likes: mongoose.Types.ObjectId[];
  sustainabilityInfo: {
    material: string;
    careInstructions: string;
    estimatedLifespan: string;
    recyclingInfo?: string;
  };
  qualityAssurance?: {
    isVerified: boolean;
    verifiedBy?: mongoose.Types.ObjectId;
    verificationDate?: Date;
    notes?: string;
  };
  createdAt: Date;
  updatedAt: Date;
}

const clothingItemSchema = new Schema<IClothingItem>({
  owner: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  title: {
    type: String,
    required: true,
    trim: true,
    maxlength: 100,
  },
  description: {
    type: String,
    required: true,
    trim: true,
    maxlength: 1000,
  },
  category: {
    type: String,
    required: true,
    enum: [
      'Tops', 'Bottoms', 'Dresses', 'Outerwear', 'Shoes', 'Accessories',
      'Formal', 'Casual', 'Sportswear', 'Traditional', 'Vintage', 'Underwear'
    ],
  },
  subcategory: {
    type: String,
    trim: true,
  },
  brand: {
    type: String,
    trim: true,
    maxlength: 50,
  },
  size: {
    type: String,
    required: true,
    enum: ['XS', 'S', 'M', 'L', 'XL', 'XXL', 'XXXL', 'One Size', 'Custom'],
  },
  color: {
    type: String,
    required: true,
    trim: true,
  },
  condition: {
    type: String,
    required: true,
    enum: ['New', 'Like New', 'Good', 'Fair', 'Poor'],
  },
  images: [{
    type: String,
    required: true,
  }],
  originalPrice: {
    type: Number,
    min: 0,
  },
  tokenPrice: {
    type: Number,
    min: 0,
  },
  isAvailable: {
    type: Boolean,
    default: true,
  },
  exchangeType: {
    type: String,
    required: true,
    enum: ['swap', 'token', 'donation', 'all'],
  },
  preferredSwapCategories: [{
    type: String,
    enum: [
      'Tops', 'Bottoms', 'Dresses', 'Outerwear', 'Shoes', 'Accessories',
      'Formal', 'Casual', 'Sportswear', 'Traditional', 'Vintage'
    ],
  }],
  location: {
    county: {
      type: String,
      required: true,
    },
    town: {
      type: String,
      required: true,
    },
  },
  tags: [{
    type: String,
    trim: true,
    lowercase: true,
  }],
  views: {
    type: Number,
    default: 0,
    min: 0,
  },
  likes: [{
    type: Schema.Types.ObjectId,
    ref: 'User',
  }],
  sustainabilityInfo: {
    material: {
      type: String,
      required: true,
      trim: true,
    },
    careInstructions: {
      type: String,
      required: true,
      trim: true,
    },
    estimatedLifespan: {
      type: String,
      required: true,
      trim: true,
    },
    recyclingInfo: {
      type: String,
      trim: true,
    },
  },
  qualityAssurance: {
    isVerified: {
      type: Boolean,
      default: false,
    },
    verifiedBy: {
      type: Schema.Types.ObjectId,
      ref: 'User',
    },
    verificationDate: {
      type: Date,
    },
    notes: {
      type: String,
      trim: true,
    },
  },
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true },
});

// Indexes
clothingItemSchema.index({ owner: 1 });
clothingItemSchema.index({ category: 1, subcategory: 1 });
clothingItemSchema.index({ size: 1 });
clothingItemSchema.index({ condition: 1 });
clothingItemSchema.index({ exchangeType: 1 });
clothingItemSchema.index({ isAvailable: 1 });
clothingItemSchema.index({ 'location.county': 1, 'location.town': 1 });
clothingItemSchema.index({ tags: 1 });
clothingItemSchema.index({ createdAt: -1 });
clothingItemSchema.index({ views: -1 });
clothingItemSchema.index({ tokenPrice: 1 });

// Text search index
clothingItemSchema.index({
  title: 'text',
  description: 'text',
  brand: 'text',
  tags: 'text',
});

// Virtual for like count
clothingItemSchema.virtual('likeCount').get(function() {
  return this.likes ? this.likes.length : 0;
});

// Virtual for age in days
clothingItemSchema.virtual('ageInDays').get(function() {
  return Math.floor((Date.now() - this.createdAt.getTime()) / (1000 * 60 * 60 * 24));
});

export default mongoose.model<IClothingItem>('ClothingItem', clothingItemSchema);
