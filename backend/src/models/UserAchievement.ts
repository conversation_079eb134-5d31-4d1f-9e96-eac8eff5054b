import mongoose, { Document, Schema } from 'mongoose';

export interface IUserAchievement extends Document {
  _id: mongoose.Types.ObjectId;
  user: mongoose.Types.ObjectId;
  achievement: mongoose.Types.ObjectId;
  unlockedAt: Date;
  progress?: {
    current: number;
    target: number;
    percentage: number;
  };
  isCompleted: boolean;
  tokensAwarded: number;
  createdAt: Date;
  updatedAt: Date;
}

const userAchievementSchema = new Schema<IUserAchievement>({
  user: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  achievement: {
    type: Schema.Types.ObjectId,
    ref: 'Achievement',
    required: true
  },
  unlockedAt: {
    type: Date,
    default: Date.now
  },
  progress: {
    current: {
      type: Number,
      default: 0,
      min: 0
    },
    target: {
      type: Number,
      required: true,
      min: 1
    },
    percentage: {
      type: Number,
      default: 0,
      min: 0,
      max: 100
    }
  },
  isCompleted: {
    type: Boolean,
    default: false
  },
  tokensAwarded: {
    type: Number,
    default: 0,
    min: 0
  }
}, {
  timestamps: true
});

// Indexes
userAchievementSchema.index({ user: 1, achievement: 1 }, { unique: true });
userAchievementSchema.index({ user: 1, isCompleted: 1 });
userAchievementSchema.index({ unlockedAt: -1 });

// Virtual for completion percentage
userAchievementSchema.virtual('completionPercentage').get(function() {
  if (!this.progress) return 0;
  return Math.min(100, Math.round((this.progress.current / this.progress.target) * 100));
});

// Update percentage when progress changes
userAchievementSchema.pre('save', function(next) {
  if (this.progress) {
    this.progress.percentage = Math.min(100, Math.round((this.progress.current / this.progress.target) * 100));
    this.isCompleted = this.progress.current >= this.progress.target;
  }
  next();
});

export default mongoose.model<IUserAchievement>('UserAchievement', userAchievementSchema);
