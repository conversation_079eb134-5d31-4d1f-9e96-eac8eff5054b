import { body, param, query } from 'express-validator';

// Phone number validation for Kenya
export const validateKenyanPhone = () => 
  body('phoneNumber')
    .matches(/^(\+254|0)[17]\d{8}$/)
    .withMessage('Please enter a valid Kenyan phone number');

// Email validation
export const validateEmail = () =>
  body('email')
    .optional()
    .isEmail()
    .normalizeEmail()
    .withMessage('Please enter a valid email address');

// User registration validation
export const validateUserRegistration = [
  validateKenyanPhone(),
  validateEmail(),
  body('firstName')
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('First name must be between 2 and 50 characters'),
  body('lastName')
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('Last name must be between 2 and 50 characters'),
  body('location.county')
    .notEmpty()
    .withMessage('County is required'),
  body('location.town')
    .trim()
    .notEmpty()
    .withMessage('Town is required'),
];

// OTP validation
export const validateOTP = [
  body('phoneNumber')
    .matches(/^(\+254|0)[17]\d{8}$/)
    .withMessage('Please enter a valid Kenyan phone number'),
  body('otp')
    .isLength({ min: 6, max: 6 })
    .isNumeric()
    .withMessage('OTP must be a 6-digit number'),
];

// Clothing item validation
export const validateClothingItem = [
  body('title')
    .trim()
    .isLength({ min: 5, max: 100 })
    .withMessage('Title must be between 5 and 100 characters'),
  body('description')
    .trim()
    .isLength({ min: 20, max: 1000 })
    .withMessage('Description must be between 20 and 1000 characters'),
  body('category')
    .isIn(['Tops', 'Bottoms', 'Dresses', 'Outerwear', 'Shoes', 'Accessories', 'Formal', 'Casual', 'Sportswear', 'Traditional', 'Vintage'])
    .withMessage('Invalid category'),
  body('size')
    .isIn(['XS', 'S', 'M', 'L', 'XL', 'XXL', 'XXXL', 'One Size', 'Custom'])
    .withMessage('Invalid size'),
  body('condition')
    .isIn(['New', 'Like New', 'Good', 'Fair', 'Poor'])
    .withMessage('Invalid condition'),
  body('exchangeType')
    .isIn(['swap', 'token', 'donation', 'all'])
    .withMessage('Invalid exchange type'),
  body('color')
    .trim()
    .notEmpty()
    .withMessage('Color is required'),
  body('sustainabilityInfo.material')
    .trim()
    .notEmpty()
    .withMessage('Material information is required'),
  body('sustainabilityInfo.careInstructions')
    .trim()
    .notEmpty()
    .withMessage('Care instructions are required'),
  body('sustainabilityInfo.estimatedLifespan')
    .trim()
    .notEmpty()
    .withMessage('Estimated lifespan is required'),
];

// Transaction validation
export const validateTransaction = [
  body('type')
    .isIn(['swap', 'token_purchase', 'donation'])
    .withMessage('Invalid transaction type'),
  body('initiatorItems')
    .isArray({ min: 1 })
    .withMessage('At least one item must be included'),
  body('deliveryMethod')
    .isIn(['pickup', 'delivery', 'meetup'])
    .withMessage('Invalid delivery method'),
];

// Pagination validation
export const validatePagination = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100'),
  query('sortBy')
    .optional()
    .isString()
    .withMessage('Sort by must be a string'),
  query('sortOrder')
    .optional()
    .isIn(['asc', 'desc'])
    .withMessage('Sort order must be asc or desc'),
];

// MongoDB ObjectId validation
export const validateObjectId = (field: string) =>
  param(field)
    .isMongoId()
    .withMessage(`Invalid ${field} format`);

// Search validation
export const validateSearch = [
  query('q')
    .optional()
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('Search query must be between 1 and 100 characters'),
  query('category')
    .optional()
    .isIn(['Tops', 'Bottoms', 'Dresses', 'Outerwear', 'Shoes', 'Accessories', 'Formal', 'Casual', 'Sportswear', 'Traditional', 'Vintage'])
    .withMessage('Invalid category'),
  query('size')
    .optional()
    .isIn(['XS', 'S', 'M', 'L', 'XL', 'XXL', 'XXXL', 'One Size', 'Custom'])
    .withMessage('Invalid size'),
  query('condition')
    .optional()
    .isIn(['New', 'Like New', 'Good', 'Fair', 'Poor'])
    .withMessage('Invalid condition'),
  query('exchangeType')
    .optional()
    .isIn(['swap', 'token', 'donation', 'all'])
    .withMessage('Invalid exchange type'),
  query('county')
    .optional()
    .isString()
    .withMessage('County must be a string'),
  query('minTokens')
    .optional()
    .isInt({ min: 0 })
    .withMessage('Minimum tokens must be a non-negative integer'),
  query('maxTokens')
    .optional()
    .isInt({ min: 0 })
    .withMessage('Maximum tokens must be a non-negative integer'),
];

// Rating validation
export const validateRating = [
  body('score')
    .isInt({ min: 1, max: 5 })
    .withMessage('Rating must be between 1 and 5'),
  body('comment')
    .optional()
    .trim()
    .isLength({ max: 500 })
    .withMessage('Comment must not exceed 500 characters'),
];

// Chat message validation
export const validateChatMessage = [
  body('content')
    .trim()
    .isLength({ min: 1, max: 1000 })
    .withMessage('Message content must be between 1 and 1000 characters'),
  body('type')
    .optional()
    .isIn(['text', 'image', 'system'])
    .withMessage('Invalid message type'),
];
