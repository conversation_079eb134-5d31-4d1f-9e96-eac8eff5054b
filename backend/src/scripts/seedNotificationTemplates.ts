import mongoose from 'mongoose';
import NotificationTemplate from '@/models/NotificationTemplate';
import { logger } from '@/utils/logger';

const templates = [
  {
    name: 'payment_confirmation',
    description: 'Payment confirmation notification',
    category: 'payment',
    type: 'sms',
    title: 'Payment Confirmed',
    message: 'Your {{paymentType}} payment of KES {{amount}} has been confirmed. Payment ID: {{paymentId}}. Thank you for using Pedi! 💚',
    variables: [
      {
        name: 'paymentType',
        type: 'string',
        required: true,
        description: 'Type of payment (e.g., platform fee, token purchase)',
      },
      {
        name: 'amount',
        type: 'number',
        required: true,
        description: 'Payment amount',
      },
      {
        name: 'paymentId',
        type: 'string',
        required: true,
        description: 'Payment reference ID',
      },
    ],
    priority: 'high',
    maxAttempts: 3,
  },
  {
    name: 'payment_failure',
    description: 'Payment failure notification',
    category: 'payment',
    type: 'sms',
    title: 'Payment Failed',
    message: 'Your payment of KES {{amount}} could not be processed. {{reason}}. Please try again or contact support.',
    variables: [
      {
        name: 'amount',
        type: 'number',
        required: true,
        description: 'Payment amount',
      },
      {
        name: 'reason',
        type: 'string',
        required: false,
        description: 'Failure reason',
        defaultValue: 'Unknown error',
      },
    ],
    priority: 'high',
    maxAttempts: 3,
  },
  {
    name: 'token_earned',
    description: 'Token earned notification',
    category: 'token',
    type: 'sms',
    title: 'Tokens Earned!',
    message: "You've earned {{amount}} tokens for {{reason}}! Your sustainable actions are making a difference. 🌱",
    variables: [
      {
        name: 'amount',
        type: 'number',
        required: true,
        description: 'Number of tokens earned',
      },
      {
        name: 'reason',
        type: 'string',
        required: true,
        description: 'Reason for earning tokens',
      },
    ],
    priority: 'medium',
    maxAttempts: 2,
  },
  {
    name: 'welcome_message',
    description: 'Welcome message for new users',
    category: 'welcome',
    type: 'sms',
    title: 'Welcome to Pedi!',
    message: 'Hi {{firstName}}! Welcome to Pedi, Kenya\'s sustainable fashion platform. Start by listing your first item or browse available exchanges. Let\'s make fashion sustainable together! 🌱👗',
    variables: [
      {
        name: 'firstName',
        type: 'string',
        required: true,
        description: 'User\'s first name',
      },
    ],
    priority: 'high',
    maxAttempts: 3,
  },
  {
    name: 'exchange_request',
    description: 'New exchange request notification',
    category: 'exchange',
    type: 'sms',
    title: 'New Exchange Request',
    message: '{{requesterName}} wants to exchange "{{itemName}}" with you. Check the app to respond.',
    variables: [
      {
        name: 'requesterName',
        type: 'string',
        required: true,
        description: 'Name of the person requesting exchange',
      },
      {
        name: 'itemName',
        type: 'string',
        required: true,
        description: 'Name of the item to exchange',
      },
    ],
    priority: 'medium',
    maxAttempts: 2,
  },
  {
    name: 'exchange_accepted',
    description: 'Exchange request accepted notification',
    category: 'exchange',
    type: 'sms',
    title: 'Exchange Accepted',
    message: 'Your exchange request for "{{itemName}}" has been accepted by {{partnerName}}. Arrange pickup/delivery in the app.',
    variables: [
      {
        name: 'itemName',
        type: 'string',
        required: true,
        description: 'Name of the item to exchange',
      },
      {
        name: 'partnerName',
        type: 'string',
        required: true,
        description: 'Name of the exchange partner',
      },
    ],
    priority: 'medium',
    maxAttempts: 2,
  },
  {
    name: 'exchange_completed',
    description: 'Exchange completed notification',
    category: 'exchange',
    type: 'sms',
    title: 'Exchange Completed',
    message: 'Your exchange of "{{itemName}}" with {{partnerName}} is complete! You\'ve earned tokens for sustainable fashion. 🌍',
    variables: [
      {
        name: 'itemName',
        type: 'string',
        required: true,
        description: 'Name of the item exchanged',
      },
      {
        name: 'partnerName',
        type: 'string',
        required: true,
        description: 'Name of the exchange partner',
      },
    ],
    priority: 'medium',
    maxAttempts: 2,
  },
  {
    name: 'verification_reminder',
    description: 'Account verification reminder',
    category: 'security',
    type: 'sms',
    title: 'Verification Required',
    message: 'Please verify your {{verificationType}} to secure your account and access all Pedi features. Verification helps protect your account and builds trust in our community. 🔒',
    variables: [
      {
        name: 'verificationType',
        type: 'string',
        required: true,
        description: 'Type of verification required (phone number, email, identity)',
      },
    ],
    priority: 'high',
    maxAttempts: 3,
  },
  {
    name: 'pickup_reminder',
    description: 'Item pickup reminder',
    category: 'reminder',
    type: 'sms',
    title: 'Pickup Reminder',
    message: '{{details}}. Don\'t forget to complete your pickup to keep your sustainable fashion journey going! 📱',
    variables: [
      {
        name: 'details',
        type: 'string',
        required: true,
        description: 'Pickup details and instructions',
      },
    ],
    priority: 'medium',
    maxAttempts: 2,
  },
  {
    name: 'system_maintenance',
    description: 'System maintenance alert',
    category: 'system',
    type: 'sms',
    title: 'System Maintenance',
    message: '🔧 {{message}}',
    variables: [
      {
        name: 'message',
        type: 'string',
        required: true,
        description: 'Maintenance message details',
      },
    ],
    priority: 'high',
    maxAttempts: 3,
  },
];

export async function seedNotificationTemplates(): Promise<void> {
  try {
    logger.info('Starting notification templates seeding...');

    // Create a default admin user ID for templates
    const adminUserId = new mongoose.Types.ObjectId();

    for (const templateData of templates) {
      const existingTemplate = await NotificationTemplate.findOne({ name: templateData.name });
      
      if (existingTemplate) {
        logger.info(`Template ${templateData.name} already exists, skipping...`);
        continue;
      }

      const template = new NotificationTemplate({
        ...templateData,
        createdBy: adminUserId,
        isActive: true,
        usageCount: 0,
      });

      await template.save();
      logger.info(`Created notification template: ${templateData.name}`);
    }

    logger.info('Notification templates seeding completed successfully');

  } catch (error) {
    logger.error('Failed to seed notification templates:', error);
    throw error;
  }
}

// Run seeding if this file is executed directly
if (require.main === module) {
  const mongoUri = process.env.MONGODB_URI || 'mongodb://localhost:27017/pedi';
  
  mongoose.connect(mongoUri)
    .then(async () => {
      logger.info('Connected to MongoDB for seeding');
      await seedNotificationTemplates();
      await mongoose.disconnect();
      logger.info('Seeding completed and disconnected from MongoDB');
      process.exit(0);
    })
    .catch((error) => {
      logger.error('Failed to connect to MongoDB:', error);
      process.exit(1);
    });
}
