import { Request, Response, NextFunction } from 'express';
import { tokenService } from '../services/token';
import User from '../models/User';
import ClothingItem from '../models/ClothingItem';
import logger from '../utils/logger';

interface AuthenticatedRequest extends Request {
  user?: {
    id: string;
    phoneNumber: string;
    role: string;
  };
}

/**
 * Factory function to create middleware for tracking specific user activities
 */
export const trackUserActivity = (activityType: string, customMetadata: any = {}) => {
  return async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    try {
      if (!req.user?.id) {
        return next();
      }

      const userId = req.user.id;
      let metadata: any = { ...customMetadata };

      // Add common metadata based on activity type
      switch (activityType) {
        case 'profile_update':
          metadata = { ...metadata, section: 'profile' };
          break;
        case 'review_given':
          metadata = {
            ...metadata,
            reviewType: req.body?.rating ? 'rating' : 'text',
            rating: req.body?.rating
          };
          break;
        case 'donation_initiated':
        case 'donation_completed':
          metadata = {
            ...metadata,
            itemId: req.body?.itemId || req.body?.items?.[0],
            charityId: req.body?.charityPartnerId
          };
          break;
        case 'swap_completed':
          metadata = {
            ...metadata,
            swapId: req.params?.id,
            partnerId: req.body?.partnerId
          };
          break;
        case 'referral':
          metadata = {
            ...metadata,
            referredPhone: req.body?.phoneNumber,
            referralCode: req.body?.code
          };
          break;
        case 'exchange_offer_created':
        case 'exchange_counter_offer_created':
          metadata = {
            ...metadata,
            exchangeType: req.body?.type,
            targetUserId: req.body?.targetUserId,
            tokenAmount: req.body?.tokenAmount
          };
          break;
        case 'token_purchase_initiated':
        case 'token_purchase_accepted':
        case 'token_purchase_completed':
          metadata = {
            ...metadata,
            itemId: req.body?.itemId,
            offeredAmount: req.body?.offeredAmount,
            deliveryMethod: req.body?.deliveryMethod
          };
          break;
      }

      // Store activity info for post-response processing
      res.locals.tokenActivity = {
        userId,
        activityType,
        metadata
      };

      next();
    } catch (error) {
      logger.error('Error in trackUserActivity middleware:', error);
      next(); // Don't block the request if tracking fails
    }
  };
};

/**
 * Legacy middleware for automatic activity detection (deprecated)
 */
export const trackUserActivityAuto = async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
  try {
    if (!req.user?.id) {
      return next();
    }

    const userId = req.user.id;
    const method = req.method;
    const path = req.path;

    // Track different activities based on the route and method
    let activityType: string | null = null;
    let metadata: any = {};

    // Profile completion tracking
    if (method === 'PUT' && path === '/profile') {
      activityType = 'profile_update';
      metadata = { section: 'profile' };
    }

    // Review submission tracking
    if (method === 'POST' && path.includes('/reviews')) {
      activityType = 'review_given';
      metadata = {
        reviewType: req.body?.rating ? 'rating' : 'text',
        rating: req.body?.rating
      };
    }

    // Store activity info for post-response processing
    if (activityType) {
      res.locals.tokenActivity = {
        userId,
        activityType,
        metadata
      };
    }

    next();
  } catch (error) {
    logger.error('Error in trackUserActivityAuto middleware:', error);
    next(); // Don't block the request if tracking fails
  }
};

/**
 * Middleware to process token rewards after successful response
 */
export const processTokenRewards = async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
  const originalSend = res.send;

  res.send = function(data: any) {
    // Call original send first
    const result = originalSend.call(this, data);

    // Process token rewards asynchronously after response is sent
    setImmediate(async () => {
      try {
        const activity = res.locals.tokenActivity;
        if (!activity) return;

        const { userId, activityType, metadata } = activity;

        // Only award tokens if the response was successful
        const responseData = typeof data === 'string' ? JSON.parse(data) : data;
        if (!responseData?.success) return;

        // Award tokens based on activity type
        await awardActivityTokens(userId, activityType, metadata, responseData);

      } catch (error) {
        logger.error('Error processing token rewards:', error);
      }
    });

    return result;
  };

  next();
};

/**
 * Award tokens for specific activities
 */
async function awardActivityTokens(
  userId: string, 
  activityType: string, 
  metadata: any, 
  responseData: any
) {
  try {
    let description = '';
    let referenceId = '';
    let enhancedMetadata = { ...metadata };

    switch (activityType) {
      case 'profile_update':
        // Check if profile is now complete
        const user = await User.findById(userId);
        if (user && isProfileComplete(user)) {
          description = 'Completed your profile';
          enhancedMetadata.isFirstTime = !user.profileCompleted;
          enhancedMetadata.completionPercentage = 100;

          // Mark profile as completed
          if (!user.profileCompleted) {
            user.profileCompleted = true;
            await user.save();
          }
        } else {
          return; // Don't award tokens for partial updates
        }
        break;

      case 'review_given':
        description = `Wrote a helpful review`;
        referenceId = responseData.data?.id || '';
        enhancedMetadata.isFirstTime = await isFirstTimeAction(userId, 'review');
        break;

      case 'donation_initiated':
        description = `Initiated a donation to charity`;
        referenceId = metadata.itemId || '';
        enhancedMetadata.sustainabilityImpact = 'high';
        break;

      case 'donation_completed':
        description = `Completed a donation to charity`;
        referenceId = metadata.itemId || '';
        enhancedMetadata.isFirstTime = await isFirstTimeAction(userId, 'donation');
        enhancedMetadata.sustainabilityImpact = 'high';
        break;

      case 'recycling_initiated':
        description = `Initiated recycling of clothing item`;
        referenceId = metadata.itemId || '';
        enhancedMetadata.sustainabilityImpact = 'high';
        break;

      case 'recycling_completed':
        description = `Completed recycling of clothing item`;
        referenceId = metadata.itemId || '';
        enhancedMetadata.isFirstTime = await isFirstTimeAction(userId, 'recycling');
        enhancedMetadata.sustainabilityImpact = 'high';
        break;

      case 'swap_completed':
        description = `Completed a clothing swap`;
        referenceId = metadata.swapId || '';
        enhancedMetadata.isFirstTime = await isFirstTimeAction(userId, 'swap');
        enhancedMetadata.sustainabilityImpact = 'medium';
        break;

      case 'exchange_offer_created':
        description = `Created an exchange offer`;
        referenceId = responseData.data?.id || '';
        enhancedMetadata.exchangeType = metadata.exchangeType;
        enhancedMetadata.sustainabilityImpact = 'medium';
        break;

      case 'exchange_counter_offer_created':
        description = `Created a counter offer`;
        referenceId = responseData.data?.id || '';
        enhancedMetadata.exchangeType = metadata.exchangeType;
        break;

      case 'token_purchase_initiated':
        description = `Initiated a token purchase`;
        referenceId = metadata.itemId || '';
        enhancedMetadata.offeredAmount = metadata.offeredAmount;
        break;

      case 'token_purchase_accepted':
        description = `Accepted a token purchase offer`;
        referenceId = responseData.data?.id || '';
        enhancedMetadata.sustainabilityImpact = 'medium';
        break;

      case 'token_purchase_completed':
        description = `Completed a token purchase`;
        referenceId = responseData.data?.id || '';
        enhancedMetadata.isFirstTime = await isFirstTimeAction(userId, 'token_purchase');
        enhancedMetadata.sustainabilityImpact = 'medium';
        break;

      case 'referral':
        description = `Referred a new user to Pedi`;
        enhancedMetadata.isFirstTime = false; // Referrals are always rewarded
        enhancedMetadata.socialImpact = 'high';
        break;

      default:
        return; // Unknown activity type
    }

    // Award the tokens
    const result = await tokenService.earnTokens({
      userId,
      action: activityType,
      referenceId,
      description,
      metadata: enhancedMetadata
    });

    if (result.success) {
      logger.info('Tokens awarded for activity', {
        userId,
        activityType,
        tokensEarned: result.tokensEarned,
        newBalance: result.newBalance
      });
    }

  } catch (error) {
    logger.error('Error awarding activity tokens:', error);
  }
}

/**
 * Check if user profile is complete
 */
function isProfileComplete(user: any): boolean {
  const requiredFields = [
    'firstName',
    'lastName',
    'phoneNumber',
    'location.county',
    'location.town'
  ];

  return requiredFields.every(field => {
    const value = field.split('.').reduce((obj, key) => obj?.[key], user);
    return value && value.toString().trim().length > 0;
  });
}

/**
 * Check if this is the first time user performs this action
 */
async function isFirstTimeAction(userId: string, action: string): Promise<boolean> {
  try {
    const user = await User.findById(userId);
    if (!user) return false;

    switch (action) {
      case 'review':
        return user.totalReviews === 0;
      case 'donation':
        return user.totalDonations === 0;
      case 'swap':
        return user.totalSwaps === 0;
      case 'token_purchase':
        return user.totalTokenPurchases === 0;
      default:
        return false;
    }
  } catch (error) {
    logger.error('Error checking first time action:', error);
    return false;
  }
}

/**
 * Middleware specifically for listing rewards with quality bonuses
 */
export const processListingRewards = async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
  const originalSend = res.send;

  res.send = function(data: any) {
    const result = originalSend.call(this, data);

    setImmediate(async () => {
      try {
        if (!req.user?.id) return;

        const responseData = typeof data === 'string' ? JSON.parse(data) : data;
        if (!responseData?.success || !responseData?.data) return;

        const userId = req.user.id;
        const clothingItem = responseData.data;

        // Only award tokens for donations and recycling, not for swap/token exchange
        if (clothingItem.exchangeType === 'donation') {
          // Check if this is user's first donation
          const user = await User.findById(userId);
          const isFirstDonation = user ? user.totalDonations === 0 : false;

          // Calculate quality bonus for donations
          let qualityBonus = 0;
          if (clothingItem.condition === 'Like New' || clothingItem.condition === 'New') qualityBonus += 10;
          if (clothingItem.sustainabilityInfo?.score > 80) qualityBonus += 5;

          const enhancedMetadata = {
            condition: clothingItem.condition,
            category: clothingItem.category,
            sustainabilityScore: clothingItem.sustainabilityInfo?.score || 0,
            isFirstTime: isFirstDonation,
            qualityBonus,
            brand: clothingItem.brand,
            exchangeType: 'donation'
          };

          // Award tokens for donation listing
          const tokenResult = await tokenService.earnTokens({
            userId,
            action: isFirstDonation ? 'first_donation' : 'donation_initiated',
            referenceId: clothingItem._id || clothingItem.id,
            description: `Listed ${clothingItem.title} for donation`,
            metadata: enhancedMetadata
          });

          logger.info('Donation listing tokens awarded', {
            userId,
            itemId: clothingItem._id || clothingItem.id,
            tokensEarned: tokenResult.tokensEarned,
            qualityBonus,
            isFirstDonation
          });
        } else if (clothingItem.exchangeType === 'recycling') {
          // Check if this is user's first recycling
          const user = await User.findById(userId);
          const isFirstRecycling = user ? user.totalRecycling === 0 : false;

          // Calculate quality bonus for recycling (lower than donations since items may be worn out)
          let qualityBonus = 0;
          if (clothingItem.sustainabilityInfo?.score > 70) qualityBonus += 3;

          const enhancedMetadata = {
            condition: clothingItem.condition,
            category: clothingItem.category,
            sustainabilityScore: clothingItem.sustainabilityInfo?.score || 0,
            isFirstTime: isFirstRecycling,
            qualityBonus,
            brand: clothingItem.brand,
            exchangeType: 'recycling'
          };

          // Award tokens for recycling listing
          const tokenResult = await tokenService.earnTokens({
            userId,
            action: isFirstRecycling ? 'first_recycling' : 'recycling_initiated',
            referenceId: clothingItem._id || clothingItem.id,
            description: `Listed ${clothingItem.title} for recycling`,
            metadata: enhancedMetadata
          });

          logger.info('Recycling listing tokens awarded', {
            userId,
            itemId: clothingItem._id || clothingItem.id,
            tokensEarned: tokenResult.tokensEarned,
            qualityBonus,
            isFirstRecycling
          });
        }
        // Note: No tokens awarded for swap/token exchange listings



      } catch (error) {
        logger.error('Error processing listing rewards:', error);
      }
    });

    return result;
  };

  next();
};
