import express from 'express';
import { body, param, query, validationResult } from 'express-validator';
import { authenticate } from '../middleware/auth';
import { swapService } from '../services/swap';
import { trackUserActivity } from '../middleware/tokenRewards';

const router = express.Router();

// Validation middleware
const handleValidationErrors = (req: express.Request, res: express.Response, next: express.NextFunction) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      message: 'Validation failed',
      errors: errors.array()
    });
  }
  next();
};

// Create a direct swap transaction
router.post('/',
  authenticate,
  trackUserActivity('swap_initiated'),
  [
    body('recipientId').isMongoId().withMessage('Invalid recipient ID'),
    body('initiatorItems').isArray({ min: 1 }).withMessage('At least one initiator item is required'),
    body('initiatorItems.*').isMongoId().withMessage('Invalid initiator item ID'),
    body('recipientItems').isArray({ min: 1 }).withMessage('At least one recipient item is required'),
    body('recipientItems.*').isMongoId().withMessage('Invalid recipient item ID'),
    body('deliveryMethod').isIn(['pickup', 'delivery', 'meetup']).withMessage('Invalid delivery method'),
    body('deliveryAddress').optional().isObject(),
    body('deliveryAddress.county').optional().isString().withMessage('County is required for delivery'),
    body('deliveryAddress.town').optional().isString().withMessage('Town is required for delivery'),
    body('deliveryAddress.specificLocation').optional().isString().withMessage('Specific location is required for delivery'),
    body('deliveryAddress.contactPhone').optional().matches(/^(\+254|0)[17]\d{8}$/).withMessage('Invalid phone number'),
    body('meetupLocation').optional().isObject(),
    body('meetupLocation.name').optional().isString().withMessage('Meetup location name is required'),
    body('meetupLocation.address').optional().isString().withMessage('Meetup address is required'),
    body('meetupLocation.scheduledTime').optional().isISO8601().withMessage('Invalid scheduled time'),
    body('notes').optional().isLength({ max: 500 }).withMessage('Notes too long'),
  ],
  handleValidationErrors,
  async (req: express.Request, res: express.Response) => {
    try {
      const userId = req.user!.id;
      const { recipientId, ...swapData } = req.body;

      if (userId === recipientId) {
        return res.status(400).json({
          success: false,
          message: 'You cannot create a swap with yourself'
        });
      }

      const transaction = await swapService.createSwapTransaction(userId, recipientId, swapData);

      res.status(201).json({
        success: true,
        message: 'Swap transaction created successfully',
        data: transaction
      });
    } catch (error: any) {
      res.status(400).json({
        success: false,
        message: error.message || 'Failed to create swap transaction'
      });
    }
  }
);

// Complete a swap transaction
router.post('/:transactionId/complete',
  authenticate,
  trackUserActivity('swap_completed'),
  [
    param('transactionId').isMongoId().withMessage('Invalid transaction ID'),
    body('rating').optional().isFloat({ min: 1, max: 5 }).withMessage('Rating must be between 1 and 5'),
    body('comment').optional().isLength({ max: 300 }).withMessage('Comment too long'),
    body('qualityConfirmed').isBoolean().withMessage('Quality confirmation is required'),
  ],
  handleValidationErrors,
  async (req: express.Request, res: express.Response) => {
    try {
      const userId = req.user!.id;
      const { transactionId } = req.params;
      const completionData = req.body;

      const transaction = await swapService.completeSwap(transactionId, userId, completionData);

      res.json({
        success: true,
        message: 'Swap completed successfully',
        data: transaction
      });
    } catch (error: any) {
      res.status(400).json({
        success: false,
        message: error.message || 'Failed to complete swap'
      });
    }
  }
);

// Cancel a swap transaction
router.post('/:transactionId/cancel',
  authenticate,
  trackUserActivity('swap_cancelled'),
  [
    param('transactionId').isMongoId().withMessage('Invalid transaction ID'),
    body('reason').isLength({ min: 10, max: 300 }).withMessage('Cancellation reason must be between 10 and 300 characters'),
  ],
  handleValidationErrors,
  async (req: express.Request, res: express.Response) => {
    try {
      const userId = req.user!.id;
      const { transactionId } = req.params;
      const { reason } = req.body;

      const transaction = await swapService.cancelSwap(transactionId, userId, reason);

      res.json({
        success: true,
        message: 'Swap cancelled successfully',
        data: transaction
      });
    } catch (error: any) {
      res.status(400).json({
        success: false,
        message: error.message || 'Failed to cancel swap'
      });
    }
  }
);

// Get swap recommendations
router.get('/recommendations',
  authenticate,
  [
    query('itemId').optional().isMongoId().withMessage('Invalid item ID'),
  ],
  handleValidationErrors,
  async (req: express.Request, res: express.Response) => {
    try {
      const userId = req.user!.id;
      const { itemId } = req.query;

      const recommendations = await swapService.getSwapRecommendations(userId, itemId as string);

      res.json({
        success: true,
        data: recommendations
      });
    } catch (error: any) {
      res.status(500).json({
        success: false,
        message: error.message || 'Failed to fetch swap recommendations'
      });
    }
  }
);

// Get swap history for user
router.get('/history',
  authenticate,
  [
    query('status').optional().isIn(['pending', 'accepted', 'completed', 'cancelled']).withMessage('Invalid status filter'),
    query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
    query('limit').optional().isInt({ min: 1, max: 50 }).withMessage('Limit must be between 1 and 50'),
  ],
  handleValidationErrors,
  async (req: express.Request, res: express.Response) => {
    try {
      const userId = req.user!.id;
      const { status, page = 1, limit = 10 } = req.query;

      // This would be implemented with proper pagination
      // For now, returning placeholder response
      const swaps = {
        transactions: [],
        pagination: {
          currentPage: parseInt(page as string),
          totalPages: 0,
          totalItems: 0,
          itemsPerPage: parseInt(limit as string)
        }
      };

      res.json({
        success: true,
        data: swaps
      });
    } catch (error: any) {
      res.status(500).json({
        success: false,
        message: error.message || 'Failed to fetch swap history'
      });
    }
  }
);

// Get swap statistics for user
router.get('/stats',
  authenticate,
  async (req: express.Request, res: express.Response) => {
    try {
      const userId = req.user!.id;

      // This would be implemented with aggregation pipelines
      // For now, returning placeholder data
      const stats = {
        totalSwaps: 0,
        completedSwaps: 0,
        cancelledSwaps: 0,
        averageRating: 0,
        totalItemsSwapped: 0,
        sustainabilityImpact: {
          co2Saved: 0, // kg
          waterSaved: 0, // liters
          wasteReduced: 0 // kg
        },
        monthlySwaps: [],
        topCategories: []
      };

      res.json({
        success: true,
        data: stats
      });
    } catch (error: any) {
      res.status(500).json({
        success: false,
        message: error.message || 'Failed to fetch swap statistics'
      });
    }
  }
);

// Validate swap compatibility
router.post('/validate',
  authenticate,
  [
    body('initiatorItems').isArray({ min: 1 }).withMessage('At least one initiator item is required'),
    body('initiatorItems.*').isMongoId().withMessage('Invalid initiator item ID'),
    body('recipientItems').isArray({ min: 1 }).withMessage('At least one recipient item is required'),
    body('recipientItems.*').isMongoId().withMessage('Invalid recipient item ID'),
    body('recipientId').isMongoId().withMessage('Invalid recipient ID'),
  ],
  handleValidationErrors,
  async (req: express.Request, res: express.Response) => {
    try {
      const userId = req.user!.id;
      const { initiatorItems, recipientItems, recipientId } = req.body;

      // This would implement validation logic
      // For now, returning placeholder response
      const validation = {
        isValid: true,
        warnings: [],
        suggestions: [],
        valueComparison: {
          initiatorValue: 0,
          recipientValue: 0,
          fairnessScore: 0
        }
      };

      res.json({
        success: true,
        data: validation
      });
    } catch (error: any) {
      res.status(500).json({
        success: false,
        message: error.message || 'Failed to validate swap'
      });
    }
  }
);

export default router;
