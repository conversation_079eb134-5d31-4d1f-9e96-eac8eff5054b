import { Router } from 'express';
import { validationResult } from 'express-validator';
import { body, param, query } from 'express-validator';
import mongoose from 'mongoose';
import Chat from '@/models/Chat';
import User from '@/models/User';
import ClothingItem from '@/models/ClothingItem';
import { authenticate } from '@/middleware/auth';
import { createError } from '@/middleware/errorHandler';
import { logger } from '@/utils/logger';
import { emitToUser, emitToChat, getSocketInstance } from '@/services/socket';

const router = Router();

// Validation middleware
const handleValidationErrors = (req: any, res: any, next: any) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return next(createError('Validation failed', 400, errors.array()));
  }
  next();
};

// Get user conversations
router.get('/conversations',
  authenticate,
  [
    query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
    query('limit').optional().isInt({ min: 1, max: 50 }).withMessage('Limit must be between 1 and 50'),
    query('search').optional().isString().withMessage('Search must be a string'),
  ],
  handleValidationErrors,
  async (req: any, res: any, next: any) => {
    try {
      const userId = req.user.id;
      const page = parseInt(req.query.page) || 1;
      const limit = parseInt(req.query.limit) || 20;
      const search = req.query.search;
      const skip = (page - 1) * limit;

      // Build query
      const query: any = {
        participants: userId,
        isActive: true,
      };

      // Add search functionality if provided
      if (search) {
        query.$or = [
          { 'lastMessage.content': { $regex: search, $options: 'i' } },
        ];
      }

      // Get conversations with populated participants
      const conversations = await Chat.find(query)
        .populate('participants', 'firstName lastName profilePicture phoneNumber')
        .populate('relatedClothingItem', 'title images category')
        .sort({ 'lastMessage.timestamp': -1, updatedAt: -1 })
        .skip(skip)
        .limit(limit)
        .lean();

      // Calculate unread count for each conversation
      const conversationsWithUnread = conversations.map(chat => {
        const unreadCount = chat.messages.filter((msg: any) =>
          !msg.isRead && msg.sender.toString() !== userId
        ).length;

        return {
          ...chat,
          unreadCount,
          // Remove sensitive data and format response
          participants: chat.participants.map((p: any) => ({
            id: p._id,
            firstName: p.firstName,
            lastName: p.lastName,
            profilePicture: p.profilePicture,
          })),
        };
      });

      // Get total count for pagination
      const totalCount = await Chat.countDocuments(query);

      res.json({
        success: true,
        data: {
          conversations: conversationsWithUnread,
          pagination: {
            page,
            limit,
            totalPages: Math.ceil(totalCount / limit),
            totalCount,
            hasNext: page * limit < totalCount,
            hasPrev: page > 1,
          },
        },
      });
    } catch (error: any) {
      logger.error('Error fetching conversations:', error);
      next(createError('Failed to fetch conversations', 500));
    }
  }
);

// Create new conversation
router.post('/conversations',
  authenticate,
  [
    body('participantId').isMongoId().withMessage('Valid participant ID is required'),
    body('relatedClothingItem').optional().isMongoId().withMessage('Related clothing item must be a valid ID'),
    body('initialMessage').optional().isString().trim().isLength({ min: 1, max: 1000 }).withMessage('Initial message must be 1-1000 characters'),
  ],
  handleValidationErrors,
  async (req: any, res: any, next: any) => {
    try {
      const userId = req.user.id;
      const { participantId, relatedClothingItem, initialMessage } = req.body;

      // Validate participant exists and is not the same as current user
      if (participantId === userId) {
        return next(createError('Cannot create conversation with yourself', 400));
      }

      const participant = await User.findById(participantId);
      if (!participant) {
        return next(createError('Participant not found', 404));
      }

      // Check if conversation already exists between these users
      const existingChat = await Chat.findOne({
        participants: { $all: [userId, participantId] },
        isActive: true,
        ...(relatedClothingItem && { relatedClothingItem }),
      });

      if (existingChat) {
        return res.json({
          success: true,
          data: existingChat,
          message: 'Conversation already exists',
        });
      }

      // Validate related clothing item if provided
      if (relatedClothingItem) {
        const clothingItem = await ClothingItem.findById(relatedClothingItem);
        if (!clothingItem) {
          return next(createError('Related clothing item not found', 404));
        }
      }

      // Create new conversation
      const newChat = new Chat({
        participants: [userId, participantId],
        relatedClothingItem,
        messages: [],
        isActive: true,
      });

      // Add initial message if provided
      if (initialMessage) {
        newChat.messages.push({
          sender: new mongoose.Types.ObjectId(userId),
          content: initialMessage,
          type: 'text',
          timestamp: new Date(),
          isRead: false,
        });

        newChat.lastMessage = {
          content: initialMessage,
          sender: new mongoose.Types.ObjectId(userId),
          timestamp: new Date(),
        };
      }

      await newChat.save();

      // Populate the response
      const populatedChat = await Chat.findById(newChat._id)
        .populate('participants', 'firstName lastName profilePicture phoneNumber')
        .populate('relatedClothingItem', 'title images category');

      // Emit to both participants via Socket.io
      const participantIds = [userId, participantId];
      participantIds.forEach(id => {
        emitToUser(id, 'new-conversation', populatedChat);
      });

      res.status(201).json({
        success: true,
        data: populatedChat,
        message: 'Conversation created successfully',
      });
    } catch (error: any) {
      logger.error('Error creating conversation:', error);
      next(createError('Failed to create conversation', 500));
    }
  }
);

// Get conversation messages
router.get('/conversations/:id/messages',
  authenticate,
  [
    param('id').isMongoId().withMessage('Valid conversation ID is required'),
    query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
    query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100'),
    query('before').optional().isISO8601().withMessage('Before must be a valid date'),
  ],
  handleValidationErrors,
  async (req: any, res: any, next: any) => {
    try {
      const userId = req.user.id;
      const chatId = req.params.id;
      const page = parseInt(req.query.page) || 1;
      const limit = parseInt(req.query.limit) || 50;
      const before = req.query.before;

      // Find the conversation and verify user is a participant
      const chat = await Chat.findOne({
        _id: chatId,
        participants: userId,
        isActive: true,
      });

      if (!chat) {
        return next(createError('Conversation not found or access denied', 404));
      }

      // Build message query
      let messageQuery: any = {};
      if (before) {
        messageQuery.timestamp = { $lt: new Date(before) };
      }

      // Get messages with pagination (newest first, then reverse for display)
      const skip = (page - 1) * limit;

      // Filter messages based on query and paginate
      let filteredMessages = chat.messages;
      if (before) {
        filteredMessages = chat.messages.filter(msg =>
          msg.timestamp < new Date(before)
        );
      }

      // Sort by timestamp descending, then paginate
      const sortedMessages = filteredMessages
        .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
        .slice(skip, skip + limit);

      // Populate sender information
      const populatedMessages = await Chat.populate(sortedMessages, {
        path: 'sender',
        select: 'firstName lastName profilePicture',
      });

      // Mark messages as read for the current user
      const unreadMessageIds = chat.messages
        .filter(msg => !msg.isRead && msg.sender.toString() !== userId)
        .map(msg => msg._id);

      if (unreadMessageIds.length > 0) {
        await Chat.updateOne(
          { _id: chatId },
          { $set: { 'messages.$[elem].isRead': true } },
          { arrayFilters: [{ 'elem._id': { $in: unreadMessageIds } }] }
        );

        // Emit read status update
        emitToChat(chatId, 'messages-read', {
          messageIds: unreadMessageIds,
          readBy: userId,
        });
      }

      const totalMessages = filteredMessages.length;

      res.json({
        success: true,
        data: {
          messages: populatedMessages.reverse(), // Reverse to show oldest first
          pagination: {
            page,
            limit,
            totalPages: Math.ceil(totalMessages / limit),
            totalCount: totalMessages,
            hasNext: page * limit < totalMessages,
            hasPrev: page > 1,
          },
        },
      });
    } catch (error: any) {
      logger.error('Error fetching messages:', error);
      next(createError('Failed to fetch messages', 500));
    }
  }
);

// Send message
router.post('/conversations/:id/messages',
  authenticate,
  [
    param('id').isMongoId().withMessage('Valid conversation ID is required'),
    body('content').isString().trim().isLength({ min: 1, max: 1000 }).withMessage('Message content must be 1-1000 characters'),
    body('type').optional().isIn(['text', 'image']).withMessage('Message type must be text or image'),
  ],
  handleValidationErrors,
  async (req: any, res: any, next: any) => {
    try {
      const userId = req.user.id;
      const chatId = req.params.id;
      const { content, type = 'text' } = req.body;

      // Find the conversation and verify user is a participant
      const chat = await Chat.findOne({
        _id: chatId,
        participants: userId,
        isActive: true,
      });

      if (!chat) {
        return next(createError('Conversation not found or access denied', 404));
      }

      // Create new message
      const newMessage = {
        sender: new mongoose.Types.ObjectId(userId),
        content: content.trim(),
        type,
        timestamp: new Date(),
        isRead: false,
      };

      // Add message to chat
      chat.messages.push(newMessage);
      chat.lastMessage = {
        content: content.trim(),
        sender: new mongoose.Types.ObjectId(userId),
        timestamp: new Date(),
      };

      await chat.save();

      // Get the saved message with populated sender
      const savedMessage = chat.messages[chat.messages.length - 1];
      const populatedMessage = await Chat.populate(savedMessage, {
        path: 'sender',
        select: 'firstName lastName profilePicture',
      });

      // Emit to all participants via Socket.io
      emitToChat(chatId, 'new-message', {
        chatId,
        message: populatedMessage,
      });

      // Also emit to user-specific channels for notifications
      chat.participants.forEach((participantId: any) => {
        if (participantId.toString() !== userId) {
          emitToUser(participantId.toString(), 'new-message', {
            chatId,
            message: populatedMessage,
            from: req.user.firstName + ' ' + req.user.lastName,
          });
        }
      });

      res.status(201).json({
        success: true,
        data: populatedMessage,
        message: 'Message sent successfully',
      });
    } catch (error: any) {
      logger.error('Error sending message:', error);
      next(createError('Failed to send message', 500));
    }
  }
);

// Mark messages as read
router.patch('/conversations/:id/messages/read',
  authenticate,
  [
    param('id').isMongoId().withMessage('Valid conversation ID is required'),
    body('messageIds').optional().isArray().withMessage('Message IDs must be an array'),
  ],
  handleValidationErrors,
  async (req: any, res: any, next: any) => {
    try {
      const userId = req.user.id;
      const chatId = req.params.id;
      const { messageIds } = req.body;

      // Find the conversation and verify user is a participant
      const chat = await Chat.findOne({
        _id: chatId,
        participants: userId,
        isActive: true,
      });

      if (!chat) {
        return next(createError('Conversation not found or access denied', 404));
      }

      // Mark messages as read
      let updateQuery: any = { $set: { 'messages.$[elem].isRead': true } };
      let arrayFilters: any = [{ 'elem.sender': { $ne: userId } }];

      if (messageIds && messageIds.length > 0) {
        arrayFilters = [{
          'elem._id': { $in: messageIds.map((id: string) => new mongoose.Types.ObjectId(id)) },
          'elem.sender': { $ne: userId }
        }];
      }

      await Chat.updateOne(
        { _id: chatId },
        updateQuery,
        { arrayFilters }
      );

      // Emit read status update
      emitToChat(chatId, 'messages-read', {
        readBy: userId,
        messageIds: messageIds || 'all',
      });

      res.json({
        success: true,
        message: 'Messages marked as read',
      });
    } catch (error: any) {
      logger.error('Error marking messages as read:', error);
      next(createError('Failed to mark messages as read', 500));
    }
  }
);

// Delete conversation (soft delete)
router.delete('/conversations/:id',
  authenticate,
  [
    param('id').isMongoId().withMessage('Valid conversation ID is required'),
  ],
  handleValidationErrors,
  async (req: any, res: any, next: any) => {
    try {
      const userId = req.user.id;
      const chatId = req.params.id;

      // Find the conversation and verify user is a participant
      const chat = await Chat.findOne({
        _id: chatId,
        participants: userId,
        isActive: true,
      });

      if (!chat) {
        return next(createError('Conversation not found or access denied', 404));
      }

      // Soft delete the conversation
      chat.isActive = false;
      await chat.save();

      // Emit deletion event
      chat.participants.forEach((participantId: any) => {
        emitToUser(participantId.toString(), 'conversation-deleted', {
          chatId,
          deletedBy: userId,
        });
      });

      res.json({
        success: true,
        message: 'Conversation deleted successfully',
      });
    } catch (error: any) {
      logger.error('Error deleting conversation:', error);
      next(createError('Failed to delete conversation', 500));
    }
  }
);

export default router;
