import express from 'express';
import { body, param, query, validationResult } from 'express-validator';
import { authenticate, AuthenticatedRequest } from '../middleware/auth';
import { donationService } from '../services/donation';
import { trackUserActivity } from '../middleware/tokenRewards';

const router = express.Router();

// Validation middleware
const handleValidationErrors = (req: express.Request, res: express.Response, next: express.NextFunction) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      message: 'Validation failed',
      errors: errors.array()
    });
  }
  return next();
};

// Create a donation
router.post('/',
  authenticate,
  trackUserActivity('donation_initiated'),
  [
    body('items').isArray({ min: 1 }).withMessage('At least one item is required'),
    body('items.*').isMongoId().withMessage('Invalid item ID'),
    body('charityPartnerId').isMongoId().withMessage('Invalid charity partner ID'),
    body('deliveryMethod').isIn(['pickup', 'delivery', 'drop_off']).withMessage('Invalid delivery method'),
    body('deliveryAddress').optional().isObject(),
    body('deliveryAddress.county').optional().isString().withMessage('County is required for delivery'),
    body('deliveryAddress.town').optional().isString().withMessage('Town is required for delivery'),
    body('deliveryAddress.specificLocation').optional().isString().withMessage('Specific location is required for delivery'),
    body('deliveryAddress.contactPhone').optional().matches(/^(\+254|0)[17]\d{8}$/).withMessage('Invalid phone number'),
    body('dropOffLocation').optional().isObject(),
    body('dropOffLocation.name').optional().isString().withMessage('Drop-off location name is required'),
    body('dropOffLocation.address').optional().isString().withMessage('Drop-off address is required'),
    body('dropOffLocation.operatingHours').optional().isString().withMessage('Operating hours are required'),
    body('message').optional().isLength({ max: 500 }).withMessage('Message too long'),
    body('isAnonymous').optional().isBoolean().withMessage('Anonymous flag must be boolean'),
  ],
  handleValidationErrors,
  async (req: AuthenticatedRequest, res: express.Response) => {
    try {
      const userId = req.user!.id;
      const donationData = req.body;

      const transaction = await donationService.createDonation(userId, donationData);

      res.status(201).json({
        success: true,
        message: 'Donation created successfully',
        data: transaction
      });
    } catch (error: any) {
      res.status(400).json({
        success: false,
        message: error.message || 'Failed to create donation'
      });
    }
  }
);

// Accept a donation (charity partner endpoint)
router.post('/:transactionId/accept',
  authenticate,
  trackUserActivity('donation_accepted'),
  [
    param('transactionId').isMongoId().withMessage('Invalid transaction ID'),
  ],
  handleValidationErrors,
  async (req: AuthenticatedRequest, res: express.Response) => {
    try {
      const charityPartnerId = req.user!.id; // Assuming charity partners have user accounts
      const { transactionId } = req.params;

      const transaction = await donationService.acceptDonation(transactionId, charityPartnerId);

      res.json({
        success: true,
        message: 'Donation accepted successfully',
        data: transaction
      });
    } catch (error: any) {
      res.status(400).json({
        success: false,
        message: error.message || 'Failed to accept donation'
      });
    }
  }
);

// Complete a donation (charity partner endpoint)
router.post('/:transactionId/complete',
  authenticate,
  trackUserActivity('donation_completed'),
  [
    param('transactionId').isMongoId().withMessage('Invalid transaction ID'),
    body('receiptNumber').optional().isString().withMessage('Receipt number must be a string'),
    body('itemsReceived').isArray({ min: 1 }).withMessage('Items received information is required'),
    body('itemsReceived.*.itemId').isMongoId().withMessage('Invalid item ID'),
    body('itemsReceived.*.condition').isIn(['poor', 'fair', 'good', 'very_good', 'excellent']).withMessage('Invalid condition'),
    body('itemsReceived.*.estimatedValue').isNumeric().isFloat({ min: 0 }).withMessage('Estimated value must be non-negative'),
    body('impactStatement').optional().isLength({ max: 1000 }).withMessage('Impact statement too long'),
    body('beneficiaryCount').optional().isInt({ min: 0 }).withMessage('Beneficiary count must be non-negative'),
  ],
  handleValidationErrors,
  async (req: AuthenticatedRequest, res: express.Response) => {
    try {
      const charityPartnerId = req.user!.id;
      const { transactionId } = req.params;
      const completionData = req.body;

      const transaction = await donationService.completeDonation(transactionId, charityPartnerId, completionData);

      res.json({
        success: true,
        message: 'Donation completed successfully',
        data: transaction
      });
    } catch (error: any) {
      res.status(400).json({
        success: false,
        message: error.message || 'Failed to complete donation'
      });
    }
  }
);

// Cancel a donation
router.post('/:transactionId/cancel',
  authenticate,
  trackUserActivity('donation_cancelled'),
  [
    param('transactionId').isMongoId().withMessage('Invalid transaction ID'),
    body('reason').isLength({ min: 10, max: 300 }).withMessage('Cancellation reason must be between 10 and 300 characters'),
  ],
  handleValidationErrors,
  async (req: AuthenticatedRequest, res: express.Response) => {
    try {
      const userId = req.user!.id;
      const { transactionId } = req.params;
      const { reason } = req.body;

      const transaction = await donationService.cancelDonation(transactionId, userId, reason);

      res.json({
        success: true,
        message: 'Donation cancelled successfully',
        data: transaction
      });
    } catch (error: any) {
      res.status(400).json({
        success: false,
        message: error.message || 'Failed to cancel donation'
      });
    }
  }
);

// Get donation recommendations
router.get('/recommendations',
  authenticate,
  [
    query('category').optional().isString().withMessage('Invalid category'),
  ],
  handleValidationErrors,
  async (req: AuthenticatedRequest, res: express.Response) => {
    try {
      const userId = req.user!.id;
      const { category } = req.query;

      const recommendations = await donationService.getDonationRecommendations(userId, category as string);

      res.json({
        success: true,
        data: recommendations
      });
    } catch (error: any) {
      res.status(500).json({
        success: false,
        message: error.message || 'Failed to fetch donation recommendations'
      });
    }
  }
);

// Get donation impact report
router.get('/impact',
  authenticate,
  async (req: AuthenticatedRequest, res: express.Response) => {
    try {
      const userId = req.user!.id;

      const impact = await donationService.getDonationImpact(userId);

      res.json({
        success: true,
        data: impact
      });
    } catch (error: any) {
      res.status(500).json({
        success: false,
        message: error.message || 'Failed to fetch donation impact'
      });
    }
  }
);

// Get donation history for user
router.get('/history',
  authenticate,
  [
    query('status').optional().isIn(['pending', 'accepted', 'completed', 'cancelled']).withMessage('Invalid status filter'),
    query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
    query('limit').optional().isInt({ min: 1, max: 50 }).withMessage('Limit must be between 1 and 50'),
  ],
  handleValidationErrors,
  async (req: AuthenticatedRequest, res: express.Response) => {
    try {
      const userId = req.user!.id;
      const { status, page = 1, limit = 10 } = req.query;

      // This would be implemented with proper pagination and filtering
      // For now, returning placeholder response
      const donations = {
        transactions: [],
        pagination: {
          currentPage: parseInt(page as string),
          totalPages: 0,
          totalItems: 0,
          itemsPerPage: parseInt(limit as string)
        }
      };

      // TODO: Implement actual donation history retrieval using userId and status filter
      console.log(`Fetching donation history for user ${userId} with status filter: ${status}`);

      res.json({
        success: true,
        data: donations
      });
    } catch (error: any) {
      res.status(500).json({
        success: false,
        message: error.message || 'Failed to fetch donation history'
      });
    }
  }
);

// Get donation statistics for user
router.get('/stats',
  authenticate,
  async (req: AuthenticatedRequest, res: express.Response) => {
    try {
      const userId = req.user!.id;

      // This would be implemented with aggregation pipelines
      // For now, returning placeholder data
      const stats = {
        totalDonations: 0,
        totalItemsDonated: 0,
        totalDonationValue: 0,
        totalTokensEarned: 0,
        totalBeneficiariesHelped: 0,
        charityPartnersSupported: 0,
        environmentalImpact: {
          co2Saved: 0,
          waterSaved: 0,
          wasteReduced: 0
        },
        monthlyDonations: [],
        topCharityCategories: []
      };

      // TODO: Implement actual statistics aggregation using userId
      console.log(`Fetching donation statistics for user ${userId}`);

      res.json({
        success: true,
        data: stats
      });
    } catch (error: any) {
      res.status(500).json({
        success: false,
        message: error.message || 'Failed to fetch donation statistics'
      });
    }
  }
);

// Get charity partners list
router.get('/charity-partners',
  authenticate,
  [
    query('category').optional().isString().withMessage('Invalid category'),
    query('county').optional().isString().withMessage('Invalid county'),
    query('verified').optional().isBoolean().withMessage('Verified filter must be boolean'),
  ],
  handleValidationErrors,
  async (req: express.Request, res: express.Response) => {
    try {
      const { category, county, verified } = req.query;

      const matchQuery: any = {
        isActive: true
      };

      if (category) {
        matchQuery.category = category;
      }

      if (county) {
        matchQuery['contactInfo.address.county'] = county;
      }

      if (verified !== undefined) {
        matchQuery.verificationStatus = verified === 'true' ? 'verified' : { $ne: 'verified' };
      }

      // This would use the CharityPartner model
      // For now, returning placeholder response
      const charityPartners = [];

      res.json({
        success: true,
        data: charityPartners
      });
    } catch (error: any) {
      res.status(500).json({
        success: false,
        message: error.message || 'Failed to fetch charity partners'
      });
    }
  }
);

export default router;
