import express from 'express';
import { body, validationResult } from 'express-validator';
import { authenticate } from '../middleware/auth';
import ExchangePreferences from '../models/ExchangePreferences';

const router = express.Router();

// Validation middleware
const handleValidationErrors = (req: express.Request, res: express.Response, next: express.NextFunction) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      message: 'Validation failed',
      errors: errors.array()
    });
  }
  next();
};

// Get user's exchange preferences
router.get('/',
  authenticate,
  async (req: express.Request, res: express.Response) => {
    try {
      const userId = req.user!.id;

      let preferences = await ExchangePreferences.findOne({ user: userId });

      // Create default preferences if none exist
      if (!preferences) {
        const defaultPrefs = ExchangePreferences.getDefaultPreferences(userId);
        preferences = new ExchangePreferences(defaultPrefs);
        await preferences.save();
      }

      res.json({
        success: true,
        data: preferences
      });
    } catch (error: any) {
      res.status(500).json({
        success: false,
        message: error.message || 'Failed to fetch exchange preferences'
      });
    }
  }
);

// Update user's exchange preferences
router.put('/',
  authenticate,
  [
    body('autoAcceptSwaps').optional().isBoolean().withMessage('Auto accept swaps must be boolean'),
    body('autoAcceptTokenPurchases').optional().isBoolean().withMessage('Auto accept token purchases must be boolean'),
    body('autoAcceptDonations').optional().isBoolean().withMessage('Auto accept donations must be boolean'),
    body('minimumTokenOffer').optional().isNumeric({ min: 0 }).withMessage('Minimum token offer must be non-negative'),
    body('minimumRatingRequired').optional().isFloat({ min: 1.0, max: 5.0 }).withMessage('Rating must be between 1.0 and 5.0'),
    body('preferredExchangeTypes').optional().isArray(),
    body('preferredExchangeTypes.*').optional().isIn(['swap', 'token_purchase', 'donation']).withMessage('Invalid exchange type'),
    body('preferredDeliveryMethods').optional().isArray(),
    body('preferredDeliveryMethods.*').optional().isIn(['pickup', 'delivery', 'meetup']).withMessage('Invalid delivery method'),
    body('maxDeliveryDistance').optional().isNumeric({ min: 1, max: 500 }).withMessage('Delivery distance must be between 1 and 500 km'),
    body('deliveryFeeWillingness').optional().isNumeric({ min: 0 }).withMessage('Delivery fee willingness must be non-negative'),
    body('availableDays').optional().isArray(),
    body('availableDays.*').optional().isIn(['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday']).withMessage('Invalid day'),
    body('availableTimeSlots').optional().isArray(),
    body('availableTimeSlots.*.start').optional().matches(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/).withMessage('Invalid start time format (HH:MM)'),
    body('availableTimeSlots.*.end').optional().matches(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/).withMessage('Invalid end time format (HH:MM)'),
    body('minimumConditionAccepted').optional().isIn(['poor', 'fair', 'good', 'very_good', 'excellent']).withMessage('Invalid condition'),
    body('requirePhotos').optional().isBoolean().withMessage('Require photos must be boolean'),
    body('requireDetailedDescription').optional().isBoolean().withMessage('Require detailed description must be boolean'),
    body('allowDirectMessages').optional().isBoolean().withMessage('Allow direct messages must be boolean'),
    body('notifyOnNewOffers').optional().isBoolean().withMessage('Notify on new offers must be boolean'),
    body('notifyOnCounterOffers').optional().isBoolean().withMessage('Notify on counter offers must be boolean'),
    body('notifyOnOfferExpiry').optional().isBoolean().withMessage('Notify on offer expiry must be boolean'),
    body('preferredCharityCategories').optional().isArray(),
    body('requireCharityReceipt').optional().isBoolean().withMessage('Require charity receipt must be boolean'),
    body('maxActiveOffers').optional().isInt({ min: 1, max: 50 }).withMessage('Max active offers must be between 1 and 50'),
    body('maxActiveTransactions').optional().isInt({ min: 1, max: 20 }).withMessage('Max active transactions must be between 1 and 20'),
    body('requireMeetupInPublicPlace').optional().isBoolean().withMessage('Require meetup in public place must be boolean'),
    body('requireIdentityVerification').optional().isBoolean().withMessage('Require identity verification must be boolean'),
  ],
  handleValidationErrors,
  async (req: express.Request, res: express.Response) => {
    try {
      const userId = req.user!.id;
      const updateData = req.body;

      // Remove any fields that shouldn't be updated directly
      delete updateData.user;
      delete updateData.blockedUsers;
      delete updateData._id;
      delete updateData.createdAt;
      delete updateData.updatedAt;

      let preferences = await ExchangePreferences.findOne({ user: userId });

      if (!preferences) {
        // Create new preferences with default values and user updates
        const defaultPrefs = ExchangePreferences.getDefaultPreferences(userId);
        preferences = new ExchangePreferences({ ...defaultPrefs, ...updateData });
      } else {
        // Update existing preferences
        Object.assign(preferences, updateData);
      }

      await preferences.save();

      res.json({
        success: true,
        message: 'Exchange preferences updated successfully',
        data: preferences
      });
    } catch (error: any) {
      res.status(500).json({
        success: false,
        message: error.message || 'Failed to update exchange preferences'
      });
    }
  }
);

// Block a user
router.post('/block/:userId',
  authenticate,
  async (req: express.Request, res: express.Response) => {
    try {
      const currentUserId = req.user!.id;
      const { userId } = req.params;

      if (currentUserId === userId) {
        return res.status(400).json({
          success: false,
          message: 'You cannot block yourself'
        });
      }

      let preferences = await ExchangePreferences.findOne({ user: currentUserId });

      if (!preferences) {
        const defaultPrefs = ExchangePreferences.getDefaultPreferences(currentUserId);
        preferences = new ExchangePreferences(defaultPrefs);
      }

      // Add user to blocked list if not already blocked
      if (!preferences.blockedUsers.includes(userId as any)) {
        preferences.blockedUsers.push(userId as any);
        await preferences.save();
      }

      res.json({
        success: true,
        message: 'User blocked successfully'
      });
    } catch (error: any) {
      res.status(500).json({
        success: false,
        message: error.message || 'Failed to block user'
      });
    }
  }
);

// Unblock a user
router.delete('/block/:userId',
  authenticate,
  async (req: express.Request, res: express.Response) => {
    try {
      const currentUserId = req.user!.id;
      const { userId } = req.params;

      const preferences = await ExchangePreferences.findOne({ user: currentUserId });

      if (!preferences) {
        return res.status(404).json({
          success: false,
          message: 'Exchange preferences not found'
        });
      }

      // Remove user from blocked list
      preferences.blockedUsers = preferences.blockedUsers.filter(
        blockedId => blockedId.toString() !== userId
      );

      await preferences.save();

      res.json({
        success: true,
        message: 'User unblocked successfully'
      });
    } catch (error: any) {
      res.status(500).json({
        success: false,
        message: error.message || 'Failed to unblock user'
      });
    }
  }
);

// Get blocked users list
router.get('/blocked',
  authenticate,
  async (req: express.Request, res: express.Response) => {
    try {
      const userId = req.user!.id;

      const preferences = await ExchangePreferences.findOne({ user: userId })
        .populate('blockedUsers', 'name profilePicture');

      const blockedUsers = preferences?.blockedUsers || [];

      res.json({
        success: true,
        data: blockedUsers
      });
    } catch (error: any) {
      res.status(500).json({
        success: false,
        message: error.message || 'Failed to fetch blocked users'
      });
    }
  }
);

// Reset preferences to default
router.post('/reset',
  authenticate,
  async (req: express.Request, res: express.Response) => {
    try {
      const userId = req.user!.id;

      const defaultPrefs = ExchangePreferences.getDefaultPreferences(userId);
      
      let preferences = await ExchangePreferences.findOne({ user: userId });
      
      if (preferences) {
        // Keep blocked users but reset everything else
        const blockedUsers = preferences.blockedUsers;
        Object.assign(preferences, defaultPrefs);
        preferences.blockedUsers = blockedUsers;
      } else {
        preferences = new ExchangePreferences(defaultPrefs);
      }

      await preferences.save();

      res.json({
        success: true,
        message: 'Exchange preferences reset to default',
        data: preferences
      });
    } catch (error: any) {
      res.status(500).json({
        success: false,
        message: error.message || 'Failed to reset exchange preferences'
      });
    }
  }
);

export default router;
