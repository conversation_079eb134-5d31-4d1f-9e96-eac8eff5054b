import { Router } from 'express';
import { body, param, validationResult } from 'express-validator';
import { authenticate } from '@/middleware/auth';
import { handleValidationErrors } from '@/middleware/validation';
import PaymentMethod, { IPaymentMethod } from '@/models/PaymentMethod';
import { logger } from '@/utils/logger';
import { AuthenticatedRequest } from '@/types/auth';

const router = Router();

// Get user's payment methods
router.get('/',
  authenticate,
  async (req: AuthenticatedRequest, res) => {
    try {
      const userId = req.user!.id;

      const paymentMethods = await PaymentMethod.find({
        user: userId,
        isActive: true,
      }).sort({ isDefault: -1, createdAt: -1 });

      res.json({
        success: true,
        data: paymentMethods,
      });
    } catch (error: any) {
      logger.error('Failed to get payment methods:', error);
      res.status(400).json({
        success: false,
        message: error.message || 'Failed to get payment methods',
      });
    }
  }
);

// Add new payment method
router.post('/',
  authenticate,
  [
    body('type').isIn(['mpesa', 'card', 'bank_account']).withMessage('Invalid payment method type'),
    body('provider').isIn(['safaricom', 'visa', 'mastercard', 'equity', 'kcb', 'coop', 'other']).withMessage('Invalid provider'),
    body('name').isLength({ min: 1, max: 50 }).withMessage('Name is required and must be less than 50 characters'),
    body('isDefault').optional().isBoolean().withMessage('isDefault must be a boolean'),
    
    // M-Pesa specific validation
    body('mpesaDetails.phoneNumber')
      .if(body('type').equals('mpesa'))
      .isMobilePhone('any')
      .withMessage('Valid phone number is required for M-Pesa'),
    body('mpesaDetails.accountName')
      .if(body('type').equals('mpesa'))
      .optional()
      .isLength({ max: 100 })
      .withMessage('Account name must be less than 100 characters'),
    
    // Card specific validation
    body('cardDetails.lastFourDigits')
      .if(body('type').equals('card'))
      .matches(/^\d{4}$/)
      .withMessage('Last four digits must be exactly 4 numbers'),
    body('cardDetails.expiryMonth')
      .if(body('type').equals('card'))
      .isInt({ min: 1, max: 12 })
      .withMessage('Expiry month must be between 1 and 12'),
    body('cardDetails.expiryYear')
      .if(body('type').equals('card'))
      .isInt({ min: new Date().getFullYear() })
      .withMessage('Expiry year must be current year or later'),
    body('cardDetails.cardholderName')
      .if(body('type').equals('card'))
      .isLength({ min: 1, max: 100 })
      .withMessage('Cardholder name is required'),
    body('cardDetails.brand')
      .if(body('type').equals('card'))
      .isIn(['visa', 'mastercard', 'amex'])
      .withMessage('Invalid card brand'),
  ],
  handleValidationErrors,
  async (req: AuthenticatedRequest, res) => {
    try {
      const userId = req.user!.id;
      const paymentMethodData = req.body;

      // Check if this is the first payment method for the user
      const existingCount = await PaymentMethod.countDocuments({
        user: userId,
        isActive: true,
      });

      const paymentMethod = new PaymentMethod({
        user: userId,
        type: paymentMethodData.type,
        provider: paymentMethodData.provider,
        name: paymentMethodData.name,
        isDefault: paymentMethodData.isDefault || existingCount === 0, // First method is default
        isActive: true,
        mpesaDetails: paymentMethodData.mpesaDetails,
        cardDetails: paymentMethodData.cardDetails,
        bankDetails: paymentMethodData.bankDetails,
        stats: {
          totalTransactions: 0,
          totalAmount: 0,
          successRate: 0,
        },
        verificationStatus: 'pending',
        verificationAttempts: 0,
        metadata: paymentMethodData.metadata,
      });

      await paymentMethod.save();

      logger.info('Payment method added successfully', {
        userId,
        paymentMethodId: paymentMethod._id,
        type: paymentMethod.type,
      });

      res.status(201).json({
        success: true,
        message: 'Payment method added successfully',
        data: paymentMethod,
      });
    } catch (error: any) {
      logger.error('Failed to add payment method:', error);
      res.status(400).json({
        success: false,
        message: error.message || 'Failed to add payment method',
      });
    }
  }
);

// Update payment method
router.patch('/:id',
  authenticate,
  [
    param('id').isMongoId().withMessage('Invalid payment method ID'),
    body('name').optional().isLength({ min: 1, max: 50 }).withMessage('Name must be less than 50 characters'),
    body('isDefault').optional().isBoolean().withMessage('isDefault must be a boolean'),
    body('isActive').optional().isBoolean().withMessage('isActive must be a boolean'),
  ],
  handleValidationErrors,
  async (req: AuthenticatedRequest, res) => {
    try {
      const userId = req.user!.id;
      const { id } = req.params;
      const updates = req.body;

      const paymentMethod = await PaymentMethod.findOne({
        _id: id,
        user: userId,
      });

      if (!paymentMethod) {
        return res.status(404).json({
          success: false,
          message: 'Payment method not found',
        });
      }

      // Update allowed fields
      if (updates.name) paymentMethod.name = updates.name;
      if (typeof updates.isDefault === 'boolean') paymentMethod.isDefault = updates.isDefault;
      if (typeof updates.isActive === 'boolean') paymentMethod.isActive = updates.isActive;

      await paymentMethod.save();

      logger.info('Payment method updated successfully', {
        userId,
        paymentMethodId: id,
      });

      res.json({
        success: true,
        message: 'Payment method updated successfully',
        data: paymentMethod,
      });
    } catch (error: any) {
      logger.error('Failed to update payment method:', error);
      res.status(400).json({
        success: false,
        message: error.message || 'Failed to update payment method',
      });
    }
  }
);

// Delete payment method
router.delete('/:id',
  authenticate,
  [
    param('id').isMongoId().withMessage('Invalid payment method ID'),
  ],
  handleValidationErrors,
  async (req: AuthenticatedRequest, res) => {
    try {
      const userId = req.user!.id;
      const { id } = req.params;

      const paymentMethod = await PaymentMethod.findOne({
        _id: id,
        user: userId,
      });

      if (!paymentMethod) {
        return res.status(404).json({
          success: false,
          message: 'Payment method not found',
        });
      }

      // Soft delete by setting isActive to false
      paymentMethod.isActive = false;
      
      // If this was the default method, make another method default
      if (paymentMethod.isDefault) {
        paymentMethod.isDefault = false;
        
        const nextMethod = await PaymentMethod.findOne({
          user: userId,
          _id: { $ne: id },
          isActive: true,
        });
        
        if (nextMethod) {
          nextMethod.isDefault = true;
          await nextMethod.save();
        }
      }

      await paymentMethod.save();

      logger.info('Payment method deleted successfully', {
        userId,
        paymentMethodId: id,
      });

      res.json({
        success: true,
        message: 'Payment method deleted successfully',
      });
    } catch (error: any) {
      logger.error('Failed to delete payment method:', error);
      res.status(400).json({
        success: false,
        message: error.message || 'Failed to delete payment method',
      });
    }
  }
);

// Set default payment method
router.patch('/:id/set-default',
  authenticate,
  [
    param('id').isMongoId().withMessage('Invalid payment method ID'),
  ],
  handleValidationErrors,
  async (req: AuthenticatedRequest, res) => {
    try {
      const userId = req.user!.id;
      const { id } = req.params;

      const paymentMethod = await PaymentMethod.findOne({
        _id: id,
        user: userId,
        isActive: true,
      });

      if (!paymentMethod) {
        return res.status(404).json({
          success: false,
          message: 'Payment method not found',
        });
      }

      paymentMethod.isDefault = true;
      await paymentMethod.save();

      logger.info('Default payment method set successfully', {
        userId,
        paymentMethodId: id,
      });

      res.json({
        success: true,
        message: 'Default payment method set successfully',
        data: paymentMethod,
      });
    } catch (error: any) {
      logger.error('Failed to set default payment method:', error);
      res.status(400).json({
        success: false,
        message: error.message || 'Failed to set default payment method',
      });
    }
  }
);

// Verify payment method (placeholder for future implementation)
router.post('/:id/verify',
  authenticate,
  [
    param('id').isMongoId().withMessage('Invalid payment method ID'),
  ],
  handleValidationErrors,
  async (req: AuthenticatedRequest, res) => {
    try {
      const userId = req.user!.id;
      const { id } = req.params;

      const paymentMethod = await PaymentMethod.findOne({
        _id: id,
        user: userId,
        isActive: true,
      });

      if (!paymentMethod) {
        return res.status(404).json({
          success: false,
          message: 'Payment method not found',
        });
      }

      // For now, just mark as verified
      // In production, this would involve actual verification processes
      paymentMethod.verificationStatus = 'verified';
      paymentMethod.verificationAttempts += 1;
      paymentMethod.lastVerificationAttempt = new Date();

      if (paymentMethod.mpesaDetails) {
        paymentMethod.mpesaDetails.isVerified = true;
        paymentMethod.mpesaDetails.lastVerifiedAt = new Date();
      }

      await paymentMethod.save();

      logger.info('Payment method verified successfully', {
        userId,
        paymentMethodId: id,
      });

      res.json({
        success: true,
        message: 'Payment method verified successfully',
        data: paymentMethod,
      });
    } catch (error: any) {
      logger.error('Failed to verify payment method:', error);
      res.status(400).json({
        success: false,
        message: error.message || 'Failed to verify payment method',
      });
    }
  }
);

export default router;
