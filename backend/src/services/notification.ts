import mongoose from 'mongoose';
import Notification, { INotification } from '@/models/Notification';
import NotificationTemplate, { INotificationTemplate } from '@/models/NotificationTemplate';
import NotificationPreferences, { INotificationPreferences } from '@/models/NotificationPreferences';
import User, { IUser } from '@/models/User';
import { smsService } from './sms';
import { logger } from '@/utils/logger';

export interface CreateNotificationRequest {
  userId: string;
  type?: 'sms' | 'push' | 'email' | 'in_app';
  category: 'payment' | 'exchange' | 'system' | 'promotion' | 'reminder' | 'welcome' | 'token' | 'security';
  title?: string;
  message?: string;
  templateId?: string;
  templateVariables?: Record<string, any>;
  priority?: 'low' | 'medium' | 'high' | 'urgent';
  scheduledAt?: Date;
  relatedEntity?: {
    type: 'payment' | 'transaction' | 'exchange' | 'user' | 'item';
    id: string;
  };
  phoneNumber?: string;
  maxAttempts?: number;
}

export interface BulkNotificationRequest {
  userIds: string[];
  type?: 'sms' | 'push' | 'email' | 'in_app';
  category: 'payment' | 'exchange' | 'system' | 'promotion' | 'reminder' | 'welcome' | 'token' | 'security';
  templateId: string;
  templateVariables?: Record<string, any>;
  priority?: 'low' | 'medium' | 'high' | 'urgent';
  scheduledAt?: Date;
}

export interface NotificationStats {
  total: number;
  byStatus: Record<string, number>;
  byCategory: Record<string, number>;
  byType: Record<string, number>;
  totalCost: number;
  successRate: number;
}

export class NotificationService {
  /**
   * Create and send a single notification
   */
  async createNotification(request: CreateNotificationRequest): Promise<INotification> {
    try {
      // Get user and preferences
      const user = await User.findById(request.userId);
      if (!user) {
        throw new Error('User not found');
      }

      const preferences = await NotificationPreferences.getOrCreateForUser(request.userId);
      
      // Determine notification type
      const type = request.type || this.getPreferredType(preferences, request.category);
      
      // Check if user can receive this notification
      if (!preferences.canReceiveNotification(request.category, type, request.priority)) {
        logger.info('Notification blocked by user preferences', {
          userId: request.userId,
          category: request.category,
          type,
        });
        throw new Error('Notification blocked by user preferences');
      }

      // Check frequency limits
      const frequencyCheck = await preferences.checkFrequencyLimits();
      if (!frequencyCheck.canSend) {
        logger.info('Notification blocked by frequency limits', {
          userId: request.userId,
          reason: frequencyCheck.reason,
        });
        throw new Error(frequencyCheck.reason);
      }

      // Check quiet hours
      if (preferences.isInQuietHours(request.scheduledAt)) {
        // Reschedule to after quiet hours
        request.scheduledAt = this.getNextAvailableTime(preferences, request.scheduledAt);
        logger.info('Notification rescheduled due to quiet hours', {
          userId: request.userId,
          newScheduledAt: request.scheduledAt,
        });
      }

      let title = request.title;
      let message = request.message;

      // Use template if provided
      if (request.templateId) {
        const template = await NotificationTemplate.findById(request.templateId);
        if (!template || !template.isActive) {
          throw new Error('Template not found or inactive');
        }

        // Validate template variables
        const validation = template.validateVariables(request.templateVariables || {});
        if (!validation.isValid) {
          throw new Error(`Template validation failed: ${validation.errors.join(', ')}`);
        }

        // Check template limits
        const limitCheck = await template.checkLimits(request.userId);
        if (!limitCheck.canSend) {
          throw new Error(limitCheck.reason);
        }

        // Render template
        const rendered = template.renderMessage(request.templateVariables || {});
        title = rendered.title;
        message = rendered.message;

        // Update template usage
        await template.incrementUsage();
      }

      if (!title || !message) {
        throw new Error('Title and message are required');
      }

      // Create notification
      const notification = new Notification({
        user: request.userId,
        type,
        category: request.category,
        title,
        message,
        priority: request.priority || 'medium',
        scheduledAt: request.scheduledAt || new Date(),
        relatedEntity: request.relatedEntity,
        templateId: request.templateId,
        templateVariables: request.templateVariables,
        phoneNumber: request.phoneNumber || (type === 'sms' ? user.phoneNumber : undefined),
        maxAttempts: request.maxAttempts || 3,
      });

      await notification.save();

      // Send immediately if scheduled for now or past
      if (notification.scheduledAt <= new Date()) {
        await this.sendNotification(notification._id.toString());
      }

      logger.info('Notification created', {
        notificationId: notification._id,
        userId: request.userId,
        type,
        category: request.category,
      });

      return notification;

    } catch (error: any) {
      logger.error('Failed to create notification:', error);
      throw new Error(`Failed to create notification: ${error.message}`);
    }
  }

  /**
   * Create bulk notifications
   */
  async createBulkNotifications(request: BulkNotificationRequest): Promise<{
    created: number;
    failed: number;
    notifications: INotification[];
  }> {
    const results = {
      created: 0,
      failed: 0,
      notifications: [] as INotification[],
    };

    for (const userId of request.userIds) {
      try {
        const notification = await this.createNotification({
          userId,
          type: request.type,
          category: request.category,
          templateId: request.templateId,
          templateVariables: request.templateVariables,
          priority: request.priority,
          scheduledAt: request.scheduledAt,
        });

        results.notifications.push(notification);
        results.created++;

      } catch (error: any) {
        logger.warn('Failed to create notification for user', {
          userId,
          error: error.message,
        });
        results.failed++;
      }
    }

    logger.info('Bulk notifications created', {
      totalUsers: request.userIds.length,
      created: results.created,
      failed: results.failed,
    });

    return results;
  }

  /**
   * Send a notification
   */
  async sendNotification(notificationId: string): Promise<boolean> {
    try {
      const notification = await Notification.findById(notificationId)
        .populate('user', 'phoneNumber firstName lastName');

      if (!notification) {
        throw new Error('Notification not found');
      }

      if (notification.status !== 'pending') {
        logger.warn('Notification not in pending status', {
          notificationId,
          status: notification.status,
        });
        return false;
      }

      // Check if it's time to send
      if (notification.scheduledAt > new Date()) {
        logger.info('Notification not yet scheduled', {
          notificationId,
          scheduledAt: notification.scheduledAt,
        });
        return false;
      }

      // Send based on type
      let success = false;
      let messageId: string | undefined;
      let cost: string | undefined;

      switch (notification.type) {
        case 'sms':
          const smsResult = await this.sendSMS(notification);
          success = smsResult.success;
          messageId = smsResult.messageId;
          cost = smsResult.cost;
          break;

        case 'push':
          // TODO: Implement push notification sending
          success = await this.sendPushNotification(notification);
          break;

        case 'email':
          // TODO: Implement email sending
          success = await this.sendEmail(notification);
          break;

        case 'in_app':
          // In-app notifications are just stored in database
          success = true;
          break;

        default:
          throw new Error(`Unsupported notification type: ${notification.type}`);
      }

      // Update notification status
      if (success) {
        await notification.markAsSent(messageId, cost);
        logger.info('Notification sent successfully', {
          notificationId,
          type: notification.type,
          messageId,
        });
      } else {
        await notification.markAsFailed('Failed to send notification');
        logger.error('Failed to send notification', {
          notificationId,
          type: notification.type,
        });
      }

      return success;

    } catch (error: any) {
      logger.error('Error sending notification:', error);
      
      // Try to mark notification as failed
      try {
        const notification = await Notification.findById(notificationId);
        if (notification) {
          await notification.markAsFailed(error.message);
        }
      } catch (updateError) {
        logger.error('Failed to update notification status:', updateError);
      }

      return false;
    }
  }

  /**
   * Send SMS notification
   */
  private async sendSMS(notification: INotification): Promise<{
    success: boolean;
    messageId?: string;
    cost?: string;
  }> {
    if (!notification.phoneNumber) {
      throw new Error('Phone number required for SMS notification');
    }

    const result = await smsService.sendSMS({
      to: notification.phoneNumber,
      message: notification.message,
    });

    return {
      success: result.success,
      messageId: result.messageId,
      cost: result.cost,
    };
  }

  /**
   * Send push notification (placeholder)
   */
  private async sendPushNotification(notification: INotification): Promise<boolean> {
    // TODO: Implement push notification service integration
    logger.info('Push notification would be sent', {
      notificationId: notification._id,
      title: notification.title,
      message: notification.message,
    });
    return true;
  }

  /**
   * Send email notification (placeholder)
   */
  private async sendEmail(notification: INotification): Promise<boolean> {
    // TODO: Implement email service integration
    logger.info('Email notification would be sent', {
      notificationId: notification._id,
      title: notification.title,
      message: notification.message,
    });
    return true;
  }

  /**
   * Process pending notifications
   */
  async processPendingNotifications(limit: number = 100): Promise<{
    processed: number;
    successful: number;
    failed: number;
  }> {
    const pendingNotifications = await Notification.findPendingNotifications(limit);
    
    const results = {
      processed: 0,
      successful: 0,
      failed: 0,
    };

    for (const notification of pendingNotifications) {
      const success = await this.sendNotification(notification._id.toString());
      
      results.processed++;
      if (success) {
        results.successful++;
      } else {
        results.failed++;
      }
    }

    logger.info('Processed pending notifications', results);
    return results;
  }

  /**
   * Get preferred notification type based on user preferences
   */
  private getPreferredType(preferences: INotificationPreferences, category: string): 'sms' | 'push' | 'email' | 'in_app' {
    const categoryPrefs = preferences.categories[category];
    
    if (!categoryPrefs || !categoryPrefs.enabled) {
      return 'in_app'; // Fallback
    }

    // Return first enabled channel in order of preference
    const preferenceOrder: ('sms' | 'push' | 'email' | 'in_app')[] = ['sms', 'push', 'in_app', 'email'];
    
    for (const type of preferenceOrder) {
      if (categoryPrefs.channels.includes(type) && preferences.channels[type]?.enabled) {
        return type;
      }
    }

    return 'in_app'; // Final fallback
  }

  /**
   * Get next available time after quiet hours
   */
  private getNextAvailableTime(preferences: INotificationPreferences, scheduledAt?: Date): Date {
    if (!preferences.quietHours.enabled || !scheduledAt) {
      return scheduledAt || new Date();
    }

    const [endHour, endMinute] = preferences.quietHours.endTime.split(':').map(Number);
    const nextDay = new Date(scheduledAt);
    nextDay.setHours(endHour, endMinute, 0, 0);

    // If end time is tomorrow (overnight quiet hours)
    if (preferences.quietHours.startTime > preferences.quietHours.endTime) {
      if (scheduledAt.getHours() >= parseInt(preferences.quietHours.startTime.split(':')[0])) {
        nextDay.setDate(nextDay.getDate() + 1);
      }
    }

    return nextDay;
  }

  /**
   * Get notifications for a user
   */
  async getUserNotifications(userId: string, options: {
    type?: string;
    category?: string;
    status?: string;
    limit?: number;
    skip?: number;
  } = {}): Promise<INotification[]> {
    return Notification.findByUser(userId, options);
  }

  /**
   * Mark notification as read (for in-app notifications)
   */
  async markAsRead(notificationId: string, userId: string): Promise<boolean> {
    try {
      const notification = await Notification.findOne({
        _id: notificationId,
        user: userId,
      });

      if (!notification) {
        return false;
      }

      if (notification.type === 'in_app' && notification.status === 'sent') {
        await notification.markAsDelivered();
        return true;
      }

      return false;
    } catch (error) {
      logger.error('Failed to mark notification as read:', error);
      return false;
    }
  }

  /**
   * Cancel a pending notification
   */
  async cancelNotification(notificationId: string, userId?: string): Promise<boolean> {
    try {
      const query: any = { _id: notificationId, status: 'pending' };
      if (userId) query.user = userId;

      const notification = await Notification.findOne(query);
      if (!notification) {
        return false;
      }

      notification.status = 'cancelled';
      await notification.save();

      logger.info('Notification cancelled', {
        notificationId,
        userId: notification.user,
      });

      return true;
    } catch (error) {
      logger.error('Failed to cancel notification:', error);
      return false;
    }
  }

  /**
   * Retry failed notification
   */
  async retryNotification(notificationId: string): Promise<boolean> {
    try {
      const notification = await Notification.findById(notificationId);
      if (!notification) {
        return false;
      }

      if (!notification.canRetry) {
        logger.warn('Notification cannot be retried', {
          notificationId,
          attempts: notification.attempts,
          maxAttempts: notification.maxAttempts,
        });
        return false;
      }

      await notification.retry();
      return await this.sendNotification(notificationId);

    } catch (error) {
      logger.error('Failed to retry notification:', error);
      return false;
    }
  }

  /**
   * Update delivery status from webhook
   */
  async updateDeliveryStatus(messageId: string, status: string, deliveredAt?: Date): Promise<boolean> {
    try {
      const notification = await Notification.findOne({ messageId });
      if (!notification) {
        logger.warn('Notification not found for delivery update', { messageId });
        return false;
      }

      if (status === 'Success' || status === 'delivered') {
        await notification.markAsDelivered();
      } else if (status === 'Failed' || status === 'failed') {
        await notification.markAsFailed(`Delivery failed: ${status}`);
      }

      logger.info('Delivery status updated', {
        notificationId: notification._id,
        messageId,
        status,
      });

      return true;
    } catch (error) {
      logger.error('Failed to update delivery status:', error);
      return false;
    }
  }

  /**
   * Get notification statistics
   */
  async getNotificationStats(userId?: string, dateRange?: {
    startDate: Date;
    endDate: Date;
  }): Promise<NotificationStats> {
    try {
      const matchStage: any = {};

      if (userId) {
        matchStage.user = new mongoose.Types.ObjectId(userId);
      }

      if (dateRange) {
        matchStage.createdAt = {
          $gte: dateRange.startDate,
          $lte: dateRange.endDate,
        };
      }

      const stats = await Notification.aggregate([
        { $match: matchStage },
        {
          $group: {
            _id: null,
            total: { $sum: 1 },
            byStatus: {
              $push: {
                status: '$status',
                count: 1,
              },
            },
            byCategory: {
              $push: {
                category: '$category',
                count: 1,
              },
            },
            byType: {
              $push: {
                type: '$type',
                count: 1,
              },
            },
            totalCost: {
              $sum: {
                $toDouble: {
                  $ifNull: ['$cost', '0'],
                },
              },
            },
            successful: {
              $sum: {
                $cond: [
                  { $in: ['$status', ['sent', 'delivered']] },
                  1,
                  0,
                ],
              },
            },
          },
        },
      ]);

      if (!stats.length) {
        return {
          total: 0,
          byStatus: {},
          byCategory: {},
          byType: {},
          totalCost: 0,
          successRate: 0,
        };
      }

      const result = stats[0];

      // Process grouped data
      const byStatus: Record<string, number> = {};
      const byCategory: Record<string, number> = {};
      const byType: Record<string, number> = {};

      result.byStatus.forEach((item: any) => {
        byStatus[item.status] = (byStatus[item.status] || 0) + 1;
      });

      result.byCategory.forEach((item: any) => {
        byCategory[item.category] = (byCategory[item.category] || 0) + 1;
      });

      result.byType.forEach((item: any) => {
        byType[item.type] = (byType[item.type] || 0) + 1;
      });

      return {
        total: result.total,
        byStatus,
        byCategory,
        byType,
        totalCost: result.totalCost,
        successRate: result.total > 0 ? (result.successful / result.total) * 100 : 0,
      };

    } catch (error) {
      logger.error('Failed to get notification stats:', error);
      throw new Error('Failed to get notification stats');
    }
  }

  /**
   * Clean up old notifications
   */
  async cleanupOldNotifications(daysOld: number = 90): Promise<number> {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - daysOld);

      const result = await Notification.deleteMany({
        createdAt: { $lt: cutoffDate },
        status: { $in: ['delivered', 'failed', 'cancelled'] },
      });

      logger.info('Cleaned up old notifications', {
        deletedCount: result.deletedCount,
        cutoffDate,
      });

      return result.deletedCount || 0;
    } catch (error) {
      logger.error('Failed to cleanup old notifications:', error);
      return 0;
    }
  }

  /**
   * Get service health status
   */
  async getServiceHealth(): Promise<{
    isHealthy: boolean;
    smsService: boolean;
    pendingCount: number;
    failedCount: number;
    lastProcessedAt?: Date;
  }> {
    try {
      const smsStats = smsService.getServiceStats();
      const pendingCount = await Notification.countDocuments({ status: 'pending' });
      const failedCount = await Notification.countDocuments({
        status: 'failed',
        attempts: { $gte: 3 },
      });

      const lastProcessed = await Notification.findOne(
        { status: { $in: ['sent', 'delivered'] } },
        {},
        { sort: { sentAt: -1 } }
      );

      return {
        isHealthy: smsStats.isConfigured && pendingCount < 1000 && failedCount < 100,
        smsService: smsStats.isConfigured,
        pendingCount,
        failedCount,
        lastProcessedAt: lastProcessed?.sentAt,
      };
    } catch (error) {
      logger.error('Failed to get service health:', error);
      return {
        isHealthy: false,
        smsService: false,
        pendingCount: 0,
        failedCount: 0,
      };
    }
  }
}

// Export singleton instance
export const notificationService = new NotificationService();
