import { logger } from '@/utils/logger';
import { generateOTP } from '@/utils/helpers';
import { smsService } from './sms';

interface OTPData {
  phoneNumber: string;
  otp: string;
  expiresAt: Date;
  attempts: number;
  isUsed: boolean;
}

// In-memory OTP storage (in production, use Redis or database)
const otpStore = new Map<string, OTPData>();

export class OTPService {
  private maxAttempts = 3;
  private otpExpiryMinutes = 10;
  private resendCooldownMinutes = 1;

  async generateAndSendOTP(phoneNumber: string): Promise<{ success: boolean; message: string }> {
    try {
      // Check if there's a recent OTP request
      const existingOTP = otpStore.get(phoneNumber);
      if (existingOTP && !this.canResendOTP(existingOTP)) {
        return {
          success: false,
          message: 'Please wait before requesting another OTP',
        };
      }

      // Generate new OTP
      const otp = generateOTP(6);
      const expiresAt = new Date(Date.now() + this.otpExpiryMinutes * 60 * 1000);

      // Store OTP
      otpStore.set(phoneNumber, {
        phoneNumber,
        otp,
        expiresAt,
        attempts: 0,
        isUsed: false,
      });

      // Send OTP via SMS
      const smsResult = await smsService.sendOTP(phoneNumber, otp);

      if (smsResult.success) {
        logger.info('OTP sent successfully', { phoneNumber, messageId: smsResult.messageId });
        return {
          success: true,
          message: 'OTP sent successfully',
        };
      } else {
        logger.error('Failed to send OTP SMS', { phoneNumber, error: smsResult.error });
        return {
          success: false,
          message: 'Failed to send OTP. Please try again.',
        };
      }

    } catch (error) {
      logger.error('Error generating and sending OTP:', error);
      return {
        success: false,
        message: 'An error occurred while sending OTP',
      };
    }
  }

  async verifyOTP(phoneNumber: string, providedOTP: string): Promise<{ success: boolean; message: string }> {
    try {
      const otpData = otpStore.get(phoneNumber);

      if (!otpData) {
        return {
          success: false,
          message: 'No OTP found for this phone number',
        };
      }

      // Check if OTP is already used
      if (otpData.isUsed) {
        return {
          success: false,
          message: 'OTP has already been used',
        };
      }

      // Check if OTP has expired
      if (new Date() > otpData.expiresAt) {
        otpStore.delete(phoneNumber);
        return {
          success: false,
          message: 'OTP has expired. Please request a new one.',
        };
      }

      // Check if max attempts exceeded
      if (otpData.attempts >= this.maxAttempts) {
        otpStore.delete(phoneNumber);
        return {
          success: false,
          message: 'Maximum verification attempts exceeded. Please request a new OTP.',
        };
      }

      // Increment attempts
      otpData.attempts++;

      // Verify OTP
      if (otpData.otp === providedOTP) {
        // Mark as used
        otpData.isUsed = true;
        
        logger.info('OTP verified successfully', { phoneNumber });
        
        // Clean up after successful verification (optional, or keep for audit)
        setTimeout(() => {
          otpStore.delete(phoneNumber);
        }, 5 * 60 * 1000); // Delete after 5 minutes

        return {
          success: true,
          message: 'OTP verified successfully',
        };
      } else {
        const remainingAttempts = this.maxAttempts - otpData.attempts;
        
        if (remainingAttempts > 0) {
          return {
            success: false,
            message: `Invalid OTP. ${remainingAttempts} attempt(s) remaining.`,
          };
        } else {
          otpStore.delete(phoneNumber);
          return {
            success: false,
            message: 'Invalid OTP. Maximum attempts exceeded. Please request a new OTP.',
          };
        }
      }

    } catch (error) {
      logger.error('Error verifying OTP:', error);
      return {
        success: false,
        message: 'An error occurred while verifying OTP',
      };
    }
  }

  private canResendOTP(otpData: OTPData): boolean {
    const cooldownPeriod = this.resendCooldownMinutes * 60 * 1000;
    const timeSinceLastOTP = Date.now() - (otpData.expiresAt.getTime() - this.otpExpiryMinutes * 60 * 1000);
    
    return timeSinceLastOTP >= cooldownPeriod;
  }

  // Clean up expired OTPs (should be called periodically)
  cleanupExpiredOTPs(): void {
    const now = new Date();
    
    for (const [phoneNumber, otpData] of otpStore.entries()) {
      if (now > otpData.expiresAt) {
        otpStore.delete(phoneNumber);
      }
    }
    
    logger.info(`Cleaned up expired OTPs. Active OTPs: ${otpStore.size}`);
  }

  // Get OTP statistics (for admin/monitoring)
  getOTPStats(): {
    activeOTPs: number;
    totalGenerated: number;
  } {
    return {
      activeOTPs: otpStore.size,
      totalGenerated: otpStore.size, // This would be tracked differently in production
    };
  }

  // Force delete OTP (for admin use)
  deleteOTP(phoneNumber: string): boolean {
    return otpStore.delete(phoneNumber);
  }
}

export const otpService = new OTPService();

// Clean up expired OTPs every 5 minutes
setInterval(() => {
  otpService.cleanupExpiredOTPs();
}, 5 * 60 * 1000);
