import mongoose from 'mongoose';
import Transaction from '../models/Transaction';
import ClothingItem from '../models/ClothingItem';
import User from '../models/User';
import { tokenService } from './token';
import { achievementService } from './achievement';

export interface TokenPurchaseData {
  itemId: string;
  offeredAmount: number;
  deliveryMethod: 'pickup' | 'delivery' | 'meetup';
  deliveryAddress?: {
    county: string;
    town: string;
    specificLocation: string;
    contactPhone: string;
  };
  meetupLocation?: {
    name: string;
    address: string;
    coordinates?: {
      latitude: number;
      longitude: number;
    };
    scheduledTime: Date;
  };
  message?: string;
}

export interface PriceNegotiationData {
  counterOffer: number;
  message?: string;
}

class TokenPurchaseService {
  // Create a token purchase transaction
  async createTokenPurchase(buyerId: string, purchaseData: TokenPurchaseData) {
    const session = await mongoose.startSession();
    session.startTransaction();

    try {
      // Validate buyer exists and has sufficient balance
      const buyer = await User.findById(buyerId).session(session);
      if (!buyer) {
        throw new Error('Buyer not found');
      }

      const buyerBalance = await tokenService.getBalance(buyerId);
      if (buyerBalance < purchaseData.offeredAmount) {
        throw new Error('Insufficient token balance');
      }

      // Validate item exists and is available
      const item = await ClothingItem.findById(purchaseData.itemId).session(session);
      if (!item) {
        throw new Error('Item not found');
      }

      if (item.status !== 'available') {
        throw new Error('Item is not available for purchase');
      }

      if (item.owner.toString() === buyerId) {
        throw new Error('You cannot purchase your own item');
      }

      // Validate seller exists
      const seller = await User.findById(item.owner).session(session);
      if (!seller) {
        throw new Error('Seller not found');
      }

      // Check if item has a set price or if this is an offer
      const isDirectPurchase = item.tokenPrice && purchaseData.offeredAmount >= item.tokenPrice;
      const initialStatus = isDirectPurchase ? 'accepted' : 'pending';

      // Create transaction
      const transaction = new Transaction({
        type: 'token_purchase',
        status: initialStatus,
        initiator: buyerId,
        recipient: item.owner,
        recipientItems: [purchaseData.itemId],
        tokenAmount: purchaseData.offeredAmount,
        deliveryMethod: purchaseData.deliveryMethod,
        deliveryAddress: purchaseData.deliveryAddress,
        meetupLocation: purchaseData.meetupLocation,
        timeline: [{
          status: initialStatus,
          timestamp: new Date(),
          updatedBy: buyerId,
          note: purchaseData.message || (isDirectPurchase ? 'Direct token purchase' : 'Token purchase offer')
        }]
      });

      await transaction.save({ session });

      if (isDirectPurchase) {
        // Process immediate purchase
        await this.processTokenPurchase(transaction._id.toString(), session);
      } else {
        // Update item status to pending
        item.status = 'pending_sale';
        await item.save({ session });
      }

      await session.commitTransaction();
      return transaction;

    } catch (error) {
      await session.abortTransaction();
      throw error;
    } finally {
      session.endSession();
    }
  }

  // Accept a token purchase offer
  async acceptTokenPurchase(transactionId: string, sellerId: string) {
    const session = await mongoose.startSession();
    session.startTransaction();

    try {
      const transaction = await Transaction.findById(transactionId).session(session);
      if (!transaction) {
        throw new Error('Transaction not found');
      }

      if (transaction.type !== 'token_purchase') {
        throw new Error('Transaction is not a token purchase');
      }

      if (transaction.status !== 'pending') {
        throw new Error('Transaction is not pending');
      }

      if (transaction.recipient?.toString() !== sellerId) {
        throw new Error('You are not the seller for this transaction');
      }

      // Process the purchase
      await this.processTokenPurchase(transactionId, session);

      await session.commitTransaction();
      return transaction;

    } catch (error) {
      await session.abortTransaction();
      throw error;
    } finally {
      session.endSession();
    }
  }

  // Decline a token purchase offer
  async declineTokenPurchase(transactionId: string, sellerId: string, reason?: string) {
    const session = await mongoose.startSession();
    session.startTransaction();

    try {
      const transaction = await Transaction.findById(transactionId).session(session);
      if (!transaction) {
        throw new Error('Transaction not found');
      }

      if (transaction.type !== 'token_purchase') {
        throw new Error('Transaction is not a token purchase');
      }

      if (transaction.status !== 'pending') {
        throw new Error('Transaction is not pending');
      }

      if (transaction.recipient?.toString() !== sellerId) {
        throw new Error('You are not the seller for this transaction');
      }

      // Update transaction status
      transaction.status = 'declined';
      transaction.timeline.push({
        status: 'declined',
        timestamp: new Date(),
        updatedBy: new mongoose.Types.ObjectId(sellerId),
        note: reason || 'Purchase offer declined'
      });

      // Release item back to available status
      await ClothingItem.updateOne(
        { _id: { $in: transaction.recipientItems || [] } },
        { status: 'available' },
        { session }
      );

      await transaction.save({ session });
      await session.commitTransaction();

      return transaction;

    } catch (error) {
      await session.abortTransaction();
      throw error;
    } finally {
      session.endSession();
    }
  }

  // Create a counter offer for price negotiation
  async createCounterOffer(transactionId: string, sellerId: string, negotiationData: PriceNegotiationData) {
    const session = await mongoose.startSession();
    session.startTransaction();

    try {
      const transaction = await Transaction.findById(transactionId).session(session);
      if (!transaction) {
        throw new Error('Transaction not found');
      }

      if (transaction.type !== 'token_purchase') {
        throw new Error('Transaction is not a token purchase');
      }

      if (transaction.status !== 'pending') {
        throw new Error('Transaction is not pending');
      }

      if (transaction.recipient?.toString() !== sellerId) {
        throw new Error('You are not the seller for this transaction');
      }

      // Update transaction with counter offer
      transaction.tokenAmount = negotiationData.counterOffer;
      transaction.timeline.push({
        status: 'counter_offered',
        timestamp: new Date(),
        updatedBy: new mongoose.Types.ObjectId(sellerId),
        note: negotiationData.message || `Counter offer: ${negotiationData.counterOffer} tokens`
      });

      await transaction.save({ session });
      await session.commitTransaction();

      return transaction;

    } catch (error) {
      await session.abortTransaction();
      throw error;
    } finally {
      session.endSession();
    }
  }

  // Accept a counter offer
  async acceptCounterOffer(transactionId: string, buyerId: string) {
    const session = await mongoose.startSession();
    session.startTransaction();

    try {
      const transaction = await Transaction.findById(transactionId).session(session);
      if (!transaction) {
        throw new Error('Transaction not found');
      }

      if (transaction.initiator.toString() !== buyerId) {
        throw new Error('You are not the buyer for this transaction');
      }

      // Check if buyer has sufficient balance for the counter offer
      const buyerBalance = await tokenService.getBalance(buyerId);
      if (buyerBalance < transaction.tokenAmount!) {
        throw new Error('Insufficient token balance for counter offer');
      }

      // Process the purchase with the counter offer amount
      await this.processTokenPurchase(transactionId, session);

      await session.commitTransaction();
      return transaction;

    } catch (error) {
      await session.abortTransaction();
      throw error;
    } finally {
      session.endSession();
    }
  }

  // Complete a token purchase transaction
  async completeTokenPurchase(transactionId: string, userId: string, completionData: {
    rating?: number;
    comment?: string;
    qualityConfirmed: boolean;
  }) {
    const session = await mongoose.startSession();
    session.startTransaction();

    try {
      const transaction = await Transaction.findById(transactionId).session(session);
      if (!transaction) {
        throw new Error('Transaction not found');
      }

      if (transaction.type !== 'token_purchase') {
        throw new Error('Transaction is not a token purchase');
      }

      if (transaction.status !== 'accepted') {
        throw new Error('Transaction is not in accepted status');
      }

      // Check if user is part of this transaction
      const isBuyer = transaction.initiator.toString() === userId;
      const isSeller = transaction.recipient?.toString() === userId;

      if (!isBuyer && !isSeller) {
        throw new Error('You are not part of this transaction');
      }

      // Add rating if provided
      if (completionData.rating) {
        if (isBuyer) {
          transaction.ratings = transaction.ratings || {};
          transaction.ratings.initiatorRating = {
            score: completionData.rating,
            comment: completionData.comment,
            ratedAt: new Date()
          };
        } else {
          transaction.ratings = transaction.ratings || {};
          transaction.ratings.recipientRating = {
            score: completionData.rating,
            comment: completionData.comment,
            ratedAt: new Date()
          };
        }
      }

      // Complete the transaction
      transaction.status = 'completed';
      transaction.timeline.push({
        status: 'completed',
        timestamp: new Date(),
        updatedBy: new mongoose.Types.ObjectId(userId),
        note: 'Token purchase completed successfully'
      });

      // Transfer item ownership
      await ClothingItem.updateOne(
        { _id: { $in: transaction.recipientItems || [] } },
        { 
          owner: transaction.initiator,
          status: 'available',
          $inc: { 'stats.timesPurchased': 1 }
        },
        { session }
      );

      // Update user statistics
      await User.updateOne(
        { _id: transaction.initiator },
        { $inc: { totalPurchases: 1, sustainabilityScore: 10 } },
        { session }
      );

      await User.updateOne(
        { _id: transaction.recipient },
        { $inc: { totalSales: 1, sustainabilityScore: 10 } },
        { session }
      );

      // Award completion bonus tokens to seller
      const completionBonus = 10;
      const qualityBonus = completionData.rating ? Math.round(completionData.rating * 5) : 0;

      await tokenService.addTokens(transaction.recipient!.toString(), completionBonus + qualityBonus, 'sale_completed', {
        transactionId: transaction._id.toString(),
        rating: completionData.rating,
        qualityBonus
      }, session);

      // Check for achievements
      await Promise.all([
        achievementService.checkUserAchievements(transaction.initiator.toString()),
        achievementService.checkUserAchievements(transaction.recipient!.toString())
      ]);

      await transaction.save({ session });
      await session.commitTransaction();

      return transaction;

    } catch (error) {
      await session.abortTransaction();
      throw error;
    } finally {
      session.endSession();
    }
  }

  // Get purchase recommendations based on user preferences and budget
  async getPurchaseRecommendations(userId: string, budget?: number, category?: string) {
    try {
      const user = await User.findById(userId);
      if (!user) {
        throw new Error('User not found');
      }

      const userBalance = await tokenService.getBalance(userId);
      const maxBudget = budget || userBalance;

      const matchQuery: any = {
        owner: { $ne: new mongoose.Types.ObjectId(userId) },
        status: 'available',
        tokenPrice: { $lte: maxBudget, $gt: 0 }
      };

      if (category) {
        matchQuery.category = category;
      }

      const recommendations = await ClothingItem.aggregate([
        { $match: matchQuery },
        {
          $lookup: {
            from: 'users',
            localField: 'owner',
            foreignField: '_id',
            as: 'ownerInfo'
          }
        },
        { $unwind: '$ownerInfo' },
        {
          $match: {
            'ownerInfo.location.county': user.location.county, // Same county preference
            'ownerInfo.rating': { $gte: 3.0 } // Good rating sellers only
          }
        },
        {
          $addFields: {
            valueScore: {
              $multiply: [
                { $divide: ['$tokenPrice', maxBudget] }, // Price factor
                { $cond: [{ $eq: ['$condition', 'excellent'] }, 1.5, 1] } // Quality factor
              ]
            }
          }
        },
        { $sort: { valueScore: 1, 'ownerInfo.rating': -1 } },
        { $limit: 20 }
      ]);

      return recommendations;

    } catch (error) {
      throw error;
    }
  }

  // Private method to process token purchase
  private async processTokenPurchase(transactionId: string, session: mongoose.ClientSession) {
    const transaction = await Transaction.findById(transactionId).session(session);
    if (!transaction) {
      throw new Error('Transaction not found');
    }

    // Transfer tokens from buyer to seller
    await tokenService.transferTokens(
      transaction.initiator.toString(),
      transaction.recipient!.toString(),
      transaction.tokenAmount!,
      'item_purchase',
      {
        transactionId: transaction._id.toString(),
        itemId: transaction.recipientItems![0].toString()
      },
      session
    );

    // Update transaction status
    transaction.status = 'accepted';
    transaction.timeline.push({
      status: 'accepted',
      timestamp: new Date(),
      updatedBy: transaction.recipient!,
      note: 'Purchase accepted, tokens transferred'
    });

    // Update item status
    await ClothingItem.updateOne(
      { _id: { $in: transaction.recipientItems || [] } },
      { status: 'in_transaction' },
      { session }
    );

    await transaction.save({ session });
  }
}

export const tokenPurchaseService = new TokenPurchaseService();
