import { Server } from 'socket.io';

let io: Server | null = null;

export const setSocketInstance = (socketInstance: Server) => {
  io = socketInstance;
};

export const getSocketInstance = (): Server => {
  if (!io) {
    throw new Error('Socket.io instance not initialized');
  }
  return io;
};

export const emitToUser = (userId: string, event: string, data: any) => {
  if (io) {
    io.emit(`user:${userId}:${event}`, data);
  }
};

export const emitToChat = (chatId: string, event: string, data: any) => {
  if (io) {
    io.to(`chat:${chatId}`).emit(event, data);
  }
};

export const emitToRoom = (room: string, event: string, data: any) => {
  if (io) {
    io.to(room).emit(event, data);
  }
};
