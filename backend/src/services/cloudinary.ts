import { v2 as cloudinary } from 'cloudinary';
import { logger } from '../utils/logger';

export interface CloudinaryConfig {
  cloudName: string;
  apiKey: string;
  apiSecret: string;
}

export interface UploadResult {
  success: boolean;
  url?: string;
  publicId?: string;
  error?: string;
}

export class CloudinaryService {
  private config: CloudinaryConfig;

  constructor() {
    this.config = {
      cloudName: process.env['CLOUDINARY_CLOUD_NAME'] || '',
      apiKey: process.env['CLOUDINARY_API_KEY'] || '',
      apiSecret: process.env['CLOUDINARY_API_SECRET'] || '',
    };

    // Configure Cloudinary
    if (this.config.cloudName && this.config.apiKey && this.config.apiSecret) {
      cloudinary.config({
        cloud_name: this.config.cloudName,
        api_key: this.config.apiKey,
        api_secret: this.config.apiSecret,
      });
    }
  }

  async uploadImage(
    buffer: Buffer,
    options: {
      folder?: string;
      publicId?: string;
      transformation?: any;
    } = {}
  ): Promise<UploadResult> {
    try {
      // Store image as Base64 in database instead of Cloudinary
      logger.info('Storing image as Base64 in database');

      // Convert buffer to base64
      const base64Image = buffer.toString('base64');

      // Detect image type from buffer
      let mimeType = 'image/jpeg'; // default
      if (buffer[0] === 0x89 && buffer[1] === 0x50) {
        mimeType = 'image/png';
      } else if (buffer[0] === 0xFF && buffer[1] === 0xD8) {
        mimeType = 'image/jpeg';
      } else if (buffer[0] === 0x47 && buffer[1] === 0x49) {
        mimeType = 'image/gif';
      }

      // Create data URL
      const dataUrl = `data:${mimeType};base64,${base64Image}`;

      // Generate a unique ID for the image
      const publicId = options.publicId || `img_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

      logger.info('Image stored successfully as Base64', {
        publicId,
        size: buffer.length,
        mimeType
      });

      return {
        success: true,
        url: dataUrl,
        publicId: publicId,
      };

    } catch (error) {
      logger.error('Failed to process image:', error);
      return {
        success: false,
        error: 'Failed to process image',
      };
    }
  }

  async uploadMultipleImages(
    buffers: Buffer[],
    options: {
      folder?: string;
      transformation?: any;
    } = {}
  ): Promise<UploadResult[]> {
    logger.info('Processing multiple images for Base64 storage', { count: buffers.length });

    const uploadPromises = buffers.map((buffer, index) =>
      this.uploadImage(buffer, {
        ...options,
        publicId: `${Date.now()}_${index}`,
      })
    );

    const results = await Promise.all(uploadPromises);
    logger.info('Multiple images processed successfully', {
      successCount: results.filter(r => r.success).length,
      totalCount: results.length
    });

    return results;
  }

  async deleteImage(publicId: string): Promise<boolean> {
    try {
      if (!this.config.cloudName || !this.config.apiKey || !this.config.apiSecret) {
        logger.warn('Cloudinary not configured, image deletion skipped');
        return false;
      }

      const result = await cloudinary.uploader.destroy(publicId);
      
      if (result.result === 'ok') {
        logger.info('Image deleted successfully from Cloudinary', { publicId });
        return true;
      }

      return false;

    } catch (error) {
      logger.error('Failed to delete image from Cloudinary:', error);
      return false;
    }
  }

  async deleteMultipleImages(publicIds: string[]): Promise<boolean[]> {
    const deletePromises = publicIds.map(publicId => this.deleteImage(publicId));
    return Promise.all(deletePromises);
  }

  generateTransformationUrl(
    publicId: string,
    transformations: any[]
  ): string {
    if (!this.config.cloudName) {
      return '';
    }

    return cloudinary.url(publicId, {
      transformation: transformations,
      secure: true,
    });
  }

  // Extract public ID from Cloudinary URL
  extractPublicId(url: string): string {
    try {
      // Handle Cloudinary URLs like: https://res.cloudinary.com/cloud/image/upload/v123456/folder/publicId.ext
      const urlParts = url.split('/');
      const uploadIndex = urlParts.findIndex(part => part === 'upload');

      if (uploadIndex === -1) return '';

      // Get everything after 'upload' and version (if present)
      let pathAfterUpload = urlParts.slice(uploadIndex + 1).join('/');

      // Remove version if present (starts with 'v' followed by numbers)
      if (pathAfterUpload.match(/^v\d+\//)) {
        pathAfterUpload = pathAfterUpload.replace(/^v\d+\//, '');
      }

      // Remove file extension
      return pathAfterUpload.replace(/\.[^/.]+$/, '');
    } catch (error) {
      logger.error('Failed to extract public ID from URL:', error);
      return '';
    }
  }

  // Generate different sizes for clothing images
  generateClothingImageUrls(publicId: string): {
    thumbnail: string;
    medium: string;
    large: string;
    original: string;
  } {
    const baseUrl = `https://res.cloudinary.com/${this.config.cloudName}/image/upload`;

    return {
      thumbnail: `${baseUrl}/w_150,h_150,c_fill,q_auto,f_auto/${publicId}`,
      medium: `${baseUrl}/w_400,h_400,c_limit,q_auto,f_auto/${publicId}`,
      large: `${baseUrl}/w_800,h_800,c_limit,q_auto,f_auto/${publicId}`,
      original: `${baseUrl}/q_auto,f_auto/${publicId}`,
    };
  }
}

export const cloudinaryService = new CloudinaryService();
