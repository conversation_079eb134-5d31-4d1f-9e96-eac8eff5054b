import Achievement, { IAchievement } from '../models/Achievement';
import UserAchievement, { IUserAchievement } from '../models/UserAchievement';
import User from '../models/User';
import { tokenService } from './token';
import logger from '../utils/logger';

export class AchievementService {
  
  /**
   * Initialize default achievements
   */
  async initializeDefaultAchievements(): Promise<void> {
    try {
      const defaultAchievements = [
        // Token Achievements
        {
          name: 'Token Saver',
          description: 'Accumulate 1,000 Pedi tokens',
          icon: '💰',
          category: 'tokens',
          type: 'bronze',
          requirements: { condition: 'pediTokens', value: 1000, operator: 'gte' },
          rewards: { tokens: 50, badge: 'token_saver', title: 'Token Saver' }
        },
        {
          name: 'Token Collector',
          description: 'Accumulate 5,000 Pedi tokens',
          icon: '💎',
          category: 'tokens',
          type: 'silver',
          requirements: { condition: 'pediTokens', value: 5000, operator: 'gte' },
          rewards: { tokens: 200, badge: 'token_collector', title: 'Token Collector' }
        },
        {
          name: 'Token Master',
          description: 'Accumulate 10,000 Pedi tokens',
          icon: '👑',
          category: 'tokens',
          type: 'gold',
          requirements: { condition: 'pediTokens', value: 10000, operator: 'gte' },
          rewards: { tokens: 500, badge: 'token_master', title: 'Token Master' }
        },

        // Activity Achievements
        {
          name: 'First Steps',
          description: 'List your first clothing item',
          icon: '👕',
          category: 'activity',
          type: 'bronze',
          requirements: { condition: 'totalListings', value: 1, operator: 'gte' },
          rewards: { tokens: 25, badge: 'first_steps' }
        },
        {
          name: 'Active Seller',
          description: 'List 10 clothing items',
          icon: '🛍️',
          category: 'activity',
          type: 'silver',
          requirements: { condition: 'totalListings', value: 10, operator: 'gte' },
          rewards: { tokens: 100, badge: 'active_seller', title: 'Active Seller' }
        },
        {
          name: 'Super Seller',
          description: 'List 50 clothing items',
          icon: '🏪',
          category: 'activity',
          type: 'gold',
          requirements: { condition: 'totalListings', value: 50, operator: 'gte' },
          rewards: { tokens: 300, badge: 'super_seller', title: 'Super Seller' }
        },

        // Swap Achievements
        {
          name: 'Swapper',
          description: 'Complete your first clothing swap',
          icon: '🔄',
          category: 'activity',
          type: 'bronze',
          requirements: { condition: 'totalSwaps', value: 1, operator: 'gte' },
          rewards: { tokens: 50, badge: 'swapper' }
        },
        {
          name: 'Swap Enthusiast',
          description: 'Complete 10 clothing swaps',
          icon: '🔁',
          category: 'activity',
          type: 'silver',
          requirements: { condition: 'totalSwaps', value: 10, operator: 'gte' },
          rewards: { tokens: 150, badge: 'swap_enthusiast', title: 'Swap Enthusiast' }
        },
        {
          name: 'Swap Master',
          description: 'Complete 25 clothing swaps',
          icon: '⚡',
          category: 'activity',
          type: 'gold',
          requirements: { condition: 'totalSwaps', value: 25, operator: 'gte' },
          rewards: { tokens: 400, badge: 'swap_master', title: 'Swap Master' }
        },

        // Sustainability Achievements
        {
          name: 'Eco Friendly',
          description: 'Reach 200 sustainability score',
          icon: '🌱',
          category: 'sustainability',
          type: 'bronze',
          requirements: { condition: 'sustainabilityScore', value: 200, operator: 'gte' },
          rewards: { tokens: 75, badge: 'eco_friendly', title: 'Eco Friendly' }
        },
        {
          name: 'Eco Conscious',
          description: 'Reach 500 sustainability score',
          icon: '🌿',
          category: 'sustainability',
          type: 'silver',
          requirements: { condition: 'sustainabilityScore', value: 500, operator: 'gte' },
          rewards: { tokens: 200, badge: 'eco_conscious', title: 'Eco Conscious' }
        },
        {
          name: 'Eco Warrior',
          description: 'Reach 800 sustainability score',
          icon: '🌍',
          category: 'sustainability',
          type: 'gold',
          requirements: { condition: 'sustainabilityScore', value: 800, operator: 'gte' },
          rewards: { tokens: 500, badge: 'eco_warrior', title: 'Eco Warrior' }
        },

        // Donation Achievements
        {
          name: 'Helper',
          description: 'Donate 3 clothing items to charity',
          icon: '🤝',
          category: 'social',
          type: 'bronze',
          requirements: { condition: 'totalDonations', value: 3, operator: 'gte' },
          rewards: { tokens: 100, badge: 'helper' }
        },
        {
          name: 'Generous Giver',
          description: 'Donate 10 clothing items to charity',
          icon: '💝',
          category: 'social',
          type: 'silver',
          requirements: { condition: 'totalDonations', value: 10, operator: 'gte' },
          rewards: { tokens: 250, badge: 'generous_giver', title: 'Generous Giver' }
        },
        {
          name: 'Charity Champion',
          description: 'Donate 25 clothing items to charity',
          icon: '🏆',
          category: 'social',
          type: 'gold',
          requirements: { condition: 'totalDonations', value: 25, operator: 'gte' },
          rewards: { tokens: 600, badge: 'charity_champion', title: 'Charity Champion' }
        },

        // Time-based Achievements
        {
          name: 'New Member',
          description: 'Be a member for 7 days',
          icon: '🎉',
          category: 'time',
          type: 'bronze',
          requirements: { condition: 'daysSinceJoined', value: 7, operator: 'gte' },
          rewards: { tokens: 30, badge: 'new_member' }
        },
        {
          name: 'Regular',
          description: 'Be a member for 30 days',
          icon: '📅',
          category: 'time',
          type: 'silver',
          requirements: { condition: 'daysSinceJoined', value: 30, operator: 'gte' },
          rewards: { tokens: 100, badge: 'regular', title: 'Regular Member' }
        },
        {
          name: 'Veteran',
          description: 'Be a member for 365 days',
          icon: '🎖️',
          category: 'time',
          type: 'gold',
          requirements: { condition: 'daysSinceJoined', value: 365, operator: 'gte' },
          rewards: { tokens: 500, badge: 'veteran', title: 'Veteran Member' }
        },

        // Special Achievements
        {
          name: 'Community Star',
          description: 'Receive 50 positive reviews',
          icon: '⭐',
          category: 'social',
          type: 'special',
          requirements: { condition: 'positiveReviews', value: 50, operator: 'gte' },
          rewards: { tokens: 300, badge: 'community_star', title: 'Community Star' }
        },
        {
          name: 'Trendsetter',
          description: 'Have 100 items liked by other users',
          icon: '💫',
          category: 'social',
          type: 'special',
          requirements: { condition: 'totalLikes', value: 100, operator: 'gte' },
          rewards: { tokens: 250, badge: 'trendsetter', title: 'Trendsetter' }
        }
      ];

      for (const achievementData of defaultAchievements) {
        await Achievement.findOneAndUpdate(
          { name: achievementData.name },
          achievementData,
          { upsert: true, new: true }
        );
      }

      logger.info('Default achievements initialized successfully');
    } catch (error) {
      logger.error('Error initializing default achievements:', error);
      throw error;
    }
  }

  /**
   * Check and award achievements for a user
   */
  async checkUserAchievements(userId: string): Promise<IUserAchievement[]> {
    try {
      const user = await User.findById(userId);
      if (!user) {
        throw new Error('User not found');
      }

      const achievements = await Achievement.find({ isActive: true });
      const newAchievements: IUserAchievement[] = [];

      for (const achievement of achievements) {
        // Check if user already has this achievement
        const existingUserAchievement = await UserAchievement.findOne({
          user: userId,
          achievement: achievement._id
        });

        if (existingUserAchievement?.isCompleted) {
          continue; // Skip if already completed
        }

        // Calculate current progress
        const currentValue = this.getUserValue(user, achievement.requirements.condition);
        const targetValue = achievement.requirements.value;
        const isCompleted = this.checkRequirement(currentValue, targetValue, achievement.requirements.operator);

        if (existingUserAchievement) {
          // Update existing progress
          existingUserAchievement.progress = {
            current: currentValue,
            target: targetValue,
            percentage: Math.min(100, Math.round((currentValue / targetValue) * 100))
          };

          if (isCompleted && !existingUserAchievement.isCompleted) {
            existingUserAchievement.isCompleted = true;
            existingUserAchievement.unlockedAt = new Date();
            existingUserAchievement.tokensAwarded = achievement.rewards.tokens;

            // Award tokens
            await tokenService.earnTokens({
              userId,
              action: 'achievement',
              referenceId: achievement._id.toString(),
              description: `Unlocked achievement: ${achievement.name}`,
              metadata: {
                achievementName: achievement.name,
                achievementType: achievement.type,
                category: achievement.category,
                tokensAwarded: achievement.rewards.tokens
              }
            });

            newAchievements.push(existingUserAchievement);
          }

          await existingUserAchievement.save();
        } else if (isCompleted) {
          // Create new completed achievement
          const userAchievement = new UserAchievement({
            user: userId,
            achievement: achievement._id,
            progress: {
              current: currentValue,
              target: targetValue,
              percentage: 100
            },
            isCompleted: true,
            tokensAwarded: achievement.rewards.tokens
          });

          await userAchievement.save();

          // Award tokens
          await tokenService.earnTokens({
            userId,
            action: 'achievement',
            referenceId: achievement._id.toString(),
            description: `Unlocked achievement: ${achievement.name}`,
            metadata: {
              achievementName: achievement.name,
              achievementType: achievement.type,
              category: achievement.category,
              tokensAwarded: achievement.rewards.tokens
            }
          });

          newAchievements.push(userAchievement);
        } else {
          // Create new in-progress achievement
          const userAchievement = new UserAchievement({
            user: userId,
            achievement: achievement._id,
            progress: {
              current: currentValue,
              target: targetValue,
              percentage: Math.min(100, Math.round((currentValue / targetValue) * 100))
            },
            isCompleted: false,
            tokensAwarded: 0
          });

          await userAchievement.save();
        }
      }

      return newAchievements;
    } catch (error) {
      logger.error('Error checking user achievements:', error);
      throw error;
    }
  }

  /**
   * Get user's achievements with progress
   */
  async getUserAchievements(userId: string): Promise<any[]> {
    try {
      const userAchievements = await UserAchievement.find({ user: userId })
        .populate('achievement')
        .sort({ unlockedAt: -1 });

      return userAchievements.map(ua => ({
        id: ua._id,
        achievement: ua.achievement,
        progress: ua.progress,
        isCompleted: ua.isCompleted,
        unlockedAt: ua.unlockedAt,
        tokensAwarded: ua.tokensAwarded
      }));
    } catch (error) {
      logger.error('Error getting user achievements:', error);
      throw error;
    }
  }

  /**
   * Get achievement leaderboard
   */
  async getAchievementLeaderboard(limit: number = 10): Promise<any[]> {
    try {
      const leaderboard = await UserAchievement.aggregate([
        { $match: { isCompleted: true } },
        {
          $group: {
            _id: '$user',
            totalAchievements: { $sum: 1 },
            totalTokensFromAchievements: { $sum: '$tokensAwarded' },
            latestAchievement: { $max: '$unlockedAt' }
          }
        },
        {
          $lookup: {
            from: 'users',
            localField: '_id',
            foreignField: '_id',
            as: 'user'
          }
        },
        { $unwind: '$user' },
        {
          $project: {
            _id: 1,
            totalAchievements: 1,
            totalTokensFromAchievements: 1,
            latestAchievement: 1,
            'user.firstName': 1,
            'user.lastName': 1,
            'user.profilePicture': 1,
            'user.sustainabilityScore': 1,
            'user.location.county': 1
          }
        },
        { $sort: { totalAchievements: -1, totalTokensFromAchievements: -1 } },
        { $limit: limit }
      ]);

      return leaderboard.map((entry, index) => ({
        rank: index + 1,
        userId: entry._id,
        name: `${entry.user.firstName} ${entry.user.lastName}`,
        profilePicture: entry.user.profilePicture,
        county: entry.user.location?.county || 'Unknown',
        totalAchievements: entry.totalAchievements,
        totalTokensFromAchievements: entry.totalTokensFromAchievements,
        sustainabilityScore: entry.user.sustainabilityScore,
        latestAchievement: entry.latestAchievement
      }));
    } catch (error) {
      logger.error('Error getting achievement leaderboard:', error);
      throw error;
    }
  }

  /**
   * Get user value for achievement condition
   */
  private getUserValue(user: any, condition: string): number {
    switch (condition) {
      case 'pediTokens':
        return user.pediTokens || 0;
      case 'totalListings':
        return user.totalListings || 0;
      case 'totalSwaps':
        return user.totalSwaps || 0;
      case 'totalDonations':
        return user.totalDonations || 0;
      case 'sustainabilityScore':
        return user.sustainabilityScore || 0;
      case 'daysSinceJoined':
        return Math.floor((Date.now() - user.createdAt.getTime()) / (1000 * 60 * 60 * 24));
      case 'positiveReviews':
        return user.rating?.count || 0;
      case 'totalLikes':
        return user.totalLikes || 0;
      default:
        return 0;
    }
  }

  /**
   * Check if requirement is met
   */
  private checkRequirement(current: number, target: number, operator: string): boolean {
    switch (operator) {
      case 'gte':
        return current >= target;
      case 'lte':
        return current <= target;
      case 'eq':
        return current === target;
      case 'gt':
        return current > target;
      case 'lt':
        return current < target;
      default:
        return false;
    }
  }
}

export const achievementService = new AchievementService();
