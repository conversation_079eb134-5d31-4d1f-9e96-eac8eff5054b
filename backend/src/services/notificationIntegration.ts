import { notificationService } from './notification';
import { logger } from '@/utils/logger';
import User from '@/models/User';
import { IPayment } from '@/models/Payment';

/**
 * Notification integration service for platform events
 * This service provides helper methods to send notifications for various platform events
 */
export class NotificationIntegrationService {
  
  /**
   * Send payment confirmation notification
   */
  async sendPaymentConfirmation(payment: IPayment): Promise<void> {
    try {
      const user = await User.findById(payment.user);
      if (!user) {
        logger.warn('User not found for payment notification', { paymentId: payment.paymentId });
        return;
      }

      let paymentType = 'payment';
      let description = payment.description;

      // Customize message based on payment type
      if (payment.relatedEntity) {
        switch (payment.relatedEntity.type) {
          case 'platform_fee':
            paymentType = 'platform fee';
            description = 'Platform transaction fee';
            break;
          case 'token_purchase':
            paymentType = 'token purchase';
            description = 'Pedi token top-up';
            break;
          case 'delivery_fee':
            paymentType = 'delivery fee';
            description = 'Item delivery fee';
            break;
          case 'premium_feature':
            paymentType = 'premium feature';
            description = 'Premium feature activation';
            break;
        }
      }

      await notificationService.createNotification({
        userId: user._id.toString(),
        category: 'payment',
        templateId: undefined, // We'll use direct message for now
        title: 'Payment Confirmed',
        message: `Your ${paymentType} payment of KES ${payment.amount.toLocaleString()} has been confirmed. Payment ID: ${payment.paymentId}. Thank you for using Pedi! 💚`,
        priority: 'high',
        relatedEntity: {
          type: 'payment',
          id: payment.paymentId,
        },
      });

      logger.info('Payment confirmation notification sent', {
        paymentId: payment.paymentId,
        userId: user._id,
        amount: payment.amount,
      });

    } catch (error: any) {
      logger.error('Failed to send payment confirmation notification:', error);
      // Don't throw to avoid affecting payment processing
    }
  }

  /**
   * Send payment failure notification
   */
  async sendPaymentFailure(payment: IPayment): Promise<void> {
    try {
      const user = await User.findById(payment.user);
      if (!user) {
        logger.warn('User not found for payment failure notification', { paymentId: payment.paymentId });
        return;
      }

      await notificationService.createNotification({
        userId: user._id.toString(),
        category: 'payment',
        title: 'Payment Failed',
        message: `Your payment of KES ${payment.amount.toLocaleString()} could not be processed. Reason: ${payment.failureReason || 'Unknown error'}. Please try again or contact support.`,
        priority: 'high',
        relatedEntity: {
          type: 'payment',
          id: payment.paymentId,
        },
      });

      logger.info('Payment failure notification sent', {
        paymentId: payment.paymentId,
        userId: user._id,
        reason: payment.failureReason,
      });

    } catch (error: any) {
      logger.error('Failed to send payment failure notification:', error);
    }
  }

  /**
   * Send token earned notification
   */
  async sendTokenEarned(userId: string, amount: number, reason: string, relatedEntityId?: string): Promise<void> {
    try {
      await notificationService.createNotification({
        userId,
        category: 'token',
        title: 'Tokens Earned!',
        message: `You've earned ${amount} tokens for ${reason}! Your sustainable actions are making a difference. 🌱`,
        priority: 'medium',
        relatedEntity: relatedEntityId ? {
          type: 'transaction',
          id: relatedEntityId,
        } : undefined,
      });

      logger.info('Token earned notification sent', {
        userId,
        amount,
        reason,
      });

    } catch (error: any) {
      logger.error('Failed to send token earned notification:', error);
    }
  }

  /**
   * Send exchange request notification
   */
  async sendExchangeRequest(
    recipientUserId: string, 
    requesterName: string, 
    itemName: string, 
    exchangeId: string
  ): Promise<void> {
    try {
      await notificationService.createNotification({
        userId: recipientUserId,
        category: 'exchange',
        title: 'New Exchange Request',
        message: `${requesterName} wants to exchange "${itemName}" with you. Check the app to respond.`,
        priority: 'medium',
        relatedEntity: {
          type: 'exchange',
          id: exchangeId,
        },
      });

      logger.info('Exchange request notification sent', {
        recipientUserId,
        requesterName,
        itemName,
        exchangeId,
      });

    } catch (error: any) {
      logger.error('Failed to send exchange request notification:', error);
    }
  }

  /**
   * Send exchange status update notification
   */
  async sendExchangeStatusUpdate(
    userId: string,
    status: 'accepted' | 'completed' | 'cancelled',
    itemName: string,
    partnerName: string,
    exchangeId: string
  ): Promise<void> {
    try {
      let title = '';
      let message = '';

      switch (status) {
        case 'accepted':
          title = 'Exchange Accepted';
          message = `Your exchange request for "${itemName}" has been accepted by ${partnerName}. Arrange pickup/delivery in the app.`;
          break;
        case 'completed':
          title = 'Exchange Completed';
          message = `Your exchange of "${itemName}" with ${partnerName} is complete! You've earned tokens for sustainable fashion. 🌍`;
          break;
        case 'cancelled':
          title = 'Exchange Cancelled';
          message = `Your exchange of "${itemName}" with ${partnerName} has been cancelled. Browse other items in the app.`;
          break;
      }

      await notificationService.createNotification({
        userId,
        category: 'exchange',
        title,
        message,
        priority: status === 'completed' ? 'medium' : 'low',
        relatedEntity: {
          type: 'exchange',
          id: exchangeId,
        },
      });

      logger.info('Exchange status notification sent', {
        userId,
        status,
        itemName,
        exchangeId,
      });

    } catch (error: any) {
      logger.error('Failed to send exchange status notification:', error);
    }
  }

  /**
   * Send welcome notification for new users
   */
  async sendWelcomeNotification(userId: string, firstName: string): Promise<void> {
    try {
      await notificationService.createNotification({
        userId,
        category: 'welcome',
        title: 'Welcome to Pedi!',
        message: `Hi ${firstName}! Welcome to Pedi, Kenya's sustainable fashion platform. Start by listing your first item or browse available exchanges. Let's make fashion sustainable together! 🌱👗`,
        priority: 'high',
      });

      logger.info('Welcome notification sent', {
        userId,
        firstName,
      });

    } catch (error: any) {
      logger.error('Failed to send welcome notification:', error);
    }
  }

  /**
   * Send reminder notification
   */
  async sendReminder(
    userId: string,
    type: 'pickup' | 'delivery' | 'payment' | 'verification',
    details: string,
    relatedEntityId?: string
  ): Promise<void> {
    try {
      await notificationService.createNotification({
        userId,
        category: 'reminder',
        title: `${type.charAt(0).toUpperCase() + type.slice(1)} Reminder`,
        message: `${details}. Don't forget to complete your ${type} to keep your sustainable fashion journey going! 📱`,
        priority: 'medium',
        relatedEntity: relatedEntityId ? {
          type: 'transaction',
          id: relatedEntityId,
        } : undefined,
      });

      logger.info('Reminder notification sent', {
        userId,
        type,
        details,
      });

    } catch (error: any) {
      logger.error('Failed to send reminder notification:', error);
    }
  }

  /**
   * Send system alert notification
   */
  async sendSystemAlert(
    userId: string,
    alertType: 'maintenance' | 'security' | 'update',
    message: string
  ): Promise<void> {
    try {
      const prefix = alertType === 'maintenance' ? '🔧' : alertType === 'security' ? '🔒' : '📱';
      
      await notificationService.createNotification({
        userId,
        category: 'system',
        title: `System ${alertType.charAt(0).toUpperCase() + alertType.slice(1)}`,
        message: `${prefix} ${message}`,
        priority: alertType === 'security' ? 'urgent' : 'high',
      });

      logger.info('System alert notification sent', {
        userId,
        alertType,
      });

    } catch (error: any) {
      logger.error('Failed to send system alert notification:', error);
    }
  }

  /**
   * Send bulk promotional notification
   */
  async sendPromotionalNotification(
    userIds: string[],
    title: string,
    description: string,
    actionText?: string
  ): Promise<{ sent: number; failed: number }> {
    try {
      let message = `${title} - ${description}`;
      if (actionText) {
        message += ` ${actionText}`;
      }
      message += ' Open the app to learn more! 🛍️';

      const result = await notificationService.createBulkNotifications({
        userIds,
        category: 'promotion',
        templateId: undefined, // Direct message
        priority: 'low',
      });

      logger.info('Promotional notifications sent', {
        totalUsers: userIds.length,
        sent: result.created,
        failed: result.failed,
      });

      return { sent: result.created, failed: result.failed };

    } catch (error: any) {
      logger.error('Failed to send promotional notifications:', error);
      return { sent: 0, failed: userIds.length };
    }
  }

  /**
   * Send verification reminder
   */
  async sendVerificationReminder(userId: string, verificationType: 'phone' | 'email' | 'identity'): Promise<void> {
    try {
      const typeText = verificationType === 'phone' ? 'phone number' : 
                      verificationType === 'email' ? 'email address' : 'identity';

      await notificationService.createNotification({
        userId,
        category: 'security',
        title: 'Verification Required',
        message: `Please verify your ${typeText} to secure your account and access all Pedi features. Verification helps protect your account and builds trust in our community. 🔒`,
        priority: 'high',
      });

      logger.info('Verification reminder sent', {
        userId,
        verificationType,
      });

    } catch (error: any) {
      logger.error('Failed to send verification reminder:', error);
    }
  }
}

// Export singleton instance
export const notificationIntegrationService = new NotificationIntegrationService();
