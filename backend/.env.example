# Database Configuration
MONGODB_URI=mongodb+srv://your-username:<EMAIL>/pedi-db?retryWrites=true&w=majority

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-here-make-it-long-and-random
JWT_EXPIRES_IN=7d

# Server Configuration
PORT=5000
NODE_ENV=development

# Frontend URL
FRONTEND_URL=http://localhost:3000

# M-Pesa Configuration (Safaricom Daraja API)
MPESA_CONSUMER_KEY=your-mpesa-consumer-key
MPESA_CONSUMER_SECRET=your-mpesa-consumer-secret
MPESA_SHORTCODE=your-business-shortcode
MPESA_PASSKEY=your-mpesa-passkey
MPESA_CALLBACK_URL=https://yourdomain.com/api/mpesa/callback
MPESA_ENVIRONMENT=sandbox

# Africa's Talking SMS Configuration
AFRICASTALKING_USERNAME=your-username
AFRICASTALKING_API_KEY=your-api-key

# Admin Credentials
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=PediAdmin2024!
ADMIN_PHONE=+254700000000
ADMIN_FIRST_NAME=Pedi
ADMIN_LAST_NAME=Administrator

# Super Admin Credentials (for system management)
SUPER_ADMIN_EMAIL=<EMAIL>
SUPER_ADMIN_PASSWORD=SuperPediAdmin2024!
SUPER_ADMIN_PHONE=+254700000001

# Email Configuration (Optional - for notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# File Upload Configuration
CLOUDINARY_CLOUD_NAME=your-cloudinary-cloud-name
CLOUDINARY_API_KEY=your-cloudinary-api-key
CLOUDINARY_API_SECRET=your-cloudinary-api-secret

# Token Pricing Configuration
TOKEN_PRICE_KES=5
TOKEN_PRICE_USD=0.05
DONATION_REWARD_RATE=10
RECYCLING_REWARD_RATE=8

# Minimum donation/recycling weights (in kg)
MIN_DONATION_WEIGHT=0.5
MAX_DONATION_WEIGHT=100
MIN_RECYCLING_WEIGHT=1.0
MAX_RECYCLING_WEIGHT=100

# Reward Thresholds (in kg)
BRONZE_THRESHOLD=5
SILVER_THRESHOLD=25
GOLD_THRESHOLD=100
PLATINUM_THRESHOLD=500

# Security Configuration
BCRYPT_ROUNDS=12
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Session Configuration
SESSION_SECRET=your-session-secret-key-here
SESSION_EXPIRES_IN=24h

# API Keys for External Services
GOOGLE_MAPS_API_KEY=your-google-maps-api-key
WEATHER_API_KEY=your-weather-api-key

# Webhook Secrets
MPESA_WEBHOOK_SECRET=your-mpesa-webhook-secret
STRIPE_WEBHOOK_SECRET=your-stripe-webhook-secret

# Feature Flags
ENABLE_AI_ASSISTANT=true
ENABLE_BULK_DONATIONS=true
ENABLE_TOKEN_PURCHASES=true
ENABLE_RECYCLING=true
ENABLE_CHARITY_PARTNERSHIPS=true

# Logging Configuration
LOG_LEVEL=info
LOG_FILE_PATH=./logs/app.log

# Cache Configuration (Redis - Optional)
REDIS_URL=redis://localhost:6379
CACHE_TTL=3600

# Backup Configuration
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30

# Monitoring Configuration
SENTRY_DSN=your-sentry-dsn-here
ANALYTICS_ID=your-analytics-id

# Social Media Integration (Optional)
FACEBOOK_APP_ID=your-facebook-app-id
TWITTER_API_KEY=your-twitter-api-key
INSTAGRAM_ACCESS_TOKEN=your-instagram-token

# Partnership API Keys
CHARITY_PARTNER_API_KEY=your-charity-api-key
RECYCLING_CENTER_API_KEY=your-recycling-api-key

# Notification Settings
ENABLE_SMS_NOTIFICATIONS=true
ENABLE_EMAIL_NOTIFICATIONS=true
ENABLE_PUSH_NOTIFICATIONS=false

# Development Settings
DEBUG_MODE=true
MOCK_PAYMENTS=true
MOCK_SMS=true
