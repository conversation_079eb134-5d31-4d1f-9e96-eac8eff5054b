# Pedi: The Future of Fashion is Circular

A sustainable clothing exchange platform for Kenya, enabling clothing swaps, token-based exchanges, and donations/recycling to reduce textile waste and promote circular fashion.

## 🌍 Project Overview

**Pedi** (African Clothing Exchange) is a Web2 platform that addresses Kenya's textile waste problem by creating a circular fashion economy. Users can swap clothes, earn tokens through sustainable actions, and participate in donation programs with partner organizations.

### Key Problems We Solve
- **Textile Waste Reduction**: Divert clothing from landfills through reuse and recycling
- **Fast Fashion Impact**: Provide sustainable alternatives to fast fashion consumption
- **Affordable Access**: Make quality clothing accessible through swaps and token economy
- **Community Building**: Connect eco-conscious consumers and fashion enthusiasts

## 🚀 Core Features

### Exchange Modes
- **Direct Swaps**: Trade clothing items directly with other users
- **Token Purchases**: Buy items using earned Pedi tokens
- **Donations**: Contribute to charity partners and recycling programs

### Platform Features
- 📱 SMS OTP authentication for Kenya's mobile-first market
- 💬 Real-time chat system for negotiations
- 🤖 AI fashion assistant powered by Google Gemini
- 💰 M-Pesa integration for platform fees
- 📲 SMS notifications via Africa's Talking
- 🏆 Token rewards for sustainable actions

## 🛠 Tech Stack

### Frontend
- **Next.js** with TypeScript
- **Tailwind CSS** for responsive design
- **WebSocket** for real-time features

### Backend
- **Node.js/Express.js** RESTful API
- **MongoDB Atlas** with Mongoose ODM
- **JWT** authentication with SMS OTP

### Integrations
- **M-Pesa Daraja API** for payments
- **Africa's Talking** for SMS/USSD
- **Google Gemini API** for AI assistant
- **Cloud Storage** for image management

## 🎯 Target Audience

- **Primary**: Eco-conscious youth (18-35)
- **Secondary**: Budget-conscious consumers
- **Partners**: Charitable organizations, recycling companies
- **Corporate**: Sustainability programs

## 📁 Project Structure

```
pedi/
├── frontend/          # Next.js application
├── backend/           # Express.js API server
├── shared/            # Shared types and utilities
├── docs/              # Documentation
└── scripts/           # Development and deployment scripts
```

## 🚦 Development Phases

- [x] **Phase 0**: Project Setup & Core Infrastructure
- [ ] **Phase 1**: User Authentication & Profile Management
- [ ] **Phase 2**: Core Clothing Listing System
- [ ] **Phase 3**: Pedi Token System
- [ ] **Phase 4**: Exchange & Transaction System
- [ ] **Phase 5**: Real-time Communication
- [ ] **Phase 6**: Payment Integration (M-Pesa)
- [ ] **Phase 7**: Notification System
- [ ] **Phase 8**: Charity & Donation Flow
- [ ] **Phase 9**: AI Fashion Assistant
- [ ] **Phase 10**: Delivery Coordination
- [ ] **Phase 11**: Testing, Optimization & Deployment

## 💡 Revenue Model

- **Buyer Protection Fees**: Small percentage on token transactions
- **Service Fees**: Platform fees for premium features
- **Delivery Partnerships**: Revenue sharing with delivery services

## 🌱 Sustainability Impact

- **Waste Reduction**: Extend clothing lifecycle through reuse
- **Carbon Footprint**: Reduce manufacturing demand
- **Community Education**: Promote sustainable fashion practices
- **Local Economy**: Support local fashion designers and businesses

## 🚀 Getting Started

### Prerequisites
- Node.js 18+
- MongoDB Atlas account
- M-Pesa Developer account
- Africa's Talking account
- Google Cloud account (for Gemini API)

### Installation

```bash
# Clone the repository
git clone https://github.com/Ivy-NW/pedi.git
cd pedi

# Install dependencies
npm install

# Setup environment variables
cp .env.example .env.local

# Start development servers
npm run dev
```

## 📖 Documentation

- [API Documentation](./docs/api.md)
- [Database Schema](./docs/database.md)
- [Deployment Guide](./docs/deployment.md)
- [Contributing Guidelines](./docs/contributing.md)

## 🤝 Contributing

We welcome contributions! Please read our [Contributing Guidelines](./docs/contributing.md) for details on our code of conduct and the process for submitting pull requests.

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- Kenya's sustainable fashion community
- Open source contributors
- Partner organizations promoting circular economy

---

**Built with ❤️ for a sustainable future in Kenya** 🇰🇪