# Pedi Platform - Complete Setup Guide

## 🚀 Enhanced Features Implementation

This guide covers the implementation of the enhanced dashboard features, bulk donation system, token economy, and admin functionality.

## ✨ New Features Added

### 1. Dashboard Sidebars
- **UserSidebar**: Clean navigation for general users
- **AdminSidebar**: Comprehensive admin navigation
- **DashboardLayout**: Shared layout wrapper

### 2. Enhanced Clothing Listings
- Support for "both" exchange type (swap + token)
- Improved pricing and condition handling
- Better user experience with clear options

### 3. Bulk Donation System
- Weight-based donations (0.1kg - 100kg)
- Charity partner integration
- Recycling center partnerships
- Token rewards based on weight and condition
- Environmental impact tracking

### 4. Token Economy
- Token purchase with M-Pesa and card payments
- Earning system through donations and recycling
- Complete transaction history
- Leaderboards and gamification

### 5. Admin Dashboard
- Comprehensive platform statistics
- User and partner management
- Real-time activity monitoring
- System health checks

## 🛠️ Setup Instructions

### 1. Environment Configuration

Create `backend/.env` with admin credentials:

```env
# Admin Credentials
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=PediAdmin2024!
ADMIN_PHONE=+254700000000
ADMIN_FIRST_NAME=Pedi
ADMIN_LAST_NAME=Administrator

# Super Admin (Optional)
SUPER_ADMIN_EMAIL=<EMAIL>
SUPER_ADMIN_PASSWORD=SuperPediAdmin2024!
SUPER_ADMIN_PHONE=+254700000001

# Token Configuration
TOKEN_PRICE_KES=5
TOKEN_PRICE_USD=0.05
DONATION_REWARD_RATE=10
RECYCLING_REWARD_RATE=8

# Donation Limits
MIN_DONATION_WEIGHT=0.5
MAX_DONATION_WEIGHT=100
MIN_RECYCLING_WEIGHT=1.0
MAX_RECYCLING_WEIGHT=100
```

### 2. Database Setup

Run the setup scripts:

```bash
cd backend

# Create admin users
node scripts/createAdmin.js

# Seed charity partners and recycling centers
node scripts/seedPartners.js
```

### 3. Start the Application

```bash
# Backend
cd backend
npm run dev

# Frontend
cd frontend
pnpm dev
```

## 📱 User Features

### Dashboard Navigation
- **Dashboard**: Overview with available items
- **Browse Items**: Find clothing to swap
- **List Item**: Add new clothing listings
- **My Listings**: Manage your items
- **Messages**: Chat with other users
- **Donations**: Bulk charity donations
- **Recycling**: Environmental recycling
- **Tokens**: Purchase and manage tokens
- **AI Assistant**: Fashion advice
- **Notifications**: Platform updates

### Bulk Donations
1. **Choose Type**: Charity or Recycling
2. **Enter Weight**: 0.1kg to 100kg
3. **Select Condition**: Excellent, Good, Fair, Poor
4. **Choose Partner**: From verified list
5. **Delivery Method**: Pickup or Drop-off
6. **Earn Tokens**: Based on weight × rate × condition

### Token System
- **Purchase**: Buy with M-Pesa or card
- **Earn**: Through donations and recycling
- **Spend**: On clothing exchanges
- **Track**: Complete transaction history

## 🔧 Admin Features

### Admin Login
Use the admin credentials from your `.env` file:
- **Email**: <EMAIL>
- **Password**: PediAdmin2024!

### Admin Dashboard
- **User Statistics**: Total, active, new users
- **Platform Metrics**: Listings, donations, tokens
- **Partner Management**: Charity and recycling centers
- **System Health**: Monitor platform status

### Partner Management
- **Add Partners**: Create charity/recycling partners
- **Verify Partners**: Approve new partners
- **Monitor Activity**: Track partner performance

## 🎨 UI/UX Improvements

### Design System
- **Colors**: 
  - White (#FFFFFF) - Base
  - Dark Green (#032221) - Primary
  - Pine Green (#01796F) - Secondary
- **Typography**: Axiforma font family
- **Components**: Modern, accessible UI

### User Experience
- **Sidebar Navigation**: Organized, intuitive
- **Visual Feedback**: Loading states, animations
- **Responsive Design**: Mobile-first approach
- **Accessibility**: WCAG compliant

## 🌍 Environmental Impact

### Tracking Features
- **CO₂ Savings**: 3kg CO₂ per kg recycled
- **Water Conservation**: 2700L per kg recycled
- **Weight Tracking**: Total materials processed
- **Impact Visualization**: Charts and progress

### Sustainability Scoring
- **User Rankings**: Bronze, Silver, Gold, Platinum
- **Eco Badges**: Environmental achievements
- **Community Goals**: Platform-wide targets

## 💰 Token Economy

### Earning Opportunities
- **Charity Donations**: 10-15 tokens per kg
- **Recycling**: 7-10 tokens per kg
- **Quality Listings**: 5-20 bonus tokens
- **Referrals**: 50 tokens per friend

### Purchase Options
- **Starter**: 100 tokens - KES 500
- **Popular**: 250 tokens + 25 bonus - KES 1,200
- **Value**: 500 tokens + 75 bonus - KES 2,300
- **Premium**: 1000 tokens + 200 bonus - KES 4,500
- **Custom**: Any amount (5 KES per token)

## 🔐 Security Features

### Authentication
- **JWT Tokens**: Secure user sessions
- **Role-based Access**: User/Admin permissions
- **Phone Verification**: SMS OTP

### Data Protection
- **Input Validation**: Comprehensive checks
- **Rate Limiting**: API abuse prevention
- **Secure Headers**: Helmet.js protection

## 📊 Analytics & Monitoring

### User Analytics
- **Activity Tracking**: User engagement metrics
- **Conversion Rates**: Token purchase analytics
- **Retention Metrics**: User lifecycle analysis

### Platform Health
- **System Alerts**: Automated monitoring
- **Performance Metrics**: Response times
- **Error Tracking**: Comprehensive logging

## 🚀 Deployment Checklist

### Pre-deployment
- [ ] Environment variables configured
- [ ] Admin users created
- [ ] Partners seeded
- [ ] Database indexes created
- [ ] SSL certificates configured

### Production Setup
- [ ] MongoDB Atlas production cluster
- [ ] M-Pesa production credentials
- [ ] SMS service configuration
- [ ] CDN for image storage
- [ ] Monitoring tools setup

## 🆘 Troubleshooting

### Common Issues
1. **Admin Login Failed**: Check environment variables
2. **Token Purchase Failed**: Verify M-Pesa configuration
3. **Sidebar Not Loading**: Check user authentication
4. **Partners Not Showing**: Run seed script

### Support
- **Documentation**: Check `/docs` folder
- **Issues**: Create GitHub issue
- **Email**: <EMAIL>

## 🎯 Next Steps

### Recommended Enhancements
1. **Mobile App**: React Native implementation
2. **Advanced Analytics**: Business intelligence
3. **AI Recommendations**: Machine learning
4. **Social Features**: User communities
5. **Marketplace**: Extended trading features

---

**Ready to revolutionize sustainable fashion in Kenya! 🌍♻️**
