{"name": "pedi-shared", "version": "1.0.0", "description": "Shared types and utilities for Pedi platform", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "rm -rf dist", "test": "jest"}, "devDependencies": {"typescript": "^5.3.2", "@types/node": "^20.10.0", "jest": "^29.7.0", "ts-jest": "^29.1.1"}, "keywords": ["typescript", "shared", "types", "utilities"], "author": "Pedi Team", "license": "MIT"}