// Common types shared between frontend and backend

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
  pagination?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export interface PaginationParams {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface LocationData {
  county: string;
  town: string;
  coordinates?: {
    latitude: number;
    longitude: number;
  };
}

export interface UserProfile {
  id: string;
  phoneNumber: string;
  email?: string;
  firstName: string;
  lastName: string;
  fullName: string;
  profilePicture?: string;
  location: LocationData;
  isVerified: boolean;
  role: 'user' | 'admin' | 'moderator';
  pediTokens: number;
  rating: {
    average: number;
    count: number;
  };
  sustainabilityScore: number;
  totalDonations: number;
  totalSwaps: number;
  joinedAt: string;
  lastActive: string;
}

export interface ClothingItemData {
  id: string;
  owner: string | UserProfile;
  title: string;
  description: string;
  category: string;
  subcategory?: string;
  brand?: string;
  size: string;
  color: string;
  condition: 'New' | 'Like New' | 'Good' | 'Fair' | 'Poor';
  images: string[];
  originalPrice?: number;
  tokenPrice?: number;
  isAvailable: boolean;
  exchangeType: 'swap' | 'token' | 'donation' | 'all';
  preferredSwapCategories?: string[];
  location: LocationData;
  tags: string[];
  views: number;
  likeCount: number;
  likes: string[];
  sustainabilityInfo: {
    material: string;
    careInstructions: string;
    estimatedLifespan: string;
    recyclingInfo?: string;
  };
  qualityAssurance?: {
    isVerified: boolean;
    verifiedBy?: string;
    verificationDate?: string;
    notes?: string;
  };
  ageInDays: number;
  createdAt: string;
  updatedAt: string;
}

export interface TransactionData {
  id: string;
  type: 'swap' | 'token_purchase' | 'donation';
  status: 'pending' | 'accepted' | 'completed' | 'cancelled' | 'disputed';
  initiator: string | UserProfile;
  recipient?: string | UserProfile;
  initiatorItems: string[] | ClothingItemData[];
  recipientItems?: string[] | ClothingItemData[];
  tokenAmount?: number;
  deliveryMethod: 'pickup' | 'delivery' | 'meetup';
  deliveryAddress?: {
    county: string;
    town: string;
    specificLocation: string;
    contactPhone: string;
  };
  meetupLocation?: {
    name: string;
    address: string;
    coordinates?: {
      latitude: number;
      longitude: number;
    };
    scheduledTime: string;
  };
  platformFee?: {
    amount: number;
    currency: string;
    mpesaTransactionId?: string;
    status: 'pending' | 'completed' | 'failed';
  };
  charityPartner?: {
    name: string;
    id: string;
    category: string;
  };
  chatId?: string;
  timeline: {
    status: string;
    timestamp: string;
    note?: string;
    updatedBy: string;
  }[];
  ratings?: {
    initiatorRating?: {
      score: number;
      comment?: string;
      ratedAt: string;
    };
    recipientRating?: {
      score: number;
      comment?: string;
      ratedAt: string;
    };
  };
  dispute?: {
    reason: string;
    description: string;
    reportedBy: string;
    reportedAt: string;
    status: 'open' | 'investigating' | 'resolved' | 'closed';
    resolution?: string;
    resolvedBy?: string;
    resolvedAt?: string;
  };
  duration?: number;
  createdAt: string;
  updatedAt: string;
}

export interface TokenTransactionData {
  id: string;
  user: string | UserProfile;
  type: 'earn' | 'spend' | 'bonus' | 'refund' | 'penalty';
  amount: number;
  balance: number;
  source: {
    type: 'listing' | 'donation' | 'swap' | 'purchase' | 'referral' | 'welcome' | 'daily_login' | 'review' | 'admin';
    referenceId?: string;
    description: string;
  };
  metadata?: Record<string, any>;
  createdAt: string;
}

export interface ChatData {
  id: string;
  participants: string[] | UserProfile[];
  relatedTransaction?: string | TransactionData;
  relatedClothingItem?: string | ClothingItemData;
  messages: MessageData[];
  lastMessage?: {
    content: string;
    sender: string;
    timestamp: string;
  };
  isActive: boolean;
  unreadCount: number;
  createdAt: string;
  updatedAt: string;
}

export interface MessageData {
  sender: string | UserProfile;
  content: string;
  type: 'text' | 'image' | 'system';
  timestamp: string;
  isRead: boolean;
}

export interface CharityPartnerData {
  id: string;
  name: string;
  description: string;
  category: string;
  contactInfo: {
    email: string;
    phone: string;
    website?: string;
    address: LocationData & { specificLocation: string };
  };
  logo?: string;
  isActive: boolean;
  acceptedItemTypes: string[];
  requirements: {
    condition: string[];
    categories: string[];
    notes?: string;
  };
  stats: {
    totalDonationsReceived: number;
    totalItemsReceived: number;
    totalBeneficiaries: number;
  };
  verificationStatus: 'pending' | 'verified' | 'suspended';
  verifiedBy?: string;
  verificationDate?: string;
  createdAt: string;
  updatedAt: string;
}
